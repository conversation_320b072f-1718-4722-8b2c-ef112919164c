#!/bin/bash

# Polaris 鉴权日志分析脚本
# 该脚本用于分析 Polaris 服务注销鉴权的详细日志

set -e

# 配置变量
NAMESPACE="mse-zdu1rgey"
POD_NAME="registry-server-0"
LOG_FILE="auth-debug-$(date +%Y%m%d-%H%M%S).log"

echo "========================================="
echo "Polaris 鉴权日志分析工具"
echo "命名空间: ${NAMESPACE}"
echo "Pod 名称: ${POD_NAME}"
echo "========================================="

# 检查 kubectl 是否可用
if ! command -v kubectl &> /dev/null; then
    echo "错误: kubectl 命令未找到，请确保已安装 kubectl"
    exit 1
fi

# 检查 Pod 是否存在
if ! kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" &> /dev/null; then
    echo "错误: Pod ${POD_NAME} 在命名空间 ${NAMESPACE} 中不存在"
    echo "可用的 Pod:"
    kubectl get pods -n "${NAMESPACE}"
    exit 1
fi

echo "开始收集日志..."

# 收集最近的日志
kubectl logs "${POD_NAME}" -n "${NAMESPACE}" --tail=1000 > "${LOG_FILE}"

echo "日志已保存到: ${LOG_FILE}"
echo ""

# 分析鉴权相关的日志
echo "========================================="
echo "鉴权相关日志分析"
echo "========================================="

echo ""
echo "1. 客户端鉴权日志:"
echo "-------------------"
grep -n "\[Auth\]\[Client\]" "${LOG_FILE}" || echo "未找到客户端鉴权日志"

echo ""
echo "2. 权限检查日志:"
echo "-------------------"
grep -n "\[Auth\]\[CheckClientPermission\]" "${LOG_FILE}" || echo "未找到客户端权限检查日志"
grep -n "\[Auth\]\[CheckPermission\]" "${LOG_FILE}" || echo "未找到权限检查日志"

echo ""
echo "3. NacosServer 处理日志:"
echo "------------------------"
grep -n "\[NacosServer\]\[handleDeregister\]" "${LOG_FILE}" || echo "未找到 NacosServer 处理日志"

echo ""
echo "4. 服务注销相关日志:"
echo "-------------------"
grep -n "DeregisterInstance" "${LOG_FILE}" || echo "未找到服务注销相关日志"

echo ""
echo "5. Token 相关日志:"
echo "-----------------"
grep -n "token" "${LOG_FILE}" | head -10 || echo "未找到 Token 相关日志"

echo ""
echo "6. 错误日志:"
echo "-----------"
grep -n -i "error\|failed\|denied\|forbidden" "${LOG_FILE}" | head -10 || echo "未找到错误日志"

echo ""
echo "========================================="
echo "日志分析完成"
echo "========================================="
echo ""
echo "完整日志文件: ${LOG_FILE}"
echo ""
echo "如需实时监控日志，请运行:"
echo "kubectl logs -f ${POD_NAME} -n ${NAMESPACE}"
echo ""
echo "如需过滤鉴权相关日志，请运行:"
echo "kubectl logs -f ${POD_NAME} -n ${NAMESPACE} | grep -E '\[Auth\]|\[NacosServer\]|DeregisterInstance'"
