package registercenter

import (
	"fmt"

	"github.com/golang/protobuf/proto"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/msesdk"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var _ Interface = &Proxy{}

type Proxy struct {
	Domain  string
	Port    string
	Release string
	Owner   string
}

func (p *Proxy) UpdateRegisterInstance(ctx context.CsmContext, instanceId, endpointId, esgId string) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) DeleteRegisterInstanceByInstanceId(ctx context.CsmContext, id string) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetRegisterInstances(ctx context.CsmContext, query *meta.QueryRegisterInstance) (*meta.PageResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetRegisterInstancesByInstanceId(ctx context.CsmContext, instanceId string) (*meta.RegisterInsDetailRep, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetServiceList(ctx context.CsmContext,
	req *registercentersdk.RegisterCenterServiceListRequest) (*registercentersdk.RegisterCenterServiceListResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) NewRegisterInstance(ctx context.CsmContext, instance *meta.RegisterInstance, monitorToken, esgId string) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) CreateServiceInstance(ctx context.CsmContext, req *registercentersdk.CreateServiceInstanceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) UpdateServiceInstance(ctx context.CsmContext, req *registercentersdk.CreateServiceInstanceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) DeleteServiceInstance(ctx context.CsmContext, req *registercentersdk.DeleteServiceInstanceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetServiceInstanceList(ctx context.CsmContext,
	req *registercentersdk.ServiceInstanceListRequest) (*registercentersdk.ServiceInstanceListResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) UpdateRegisterInstanceArgs(ctx context.CsmContext, instanceId string, args map[string]interface{}) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetRegisterInstanceArgs(ctx context.CsmContext, instanceId string) (map[string]interface{}, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) BatchDeleteServiceInstance(ctx context.CsmContext, req []*registercentersdk.BatchRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) BatchIsolateServiceInstance(ctx context.CsmContext, req []*registercentersdk.BatchRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetNamespaceList(ctx context.CsmContext, req *registercentersdk.RegisterCenterNamespaceListRequest) (*apiservice.BatchQueryResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) CreateNamespaces(ctx context.CsmContext, req []registercentersdk.CreateNamespaceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) UpdateNamespaces(ctx context.CsmContext, req []registercentersdk.CreateNamespaceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) DeleteNamespaces(ctx context.CsmContext, req []registercentersdk.CreateNamespaceRequest) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetInstanceAccountId(ctx context.CsmContext, instanceId string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetMseClient(ctx context.CsmContext, token string) (*msesdk.Client, error) {
	endpoint := fmt.Sprintf("%s:%s", p.Domain, p.Port)
	client := msesdk.NewClient(endpoint, token, p.Release)
	return client, nil
}

func (p *Proxy) MseCommonRequest(ctx context.CsmContext, req msesdk.Request, resp proto.Message) error {
	token := ctx.Request().Header.Get("X-MSE-Token")
	client, err := p.GetMseClient(ctx, token)
	if err != nil {
		return err
	}
	decorator := msesdk.NewClientDecorator(client, req)

	if _, err = decorator.DoRequest(ctx, resp); err != nil {
		return err
	}
	return nil
}

func (p *Proxy) PostActivate(ctx context.CsmContext) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) GetActivate(ctx context.CsmContext) error {
	//TODO implement me
	panic("implement me")
}

func (p *Proxy) UpdatePublicAccessConfig(ctx context.CsmContext, instanceId string, req *UpdatePublicAccessRequest) error {
	//TODO implement me
	panic("implement me")
}
