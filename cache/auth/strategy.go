/**
 * <PERSON><PERSON> is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package auth

import (
	"fmt"
	"math"
	"strings"
	"time"

	apisecurity "github.com/polarismesh/specification/source/go/api/v1/security"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"

	types "github.com/polarismesh/polaris/cache/api"
	"github.com/polarismesh/polaris/common/model"
	"github.com/polarismesh/polaris/common/utils"
	"github.com/polarismesh/polaris/store"
)

const (
	removePrincipalChSize = 8
)

// strategyCache
type strategyCache struct {
	*types.BaseCache

	storage          store.Store
	strategys        *utils.SyncMap[string, *model.StrategyDetailCache]
	uid2Strategy     *utils.SyncMap[string, *utils.SyncSet[string]]
	groupid2Strategy *utils.SyncMap[string, *utils.SyncSet[string]]

	namespace2Strategy   *utils.SyncMap[string, *utils.SyncSet[string]]
	service2Strategy     *utils.SyncMap[string, *utils.SyncSet[string]]
	configGroup2Strategy *utils.SyncMap[string, *utils.SyncSet[string]]

	lastMtime    int64
	userCache    *userCache
	singleFlight *singleflight.Group
}

// NewStrategyCache
func NewStrategyCache(storage store.Store, cacheMgr types.CacheManager) types.StrategyCache {
	return &strategyCache{
		BaseCache: types.NewBaseCache(storage, cacheMgr),
		storage:   storage,
	}
}

func (sc *strategyCache) Initialize(c map[string]interface{}) error {
	sc.userCache = sc.BaseCache.CacheMgr.GetCacher(types.CacheUser).(*userCache)
	sc.strategys = utils.NewSyncMap[string, *model.StrategyDetailCache]()
	sc.uid2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.groupid2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.namespace2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.service2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.configGroup2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.singleFlight = new(singleflight.Group)
	sc.lastMtime = 0
	return nil
}

func (sc *strategyCache) Update() error {
	// 多个线程竞争，只有一个线程进行更新
	_, err, _ := sc.singleFlight.Do(sc.Name(), func() (interface{}, error) {
		return nil, sc.DoCacheUpdate(sc.Name(), sc.realUpdate)
	})
	return err
}

func (sc *strategyCache) ForceSync() error {
	return sc.Update()
}

func (sc *strategyCache) realUpdate() (map[string]time.Time, int64, error) {
	// 获取几秒前的全部数据
	var (
		start           = time.Now()
		lastTime        = sc.LastFetchTime()
		strategies, err = sc.storage.GetStrategyDetailsForCache(lastTime, sc.IsFirstUpdate())
	)
	if err != nil {
		log.Errorf("[Cache][AuthStrategy] refresh auth strategy cache err: %s", err.Error())
		return nil, -1, err
	}

	lastMtimes, add, update, del := sc.setStrategys(strategies)
	timeDiff := time.Since(start)
	if timeDiff > time.Second {
		log.Info("[Cache][AuthStrategy] get more auth strategy",
			zap.Int("add", add), zap.Int("update", update), zap.Int("delete", del),
			zap.Time("last", lastTime), zap.Duration("used", time.Since(start)))
	}
	return lastMtimes, int64(len(strategies)), nil
}

// setStrategys 处理策略的数据更新情况
// step 1. 先处理resource以及principal的数据更新情况（主要是为了能够获取到新老数据进行对比计算）
// step 2. 处理真正的 strategy 的缓存更新
func (sc *strategyCache) setStrategys(strategies []*model.StrategyDetail) (map[string]time.Time, int, int, int) {
	var add, remove, update int

	sc.handlerResourceStrategy(strategies)
	sc.handlerPrincipalStrategy(strategies)

	lastMtime := sc.LastMtime(sc.Name()).Unix()

	for index := range strategies {
		rule := strategies[index]
		if !rule.Valid {
			sc.strategys.Delete(rule.ID)
			remove++
		} else {
			_, ok := sc.strategys.Load(rule.ID)
			if !ok {
				add++
			} else {
				update++
			}
			sc.strategys.Store(rule.ID, buildEnchanceStrategyDetail(rule))
		}

		lastMtime = int64(math.Max(float64(lastMtime), float64(rule.ModifyTime.Unix())))
	}

	return map[string]time.Time{sc.Name(): time.Unix(lastMtime, 0)}, add, update, remove
}

func buildEnchanceStrategyDetail(strategy *model.StrategyDetail) *model.StrategyDetailCache {
	users := make(map[string]model.Principal, 0)
	groups := make(map[string]model.Principal, 0)

	for index := range strategy.Principals {
		principal := strategy.Principals[index]
		if principal.PrincipalRole == model.PrincipalUser {
			users[principal.PrincipalID] = principal
		} else {
			groups[principal.PrincipalID] = principal
		}
	}

	return &model.StrategyDetailCache{
		StrategyDetail: strategy,
		UserPrincipal:  users,
		GroupPrincipal: groups,
	}
}

func (sc *strategyCache) writeSet(linkContainers *utils.SyncMap[string, *utils.SyncSet[string]], key, val string, isDel bool) {
	if isDel {
		values, ok := linkContainers.Load(key)
		if ok {
			values.Remove(val)
		}
	} else {
		if _, ok := linkContainers.Load(key); !ok {
			linkContainers.Store(key, utils.NewSyncSet[string]())
		}
		values, _ := linkContainers.Load(key)
		values.Add(val)
	}
}

// handlerResourceStrategy 处理资源视角下策略的缓存
// 根据新老策略的资源列表比对，计算出哪些资源不在和该策略存在关联关系，哪些资源新增了相关的策略
func (sc *strategyCache) handlerResourceStrategy(strategies []*model.StrategyDetail) {
	operateLink := func(resType int32, resId, strategyId string, remove bool) {
		switch resType {
		case int32(apisecurity.ResourceType_Namespaces):
			sc.writeSet(sc.namespace2Strategy, resId, strategyId, remove)
		case int32(apisecurity.ResourceType_Services):
			sc.writeSet(sc.service2Strategy, resId, strategyId, remove)
		case int32(apisecurity.ResourceType_ConfigGroups):
			sc.writeSet(sc.configGroup2Strategy, resId, strategyId, remove)
		}
	}

	for sIndex := range strategies {
		rule := strategies[sIndex]
		addRes := rule.Resources

		if oldRule, exist := sc.strategys.Load(rule.ID); exist {
			delRes := make([]model.StrategyResource, 0, 8)
			// 计算前后对比， resource 的变化
			newRes := make(map[string]struct{}, len(addRes))
			for i := range addRes {
				newRes[fmt.Sprintf("%d_%s", addRes[i].ResType, addRes[i].ResID)] = struct{}{}
			}

			// 筛选出从策略中被踢出的 resource 列表
			for i := range oldRule.Resources {
				item := oldRule.Resources[i]
				if _, ok := newRes[fmt.Sprintf("%d_%s", item.ResType, item.ResID)]; !ok {
					delRes = append(delRes, item)
				}
			}

			// 针对被剔除的 resource 列表，清理掉所关联的鉴权策略信息
			for rIndex := range delRes {
				resource := delRes[rIndex]
				operateLink(resource.ResType, resource.ResID, rule.ID, true)
			}
		}

		for rIndex := range addRes {
			resource := addRes[rIndex]
			if rule.Valid {
				operateLink(resource.ResType, resource.ResID, rule.ID, false)
			} else {
				operateLink(resource.ResType, resource.ResID, rule.ID, true)
			}
		}
	}
}

// handlerPrincipalStrategy
func (sc *strategyCache) handlerPrincipalStrategy(strategies []*model.StrategyDetail) {
	for index := range strategies {
		rule := strategies[index]
		// 计算 uid -> auth rule
		principals := rule.Principals

		if oldRule, exist := sc.strategys.Load(rule.ID); exist {
			delMembers := make([]model.Principal, 0, 8)
			// 计算前后对比， principal 的变化
			newRes := make(map[string]struct{}, len(principals))
			for i := range principals {
				newRes[fmt.Sprintf("%d_%s", principals[i].PrincipalRole, principals[i].PrincipalID)] = struct{}{}
			}

			// 筛选出从策略中被踢出的 principal 列表
			for i := range oldRule.Principals {
				item := oldRule.Principals[i]
				if _, ok := newRes[fmt.Sprintf("%d_%s", item.PrincipalRole, item.PrincipalID)]; !ok {
					delMembers = append(delMembers, item)
				}
			}

			// 针对被剔除的 principal 列表，清理掉所关联的鉴权策略信息
			for rIndex := range delMembers {
				principal := delMembers[rIndex]
				sc.removePrincipalLink(principal, rule)
			}
		}
		if rule.Valid {
			for pos := range principals {
				principal := principals[pos]
				sc.addPrincipalLink(principal, rule)
			}
		} else {
			for pos := range principals {
				principal := principals[pos]
				sc.removePrincipalLink(principal, rule)
			}
		}
	}
}

func (sc *strategyCache) removePrincipalLink(principal model.Principal, rule *model.StrategyDetail) {
	linkContainers := sc.uid2Strategy
	if principal.PrincipalRole != model.PrincipalUser {
		linkContainers = sc.groupid2Strategy
	}
	sc.writeSet(linkContainers, principal.PrincipalID, rule.ID, true)
}

func (sc *strategyCache) addPrincipalLink(principal model.Principal, rule *model.StrategyDetail) {
	linkContainers := sc.uid2Strategy
	if principal.PrincipalRole != model.PrincipalUser {
		linkContainers = sc.groupid2Strategy
	}
	sc.writeSet(linkContainers, principal.PrincipalID, rule.ID, false)
}

func (sc *strategyCache) Clear() error {
	sc.BaseCache.Clear()
	sc.strategys = utils.NewSyncMap[string, *model.StrategyDetailCache]()
	sc.uid2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.groupid2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.namespace2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.service2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.configGroup2Strategy = utils.NewSyncMap[string, *utils.SyncSet[string]]()
	sc.lastMtime = 0
	return nil
}

func (sc *strategyCache) Name() string {
	return types.StrategyRuleName
}

// 对于 check 逻辑，如果是计算 * 策略，则必须要求 * 资源下必须有策略
// 如果是具体的资源ID，则该资源下不必有策略，如果没有策略就认为这个资源是可以被任何人编辑的
func (sc *strategyCache) checkResourceEditable(strategIds *utils.SyncSet[string], principal model.Principal, mustCheck bool) bool {
	// 是否可以编辑
	editable := false
	// 是否真的包含策略
	isCheck := strategIds.Len() != 0

	// 如果根本没有遍历过，则表示该资源下没有对应的策略列表，直接返回可编辑状态即可
	if !isCheck && !mustCheck {
		return true
	}

	strategIds.Range(func(strategyId string) {
		isCheck = true
		if rule, ok := sc.strategys.Load(strategyId); ok {
			if rule.Action == apisecurity.AuthAction_ONLY_READ.String() {
				return
			}
			if principal.PrincipalRole == model.PrincipalUser {
				_, exist := rule.UserPrincipal[principal.PrincipalID]
				editable = editable || exist
			} else {
				_, exist := rule.GroupPrincipal[principal.PrincipalID]
				editable = editable || exist
			}
		}
	})

	return editable
}

// checkResourceReadable 检查资源是否可读（包括只读和读写权限）
// 这个函数专门用于读操作的权限验证
func (sc *strategyCache) checkResourceReadable(strategIds *utils.SyncSet[string], principal model.Principal, mustCheck bool) bool {
	// 是否可以读取
	readable := false
	// 是否真的包含策略
	isCheck := strategIds.Len() != 0

	// 如果根本没有遍历过，则表示该资源下没有对应的策略列表，直接返回可读状态即可
	if !isCheck && !mustCheck {
		return true
	}

	strategIds.Range(func(strategyId string) {
		isCheck = true
		if rule, ok := sc.strategys.Load(strategyId); ok {
			// 对于读操作，只读权限和读写权限都应该被允许
			if rule.Action == apisecurity.AuthAction_ONLY_READ.String() ||
			   rule.Action == apisecurity.AuthAction_READ_WRITE.String() {
				if principal.PrincipalRole == model.PrincipalUser {
					_, exist := rule.UserPrincipal[principal.PrincipalID]
					readable = readable || exist
				} else {
					_, exist := rule.GroupPrincipal[principal.PrincipalID]
					readable = readable || exist
				}
			}
		}
	})

	return readable
}

// IsResourceEditable 判断当前资源是否可以操作
// 这里需要考虑两种情况，一种是 “ * ” 策略，另一种是明确指出了具体的资源ID的策略
func (sc *strategyCache) IsResourceEditable(
	principal model.Principal, resType apisecurity.ResourceType, resId string) bool {
	var (
		valAll, val *utils.SyncSet[string]
		ok          bool
	)
	switch resType {
	case apisecurity.ResourceType_Namespaces:
		val, ok = sc.namespace2Strategy.Load(resId)
		valAll, _ = sc.namespace2Strategy.Load("*")
	case apisecurity.ResourceType_Services:
		val, ok = sc.service2Strategy.Load(resId)
		valAll, _ = sc.service2Strategy.Load("*")
	case apisecurity.ResourceType_ConfigGroups:
		val, ok = sc.configGroup2Strategy.Load(resId)
		valAll, _ = sc.configGroup2Strategy.Load("*")
	}

	// 🔧 修复：该资源没有关联到任何策略时，检查是否是资源所有者或管理员
	if !ok {
		result := sc.isResourceOwnerOrAdmin(principal, resType, resId)
		log.Info("[Auth][Debug] No strategy found, checking resource owner/admin",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resType.String()),
			zap.String("resource-id", resId),
			zap.Bool("is-owner-or-admin", result))
		return result
	}

	principals := make([]model.Principal, 0, 4)
	principals = append(principals, principal)
	if principal.PrincipalRole == model.PrincipalUser {
		groupids := sc.userCache.GetUserLinkGroupIds(principal.PrincipalID)
		for i := range groupids {
			principals = append(principals, model.Principal{
				PrincipalID:   groupids[i],
				PrincipalRole: model.PrincipalGroup,
			})
		}
	}

	for i := range principals {
		item := principals[i]
		if valAll != nil && sc.checkResourceEditable(valAll, item, true) {
			log.Info("[Auth][Debug] Strategy check passed for global policy",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resType.String()),
				zap.String("resource-id", resId))
			return true
		}

		if val != nil && sc.checkResourceEditable(val, item, true) {
			log.Info("[Auth][Debug] Strategy check passed for specific policy",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resType.String()),
				zap.String("resource-id", resId))
			return true
		}
	}

	// 🔧 修复：如果策略检查失败，检查是否是资源所有者或管理员
	result := sc.isResourceOwnerOrAdmin(principal, resType, resId)
	log.Info("[Auth][Debug] Strategy check failed, checking resource owner/admin",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("resource-type", resType.String()),
		zap.String("resource-id", resId),
		zap.Bool("is-owner-or-admin", result))
	return result
}

// isResourceOwnerOrAdmin 检查用户是否是资源所有者或管理员
func (sc *strategyCache) isResourceOwnerOrAdmin(principal model.Principal, resType apisecurity.ResourceType, resId string) bool {
	log.Info("[Auth][Debug] Starting isResourceOwnerOrAdmin check",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("principal-role", string(principal.PrincipalRole)),
		zap.String("resource-type", resType.String()),
		zap.String("resource-id", resId))

	// 只检查用户类型的主体，用户组不适用于管理员检查
	if principal.PrincipalRole == model.PrincipalUser {
		// 通过用户ID获取用户信息，检查是否是管理员或所有者
		if user := sc.userCache.GetUserByID(principal.PrincipalID); user != nil {
			log.Info("[Auth][Debug] User found in cache",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("user-type", string(user.Type)))
			if user.Type == model.AdminUserRole || user.Type == model.OwnerUserRole {
				log.Info("[Auth][Debug] User is admin/owner, allowing access",
					zap.String("principal-id", principal.PrincipalID),
					zap.String("user-type", string(user.Type)))
				return true
			}
		} else {
			log.Info("[Auth][Debug] User not found in cache",
				zap.String("principal-id", principal.PrincipalID))
		}
	}

	// 检查是否是资源的创建者/所有者
	switch resType {
	case apisecurity.ResourceType_Namespaces:
		log.Info("[Auth][Debug] Checking namespace ownership",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("namespace-id", resId))
		if nsCache := sc.BaseCache.CacheMgr.GetCacher(types.CacheNamespace); nsCache != nil {
			if ns := nsCache.(types.NamespaceCache).GetNamespace(resId); ns != nil {
				isOwner := ns.Owner == principal.PrincipalID
				log.Info("[Auth][Debug] Namespace ownership check result",
					zap.String("principal-id", principal.PrincipalID),
					zap.String("namespace-id", resId),
					zap.String("namespace-owner", ns.Owner),
					zap.Bool("is-owner", isOwner))
				return isOwner
			} else {
				log.Info("[Auth][Debug] Namespace not found in cache",
					zap.String("namespace-id", resId))
			}
		}
	case apisecurity.ResourceType_Services:
		log.Info("[Auth][Debug] Checking service ownership",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("service-id", resId))
		if svcCache := sc.BaseCache.CacheMgr.GetCacher(types.CacheService); svcCache != nil {
			if svc := svcCache.(types.ServiceCache).GetServiceByID(resId); svc != nil {
				isOwner := svc.Owner == principal.PrincipalID
				log.Info("[Auth][Debug] Service ownership check result",
					zap.String("principal-id", principal.PrincipalID),
					zap.String("service-id", resId),
					zap.String("service-owner", svc.Owner),
					zap.Bool("is-owner", isOwner))
				return isOwner
			} else {
				log.Info("[Auth][Debug] Service not found in cache, checking virtual service",
					zap.String("service-id", resId))
			}
		}
		// 🔧 修复：对于虚拟服务（服务注册时创建的临时服务），检查命名空间权限
		// 虚拟服务ID格式为 "namespace.serviceName"，如果服务不存在于缓存中，
		// 则检查用户是否有对应命名空间的权限
		if parts := strings.Split(resId, "."); len(parts) >= 2 {
			namespace := parts[0]
			log.Info("[Auth][Debug] Checking virtual service namespace ownership",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("service-id", resId),
				zap.String("namespace", namespace))
			if nsCache := sc.BaseCache.CacheMgr.GetCacher(types.CacheNamespace); nsCache != nil {
				if ns := nsCache.(types.NamespaceCache).GetNamespace(namespace); ns != nil {
					// 首先检查用户是否是命名空间的直接所有者
					isDirectOwner := ns.Owner == principal.PrincipalID
					log.Info("[Auth][Debug] Virtual service namespace direct ownership check",
						zap.String("principal-id", principal.PrincipalID),
						zap.String("service-id", resId),
						zap.String("namespace", namespace),
						zap.String("namespace-owner", ns.Owner),
						zap.Bool("is-direct-owner", isDirectOwner))

					if isDirectOwner {
						return true
					}

					// 如果不是直接所有者，检查用户是否通过权限策略有对该命名空间的访问权限
					hasNamespacePermission := sc.IsResourceEditable(principal, apisecurity.ResourceType_Namespaces, namespace)
					log.Info("[Auth][Debug] Virtual service namespace permission check result",
						zap.String("principal-id", principal.PrincipalID),
						zap.String("service-id", resId),
						zap.String("namespace", namespace),
						zap.Bool("has-namespace-permission", hasNamespacePermission))
					return hasNamespacePermission
				} else {
					log.Info("[Auth][Debug] Virtual service namespace not found in cache",
						zap.String("namespace", namespace))
				}
			}
		}
	}

	log.Info("[Auth][Debug] Resource ownership check failed, denying access",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("resource-type", resType.String()),
		zap.String("resource-id", resId))
	return false  // 默认拒绝
}

func (sc *strategyCache) GetStrategyDetailsByUID(uid string) []*model.StrategyDetail {
	return sc.getStrategyDetails(uid, "")
}

func (sc *strategyCache) GetStrategyDetailsByGroupID(groupid string) []*model.StrategyDetail {
	return sc.getStrategyDetails("", groupid)
}

func (sc *strategyCache) getStrategyDetails(uid string, gid string) []*model.StrategyDetail {
	var (
		strategyIds []string
	)
	if uid != "" {
		sets, ok := sc.uid2Strategy.Load(uid)
		if !ok {
			return nil
		}
		strategyIds = sets.ToSlice()
	} else if gid != "" {
		sets, ok := sc.groupid2Strategy.Load(gid)
		if !ok {
			return nil
		}
		strategyIds = sets.ToSlice()
	}

	if len(strategyIds) > 0 {
		result := make([]*model.StrategyDetail, 0, 16)
		for i := range strategyIds {
			strategy, ok := sc.strategys.Load(strategyIds[i])
			if ok {
				result = append(result, strategy.StrategyDetail)
			}
		}

		return result
	}

	return nil
}

// IsResourceReadable 判断当前资源是否可读（包括只读和读写权限）
// 专门用于读操作的权限验证，与IsResourceEditable分离以避免影响现有写操作的权限检查
func (sc *strategyCache) IsResourceReadable(
	principal model.Principal, resType apisecurity.ResourceType, resId string) bool {
	var (
		valAll, val *utils.SyncSet[string]
		ok          bool
	)
	switch resType {
	case apisecurity.ResourceType_Namespaces:
		val, ok = sc.namespace2Strategy.Load(resId)
		valAll, _ = sc.namespace2Strategy.Load("*")
	case apisecurity.ResourceType_Services:
		val, ok = sc.service2Strategy.Load(resId)
		valAll, _ = sc.service2Strategy.Load("*")
	case apisecurity.ResourceType_ConfigGroups:
		val, ok = sc.configGroup2Strategy.Load(resId)
		valAll, _ = sc.configGroup2Strategy.Load("*")
	}

	// 该资源没有关联到任何策略时，检查是否是资源所有者或管理员
	if !ok {
		result := sc.isResourceOwnerOrAdmin(principal, resType, resId)
		log.Info("[Auth][Debug] No strategy found for read check, checking resource owner/admin",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resType.String()),
			zap.String("resource-id", resId),
			zap.Bool("is-owner-or-admin", result))
		return result
	}

	principals := make([]model.Principal, 0, 4)
	principals = append(principals, principal)
	if principal.PrincipalRole == model.PrincipalUser {
		groupids := sc.userCache.GetUserLinkGroupIds(principal.PrincipalID)
		for i := range groupids {
			principals = append(principals, model.Principal{
				PrincipalID:   groupids[i],
				PrincipalRole: model.PrincipalGroup,
			})
		}
	}

	for i := range principals {
		item := principals[i]
		if valAll != nil && sc.checkResourceReadable(valAll, item, true) {
			log.Info("[Auth][Debug] Read strategy check passed for global policy",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resType.String()),
				zap.String("resource-id", resId))
			return true
		}

		if val != nil && sc.checkResourceReadable(val, item, true) {
			log.Info("[Auth][Debug] Read strategy check passed for specific policy",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resType.String()),
				zap.String("resource-id", resId))
			return true
		}
	}

	// 如果策略检查失败，检查是否是资源所有者或管理员
	result := sc.isResourceOwnerOrAdmin(principal, resType, resId)
	log.Info("[Auth][Debug] Read strategy check failed, checking resource owner/admin",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("resource-type", resType.String()),
		zap.String("resource-id", resId),
		zap.Bool("is-owner-or-admin", result))
	return result
}

// IsResourceLinkStrategy 校验
func (sc *strategyCache) IsResourceLinkStrategy(resType apisecurity.ResourceType, resId string) bool {
	switch resType {
	case apisecurity.ResourceType_Namespaces:
		val, ok := sc.namespace2Strategy.Load(resId)
		return ok && hasLinkRule(val)
	case apisecurity.ResourceType_Services:
		val, ok := sc.service2Strategy.Load(resId)
		return ok && hasLinkRule(val)
	case apisecurity.ResourceType_ConfigGroups:
		val, ok := sc.configGroup2Strategy.Load(resId)
		return ok && hasLinkRule(val)
	default:
		return true
	}
}

func hasLinkRule(sets *utils.SyncSet[string]) bool {
	return sets.Len() != 0
}
