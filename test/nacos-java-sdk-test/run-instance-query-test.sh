#!/bin/bash

# 服务实例查询独立测试运行脚本
# Service Instance Query Test Runner

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 服务实例查询独立测试运行器 ===${NC}"
echo -e "${YELLOW}测试类: com.polaris.test.ServiceInstanceQueryTest${NC}"
echo ""

# 记录开始时间
START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo -e "${YELLOW}测试开始时间: ${START_TIME}${NC}"
echo ""

# 编译项目
echo -e "${YELLOW}正在编译项目...${NC}"
if mvn compile -q; then
    echo -e "${GREEN}编译成功！${NC}"
else
    echo -e "${RED}编译失败！${NC}"
    exit 1
fi

# 运行测试
echo -e "${YELLOW}正在运行服务实例查询测试: com.polaris.test.ServiceInstanceQueryTest${NC}"
echo ""

# 执行测试 - 强制指定主类并禁用默认配置
mvn exec:java -Dexec.mainClass="com.polaris.test.ServiceInstanceQueryTest" -Dexec.args="" -Dexec.cleanupDaemonThreads=false -q

# 记录结束时间
END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo -e "${YELLOW}测试开始时间: ${START_TIME}${NC}"
echo -e "${YELLOW}测试结束时间: ${END_TIME}${NC}"
echo -e "${YELLOW}日志文件位置:${NC}"
echo "  - 详细日志: logs/nacos-permission-test-detailed.log"
echo "  - 测试摘要: logs/test-summary.log"
echo ""
echo -e "${YELLOW}查看日志命令:${NC}"
echo "  tail -f logs/nacos-permission-test-detailed.log"
echo "  cat logs/test-summary.log"
