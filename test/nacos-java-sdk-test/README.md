# Nacos Java SDK 权限测试项目

## 项目概述

本项目是用于验证 Polaris 系统中 Nacos 协议兼容层权限控制功能的 Java SDK 测试项目。主要用于验证权限绕过漏洞的修复效果，确保权限控制系统正常工作。

## 测试目标

1. **权限绕过漏洞验证**：确认无权限用户无法执行未授权操作
2. **正常功能验证**：确认有权限用户的正常操作不受影响
3. **兼容性验证**：确认 Java SDK 与修复后的服务端兼容
4. **边界情况测试**：验证各种边界条件下的权限控制

## 环境要求

- **Java**: JDK 8 或更高版本
- **Maven**: 3.6.0 或更高版本
- **网络**: 能够访问测试服务器 `180.76.109.137:8848`

## 项目结构

```
nacos-java-sdk-test/
├── pom.xml                           # Maven 配置文件
├── README.md                         # 项目说明文档
├── src/
│   ├── main/
│   │   ├── java/com/polaris/test/
│   │   │   ├── TestConfig.java       # 测试配置类
│   │   │   ├── NacosClientUtil.java  # Nacos 客户端工具类
│   │   │   ├── PermissionTestCase.java # 权限测试用例
│   │   │   └── NacosPermissionTestRunner.java # 测试运行器
│   │   └── resources/
│   │       └── logback.xml           # 日志配置
│   └── test/
│       └── java/com/polaris/test/
│           └── NacosPermissionTest.java # JUnit 测试类
└── logs/                             # 日志输出目录
```

## 测试配置

### 服务端配置
- **服务端地址**: `180.76.109.137:8848`
- **协议**: Nacos HTTP API

### 用户配置
- **测试用户**: cf 用户
- **用户Token**: `RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=`

### 命名空间配置
- **有权限命名空间**: `cf` (cf 用户有读写权限)
- **无权限命名空间**: `cf-ns` (cf 用户无权限)

## 测试用例

### 1. 有权限用户服务注册测试
- **目标**: 验证有权限用户可以正常注册服务
- **预期结果**: 注册成功

### 2. 无权限用户服务注册测试
- **目标**: 验证无权限用户无法注册服务
- **预期结果**: 注册被拒绝，返回权限错误

### 3. 有权限用户服务注销测试
- **目标**: 验证有权限用户可以正常注销服务
- **预期结果**: 注销成功

### 4. 无权限用户服务注销测试
- **目标**: 验证无权限用户无法注销服务（权限绕过漏洞测试）
- **预期结果**: 注销被拒绝，返回权限错误

### 5. 服务发现功能测试
- **目标**: 验证服务发现功能正常工作
- **预期结果**: 能够正确发现已注册的服务

### 6. 跨命名空间访问测试
- **目标**: 验证命名空间隔离功能
- **预期结果**: 不同命名空间的服务相互隔离

### 7. 批量操作权限控制测试
- **目标**: 验证批量操作的权限控制
- **预期结果**: 无权限的批量操作全部被拒绝

## 运行方式

### 方式1: 使用 Maven 运行主程序

```bash
# 编译项目
mvn clean compile

# 运行测试程序
mvn exec:java -Dexec.mainClass="com.polaris.test.NacosPermissionTestRunner"
```

### 方式2: 使用 Maven 运行 JUnit 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=NacosPermissionTest

# 运行特定测试方法
mvn test -Dtest=NacosPermissionTest#testUnauthorizedServiceDeregistration
```

### 方式3: 使用 IDE 运行

1. 导入项目到 IntelliJ IDEA 或 Eclipse
2. 运行 `NacosPermissionTestRunner.main()` 方法
3. 或者运行 `NacosPermissionTest` 中的 JUnit 测试

## 测试结果解读

### 成功标准

1. **有权限操作**: 所有有权限用户的操作都应该成功
2. **无权限操作**: 所有无权限用户的操作都应该被拒绝
3. **权限绕过检测**: 不应该出现"权限绕过"的情况
4. **功能完整性**: 服务发现等基础功能正常工作

### 失败情况分析

#### 权限绕过漏洞
如果看到以下信息，说明权限绕过漏洞仍然存在：
```
权限绕过！无权限用户成功注册了服务
权限绕过！无权限用户成功注销了服务
```

#### 过度严格的权限控制
如果有权限用户的操作失败，可能是权限控制过于严格：
```
有权限用户的服务注册失败
有权限用户的服务注销失败
```

#### 环境或配置问题
如果出现连接错误或配置错误：
```
测试执行异常: Connection refused
测试执行异常: Invalid token
```

## 日志文件

测试运行时会生成详细的日志文件：
- **位置**: `logs/nacos-permission-test.log`
- **内容**: 包含所有测试操作的详细日志
- **轮转**: 按日期和大小自动轮转

## 故障排除

### 1. Java 环境问题
```bash
# 检查 Java 版本
java -version

# 检查 JAVA_HOME
echo $JAVA_HOME
```

### 2. Maven 环境问题
```bash
# 检查 Maven 版本
mvn -version

# 清理并重新编译
mvn clean compile
```

### 3. 网络连接问题
```bash
# 测试服务端连通性
curl -I http://180.76.109.137:8848/nacos/

# 检查防火墙设置
```

### 4. 权限配置问题
- 确认 cf 用户的 token 是否正确
- 确认命名空间权限配置是否正确
- 检查服务端日志中的权限检查信息

## 扩展测试

如需添加更多测试用例，可以：

1. 在 `PermissionTestCase.java` 中添加新的测试方法
2. 在 `NacosPermissionTest.java` 中添加对应的 JUnit 测试
3. 在 `NacosPermissionTestRunner.java` 中调用新的测试方法

## 联系信息

如有问题，请联系开发团队或查看相关文档：
- 权限修复文档: `apiserver/nacosserver/nacos服务注册发现问题修复.md`
- Nacos 协议兼容文档: `apiserver/nacosserver/兼容nacos协议.md`
