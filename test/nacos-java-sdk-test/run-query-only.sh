#!/bin/bash

# 纯净的服务实例查询测试运行器
# Clean Service Instance Query Test Runner

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 纯净服务实例查询测试 ===${NC}"
echo -e "${YELLOW}只运行服务实例查询操作，无其他干扰信息${NC}"
echo ""

# 记录开始时间
START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo -e "${YELLOW}测试开始时间: ${START_TIME}${NC}"
echo ""

# 确保项目已编译
if [ ! -d "target/classes" ]; then
    echo -e "${YELLOW}正在编译项目...${NC}"
    mvn compile -q
    echo -e "${GREEN}编译完成${NC}"
    echo ""
fi

# 获取classpath
echo -e "${YELLOW}准备运行环境...${NC}"
CLASSPATH="target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)"

# 直接运行Java类，避免Maven的额外输出
echo -e "${BLUE}开始执行服务实例查询测试...${NC}"
echo ""

# 运行测试
java -cp "$CLASSPATH" com.polaris.test.ServiceInstanceQueryTest

# 记录结束时间
END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo -e "${YELLOW}开始时间: ${START_TIME}${NC}"
echo -e "${YELLOW}结束时间: ${END_TIME}${NC}"
