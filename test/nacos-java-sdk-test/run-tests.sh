#!/bin/bash

# Nacos Java SDK 权限测试运行脚本
# 用于快速执行权限控制功能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    print_info "检查运行环境..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        print_error "Java 未安装或不在 PATH 中"
        print_info "请安装 JDK 8 或更高版本"
        exit 1
    fi
    
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    print_success "Java 版本: $java_version"
    
    # 检查 Maven
    if ! command -v mvn &> /dev/null; then
        print_error "Maven 未安装或不在 PATH 中"
        print_info "请安装 Maven 3.6.0 或更高版本"
        exit 1
    fi
    
    mvn_version=$(mvn -version | head -n 1)
    print_success "Maven 版本: $mvn_version"
    
    # 检查网络连通性
    print_info "检查服务端连通性..."
    if curl -s --connect-timeout 5 http://180.76.109.137:8848/nacos/ > /dev/null; then
        print_success "服务端连接正常"
    else
        print_warning "无法连接到服务端，测试可能会失败"
    fi
}

# 编译项目
compile_project() {
    print_info "编译项目..."
    
    if mvn clean compile > compile.log 2>&1; then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        print_info "查看编译日志: cat compile.log"
        exit 1
    fi
}

# 运行主程序测试
run_main_tests() {
    print_info "运行主程序测试..."
    
    echo "========================================"
    echo "开始执行 Nacos Java SDK 权限测试"
    echo "========================================"
    
    if mvn exec:java -Dexec.mainClass="com.polaris.test.NacosPermissionTestRunner" -q; then
        print_success "主程序测试执行完成"
    else
        print_error "主程序测试执行失败"
        return 1
    fi
}

# 运行 JUnit 测试
run_junit_tests() {
    print_info "运行 JUnit 测试..."
    
    echo "========================================"
    echo "开始执行 JUnit 测试"
    echo "========================================"
    
    if mvn test > junit.log 2>&1; then
        print_success "JUnit 测试全部通过"
        
        # 显示测试结果摘要
        if grep -q "Tests run:" junit.log; then
            test_summary=$(grep "Tests run:" junit.log | tail -n 1)
            print_info "测试摘要: $test_summary"
        fi
    else
        print_error "JUnit 测试失败"
        print_info "查看测试日志: cat junit.log"
        
        # 显示失败的测试
        if grep -q "FAILURE" junit.log; then
            print_warning "失败的测试:"
            grep -A 2 "FAILURE" junit.log || true
        fi
        
        return 1
    fi
}

# 分析测试结果
analyze_results() {
    print_info "分析测试结果..."
    
    # 检查日志文件
    if [ -f "logs/nacos-permission-test.log" ]; then
        log_file="logs/nacos-permission-test.log"
        
        # 检查权限绕过问题
        if grep -q "权限绕过" "$log_file"; then
            print_error "发现权限绕过问题！"
            grep "权限绕过" "$log_file" | head -n 5
            return 1
        fi
        
        # 检查权限拒绝情况
        permission_denied_count=$(grep -c "no permission\|access is not approved" "$log_file" 2>/dev/null || echo "0")
        if [ "$permission_denied_count" -gt 0 ]; then
            print_success "发现 $permission_denied_count 次权限拒绝，权限控制正常工作"
        fi
        
        # 检查成功操作
        success_count=$(grep -c "成功" "$log_file" 2>/dev/null || echo "0")
        if [ "$success_count" -gt 0 ]; then
            print_success "发现 $success_count 次成功操作"
        fi
        
    else
        print_warning "未找到详细日志文件"
    fi
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    rm -f compile.log junit.log
}

# 显示帮助信息
show_help() {
    echo "Nacos Java SDK 权限测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --compile  仅编译项目"
    echo "  -m, --main     仅运行主程序测试"
    echo "  -j, --junit    仅运行 JUnit 测试"
    echo "  -a, --all      运行所有测试（默认）"
    echo ""
    echo "示例:"
    echo "  $0              # 运行所有测试"
    echo "  $0 --main       # 仅运行主程序测试"
    echo "  $0 --junit      # 仅运行 JUnit 测试"
}

# 主函数
main() {
    local run_compile=true
    local run_main=false
    local run_junit=false
    local run_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--compile)
                run_all=false
                run_compile=true
                ;;
            -m|--main)
                run_all=false
                run_main=true
                ;;
            -j|--junit)
                run_all=false
                run_junit=true
                ;;
            -a|--all)
                run_all=true
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
    
    # 如果没有指定特定选项，运行所有测试
    if [ "$run_all" = true ]; then
        run_main=true
        run_junit=true
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 执行检查和测试
    check_environment
    
    if [ "$run_compile" = true ]; then
        compile_project
    fi
    
    local test_failed=false
    
    if [ "$run_main" = true ]; then
        if ! run_main_tests; then
            test_failed=true
        fi
    fi
    
    if [ "$run_junit" = true ]; then
        if ! run_junit_tests; then
            test_failed=true
        fi
    fi
    
    # 分析结果
    if [ "$run_main" = true ] || [ "$run_junit" = true ]; then
        if ! analyze_results; then
            test_failed=true
        fi
    fi
    
    # 清理
    cleanup
    
    # 输出最终结果
    echo "========================================"
    if [ "$test_failed" = true ]; then
        print_error "测试执行完成，但发现问题"
        print_info "请检查上述错误信息和日志文件"
        exit 1
    else
        print_success "所有测试执行完成，权限控制功能正常"
        print_info "详细日志请查看: logs/nacos-permission-test.log"
    fi
    echo "========================================"
}

# 执行主函数
main "$@"
