#!/bin/bash

# Nacos Java SDK 权限测试执行脚本
# 用于在有 Java 环境的机器上执行完整的权限控制测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    print_header "环境检查"
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        print_error "Java 未安装或不在 PATH 中"
        print_info "请安装 JDK 8 或更高版本"
        exit 1
    fi
    
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    print_success "Java 版本: $java_version"
    
    # 检查 Maven
    if ! command -v mvn &> /dev/null; then
        print_error "Maven 未安装或不在 PATH 中"
        print_info "请安装 Maven 3.6.0 或更高版本"
        exit 1
    fi
    
    mvn_version=$(mvn -version | head -n 1)
    print_success "Maven 版本: $mvn_version"
    
    # 检查网络连通性
    print_info "检查服务端连通性..."
    if curl -s --connect-timeout 5 http://180.76.109.137:8848/nacos/ > /dev/null; then
        print_success "服务端连接正常"
    else
        print_warning "无法连接到服务端，测试可能会失败"
        print_info "请确认服务端 180.76.109.137:8848 可访问"
    fi
    
    echo ""
}

# 编译项目
compile_project() {
    print_header "编译项目"
    
    print_info "清理并编译项目..."
    if mvn clean compile > compile.log 2>&1; then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        print_info "编译日志："
        cat compile.log
        exit 1
    fi
    
    echo ""
}

# 执行权限测试
execute_permission_tests() {
    print_header "执行权限控制测试"
    
    print_info "开始执行 Nacos Java SDK 权限测试..."
    print_info "测试目标：验证权限绕过漏洞修复效果"
    print_warning "⚠️  关键测试：无权限用户的服务注销操作应被拒绝"
    
    echo ""
    
    # 运行主程序测试
    print_info "运行主程序测试..."
    if mvn exec:java -Dexec.mainClass="com.polaris.test.NacosPermissionTestRunner" > main-test.log 2>&1; then
        print_success "主程序测试执行完成"
    else
        print_error "主程序测试执行失败"
        print_info "查看测试日志: cat main-test.log"
    fi
    
    echo ""
    
    # 运行 JUnit 测试
    print_info "运行 JUnit 测试..."
    if mvn test > junit-test.log 2>&1; then
        print_success "JUnit 测试执行完成"
    else
        print_warning "JUnit 测试可能有失败"
        print_info "查看测试日志: cat junit-test.log"
    fi
    
    echo ""
}

# 分析测试结果
analyze_test_results() {
    print_header "测试结果分析"
    
    local has_security_issue=false
    local has_test_failure=false
    
    # 分析主程序测试结果
    if [ -f "main-test.log" ]; then
        print_info "分析主程序测试结果..."
        
        # 检查权限绕过问题
        if grep -q "权限绕过" main-test.log; then
            print_error "🚨 发现权限绕过安全漏洞！"
            grep "权限绕过" main-test.log | head -n 3
            has_security_issue=true
        fi
        
        # 检查权限拒绝情况
        permission_denied_count=$(grep -c "权限控制正常\|被正确拒绝\|no permission\|access is not approved" main-test.log 2>/dev/null || echo "0")
        if [ "$permission_denied_count" -gt 0 ]; then
            print_success "✅ 发现 $permission_denied_count 次权限拒绝，权限控制正常"
        fi
        
        # 检查测试通过情况
        if grep -q "所有测试都通过了" main-test.log; then
            print_success "✅ 所有主程序测试都通过了"
        elif grep -q "有测试失败" main-test.log; then
            print_warning "⚠️  主程序测试有失败"
            has_test_failure=true
        fi
    fi
    
    echo ""
    
    # 分析 JUnit 测试结果
    if [ -f "junit-test.log" ]; then
        print_info "分析 JUnit 测试结果..."
        
        if grep -q "Tests run:" junit-test.log; then
            test_summary=$(grep "Tests run:" junit-test.log | tail -n 1)
            print_info "测试摘要: $test_summary"
            
            if grep -q "Failures: 0" junit-test.log && grep -q "Errors: 0" junit-test.log; then
                print_success "✅ 所有 JUnit 测试都通过了"
            else
                print_warning "⚠️  JUnit 测试有失败"
                has_test_failure=true
                
                # 显示失败的测试
                if grep -q "FAILURE" junit-test.log; then
                    print_warning "失败的测试:"
                    grep -A 2 "FAILURE" junit-test.log | head -n 10
                fi
            fi
        fi
    fi
    
    echo ""
    
    # 分析详细日志
    if [ -f "logs/nacos-permission-test.log" ]; then
        print_info "分析详细日志文件..."
        
        # 统计关键事件
        local register_success=$(grep -c "服务实例注册成功" logs/nacos-permission-test.log 2>/dev/null || echo "0")
        local register_failed=$(grep -c "服务实例注册失败" logs/nacos-permission-test.log 2>/dev/null || echo "0")
        local deregister_success=$(grep -c "服务实例注销成功" logs/nacos-permission-test.log 2>/dev/null || echo "0")
        local deregister_failed=$(grep -c "服务实例注销失败" logs/nacos-permission-test.log 2>/dev/null || echo "0")
        
        print_info "操作统计："
        print_info "  - 注册成功: $register_success 次"
        print_info "  - 注册失败: $register_failed 次"
        print_info "  - 注销成功: $deregister_success 次"
        print_info "  - 注销失败: $deregister_failed 次"
    fi
    
    echo ""
    
    # 生成最终结论
    if [ "$has_security_issue" = true ]; then
        print_error "🚨 测试结论：发现严重安全漏洞"
        print_error "权限绕过漏洞仍然存在，需要进一步修复"
        return 1
    elif [ "$has_test_failure" = true ]; then
        print_warning "⚠️  测试结论：测试有失败"
        print_warning "可能是环境问题或配置问题，请检查详细日志"
        return 1
    else
        print_success "🎉 测试结论：权限控制功能正常"
        print_success "权限绕过漏洞已修复，所有测试通过"
        return 0
    fi
}

# 生成测试报告
generate_test_report() {
    print_header "生成测试报告"
    
    local report_file="permission-test-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# Nacos Java SDK 权限控制测试报告

## 测试概述

- **测试时间**: $(date)
- **测试环境**: 180.76.109.137:8848
- **测试用户**: cf 用户
- **测试目标**: 验证权限绕过漏洞修复效果

## 测试配置

- **有权限命名空间**: cf
- **无权限命名空间**: cf-ns
- **测试Token**: RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=

## 测试结果

### 主程序测试结果
EOF

    if [ -f "main-test.log" ]; then
        echo "```" >> "$report_file"
        tail -n 50 main-test.log >> "$report_file"
        echo "```" >> "$report_file"
    fi

    cat >> "$report_file" << EOF

### JUnit 测试结果
EOF

    if [ -f "junit-test.log" ]; then
        echo "```" >> "$report_file"
        grep -A 10 -B 5 "Tests run:" junit-test.log | tail -n 20 >> "$report_file"
        echo "```" >> "$report_file"
    fi

    cat >> "$report_file" << EOF

### 详细日志摘要
EOF

    if [ -f "logs/nacos-permission-test.log" ]; then
        echo "```" >> "$report_file"
        grep -E "(权限绕过|权限控制正常|测试结果|SUCCESS|ERROR)" logs/nacos-permission-test.log | tail -n 20 >> "$report_file"
        echo "```" >> "$report_file"
    fi

    cat >> "$report_file" << EOF

## 测试结论

$(if [ -f "main-test.log" ] && grep -q "权限绕过" main-test.log; then
    echo "🚨 **发现安全漏洞**: 权限绕过漏洞仍然存在"
elif [ -f "main-test.log" ] && grep -q "所有测试都通过了" main-test.log; then
    echo "✅ **测试通过**: 权限控制功能正常，权限绕过漏洞已修复"
else
    echo "⚠️ **需要检查**: 测试结果不明确，请查看详细日志"
fi)

## 文件清单

- 主程序测试日志: main-test.log
- JUnit 测试日志: junit-test.log
- 详细运行日志: logs/nacos-permission-test.log
- 编译日志: compile.log

---
*报告生成时间: $(date)*
EOF

    print_success "测试报告已生成: $report_file"
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    # 保留重要的日志文件，只清理编译缓存
    mvn clean > /dev/null 2>&1 || true
}

# 主函数
main() {
    print_header "Nacos Java SDK 权限控制测试"
    print_info "开始执行完整的权限控制测试验证"
    print_warning "⚠️  这是权限绕过漏洞修复的关键验证测试"
    
    echo ""
    
    # 创建日志目录
    mkdir -p logs
    
    # 执行测试流程
    check_environment
    compile_project
    execute_permission_tests
    
    # 分析结果
    if analyze_test_results; then
        test_result="PASS"
    else
        test_result="FAIL"
    fi
    
    # 生成报告
    generate_test_report
    
    # 清理
    cleanup
    
    # 输出最终结果
    print_header "测试完成"
    if [ "$test_result" = "PASS" ]; then
        print_success "🎉 权限控制测试全部通过"
        print_success "权限绕过漏洞已成功修复"
    else
        print_error "❌ 权限控制测试发现问题"
        print_error "请检查测试报告和日志文件"
        exit 1
    fi
}

# 执行主函数
main "$@"
