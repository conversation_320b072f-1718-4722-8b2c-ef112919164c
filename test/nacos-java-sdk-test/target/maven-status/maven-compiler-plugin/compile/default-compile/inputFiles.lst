/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ServiceDataAnalysis.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/PermissionTestCase.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ComprehensivePermissionTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/SpecialCharacterAnalysis.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/NacosCompatibilityTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/NewConfigReadTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/InteractiveRegistryTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/OptimizedLifecycleTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/PermissionRevocationTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ConfigPermissionDebugTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/Phase2AuthEnabledTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ConfigCenterPermissionTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ComprehensiveNacosTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/PermissionBypassAnalysisTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ComprehensiveTestDataSetup.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/CacheClearTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/DetailedPermissionAnalysisTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/TestUserConfig.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ComprehensiveConfigCenterTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/CreateTestConfigsForPermissionTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/SimplePermissionTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/NacosLoginTestRunner.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ClientAuthTestUtil.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/EnhancedConfigCenterTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/TestConfig.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/InteractiveConfigCenterTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/DetailedPermissionAnalysis.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ServiceInstanceQueryTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/NacosPermissionTestRunner.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/NacosClientUtil.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/TestLogger.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ClientCacheAnalysisTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/PermissionVerificationInteractiveTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/JavaSDKOnlyTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/PermissionBypassTest.java
/Users/<USER>/workspace/go/baidu/bce-api/api-logic-mse/polaris/test/nacos-java-sdk-test/src/main/java/com/polaris/test/ClientAuthTestRunner.java
