#!/bin/bash

# Nacos Java SDK 测试运行脚本
# 使用方法: ./run-test.sh [测试类名]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认测试类
DEFAULT_TEST_CLASS="com.polaris.test.NacosPermissionTestRunner"
TEST_CLASS=${1:-$DEFAULT_TEST_CLASS}

echo -e "${GREEN}=== Nacos Java SDK 测试运行器 ===${NC}"
echo -e "${YELLOW}测试类: ${TEST_CLASS}${NC}"
echo ""

# 检查是否在正确的目录
if [ ! -f "pom.xml" ]; then
    echo -e "${RED}错误: 请在 nacos-java-sdk-test 目录下运行此脚本${NC}"
    exit 1
fi

# 编译项目
echo -e "${YELLOW}正在编译项目...${NC}"
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo -e "${RED}编译失败！${NC}"
    exit 1
fi

echo -e "${GREEN}编译成功！${NC}"

# 创建日志目录
mkdir -p logs

# 运行测试
echo -e "${YELLOW}正在运行测试: ${TEST_CLASS}${NC}"
echo ""

java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" $TEST_CLASS

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo -e "${YELLOW}日志文件位置:${NC}"
echo "  - 详细日志: logs/nacos-permission-test-detailed.log"
echo "  - 测试摘要: logs/test-summary.log"
echo "  - 权限测试: logs/permission-test.log"
echo ""
echo -e "${YELLOW}查看日志命令:${NC}"
echo "  tail -f logs/nacos-permission-test-detailed.log"
echo "  cat logs/test-summary.log"
