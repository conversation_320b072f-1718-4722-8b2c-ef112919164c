package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 在cf-n12s命名空间中创建测试配置
 * 用于后续的权限验证测试
 */
public class CreateTestConfigsForPermissionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CreateTestConfigsForPermissionTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String TARGET_NAMESPACE = "cf-n12s";
    
    // 测试配置定义
    private static final TestConfig[] TEST_CONFIGS = {
        new TestConfig("permission-test-database-config", "DEFAULT_GROUP", 
            "# Database Configuration\n" +
            "spring.datasource.url=*************************************" +
            "spring.datasource.username=test_user\n" +
            "spring.datasource.password=test_password\n" +
            "spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver\n" +
            "spring.jpa.hibernate.ddl-auto=update\n" +
            "spring.jpa.show-sql=true\n" +
            "# Created for permission testing at " + System.currentTimeMillis()),
            
        new TestConfig("permission-test-redis-config", "CACHE_GROUP", 
            "# Redis Configuration\n" +
            "spring.redis.host=localhost\n" +
            "spring.redis.port=6379\n" +
            "spring.redis.password=redis_password\n" +
            "spring.redis.database=0\n" +
            "spring.redis.timeout=2000ms\n" +
            "spring.redis.lettuce.pool.max-active=8\n" +
            "spring.redis.lettuce.pool.max-idle=8\n" +
            "spring.redis.lettuce.pool.min-idle=0\n" +
            "# Created for permission testing at " + System.currentTimeMillis()),
            
        new TestConfig("permission-test-application-config", "APP_GROUP", 
            "# Application Configuration\n" +
            "server.port=8080\n" +
            "server.servlet.context-path=/api\n" +
            "logging.level.com.polaris=DEBUG\n" +
            "logging.level.org.springframework=INFO\n" +
            "management.endpoints.web.exposure.include=health,info,metrics\n" +
            "management.endpoint.health.show-details=always\n" +
            "app.name=Permission Test Application\n" +
            "app.version=1.0.0\n" +
            "# Created for permission testing at " + System.currentTimeMillis()),
            
        new TestConfig("permission-test-security-config", "SECURITY_GROUP", 
            "# Security Configuration\n" +
            "security.jwt.secret=test_jwt_secret_key_for_permission_testing\n" +
            "security.jwt.expiration=86400\n" +
            "security.cors.allowed-origins=http://localhost:3000,http://localhost:8080\n" +
            "security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS\n" +
            "security.cors.allowed-headers=*\n" +
            "security.cors.allow-credentials=true\n" +
            "# Created for permission testing at " + System.currentTimeMillis()),
            
        new TestConfig("permission-test-monitoring-config", "MONITOR_GROUP", 
            "# Monitoring Configuration\n" +
            "monitoring.enabled=true\n" +
            "monitoring.metrics.export.prometheus.enabled=true\n" +
            "monitoring.health.diskspace.enabled=true\n" +
            "monitoring.health.diskspace.threshold=10MB\n" +
            "monitoring.tracing.enabled=true\n" +
            "monitoring.tracing.sampling-rate=0.1\n" +
            "# Created for permission testing at " + System.currentTimeMillis())
    };
    
    static class TestConfig {
        final String dataId;
        final String group;
        final String content;
        
        TestConfig(String dataId, String group, String content) {
            this.dataId = dataId;
            this.group = group;
            this.content = content;
        }
    }
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            在cf-n12s命名空间中创建测试配置                      ║");
        logger.info("║        Create Test Configs in cf-n12s Namespace         ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 创建多个测试配置用于后续权限验证测试                      ║");
        logger.info("║ 命名空间: cf-n12s (cf用户有读写权限)                          ║");
        logger.info("║ 配置数量: {} 个", TEST_CONFIGS.length);
        logger.info("║ 创建时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            createTestConfigs();
            logger.info("");
            logger.info("🎉 所有测试配置创建完成！");
            logger.info("📋 这些配置将用于后续的权限验证测试");
            
        } catch (Exception e) {
            logger.error("创建测试配置失败", e);
            System.exit(1);
        }
    }
    
    private static void createTestConfigs() throws NacosException {
        logger.info("🔧 正在连接到Nacos服务器...");
        
        // 创建ConfigService
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", TARGET_NAMESPACE);
        
        ConfigService configService = NacosFactory.createConfigService(properties);
        logger.info("✅ 成功连接到Nacos服务器");
        logger.info("📋 目标命名空间: {}", TARGET_NAMESPACE);
        logger.info("👤 用户: {}", USERNAME);
        logger.info("");
        
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < TEST_CONFIGS.length; i++) {
            TestConfig config = TEST_CONFIGS[i];
            
            logger.info("📝 创建配置 {}/{}: {}", i + 1, TEST_CONFIGS.length, config.dataId);
            logger.info("  - 分组: {}", config.group);
            logger.info("  - 内容长度: {} 字符", config.content.length());
            
            try {
                long startTime = System.currentTimeMillis();
                boolean result = configService.publishConfig(config.dataId, config.group, config.content);
                long endTime = System.currentTimeMillis();
                
                if (result) {
                    logger.info("  ✅ 创建成功 (耗时: {}ms)", endTime - startTime);
                    successCount++;
                    
                    // 验证配置是否真的被创建
                    Thread.sleep(500); // 等待一下确保配置已保存
                    String retrievedContent = configService.getConfig(config.dataId, config.group, 3000);
                    if (retrievedContent != null && retrievedContent.equals(config.content)) {
                        logger.info("  ✅ 配置验证成功，内容匹配");
                    } else {
                        logger.warn("  ⚠️ 配置验证失败，内容不匹配");
                    }
                    
                } else {
                    logger.error("  ❌ 创建失败");
                    failCount++;
                }
                
            } catch (NacosException e) {
                logger.error("  ❌ 创建失败: {} (错误代码: {})", e.getErrMsg(), e.getErrCode());
                failCount++;
            } catch (InterruptedException e) {
                logger.warn("  ⚠️ 验证过程被中断");
            }
            
            logger.info("");
        }
        
        // 输出创建结果汇总
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("📊 配置创建结果汇总");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("✅ 成功创建: {} 个配置", successCount);
        logger.info("❌ 创建失败: {} 个配置", failCount);
        logger.info("📈 成功率: {:.1f}%", (double) successCount / TEST_CONFIGS.length * 100);
        logger.info("");
        
        if (successCount > 0) {
            logger.info("📋 成功创建的配置列表:");
            for (TestConfig config : TEST_CONFIGS) {
                try {
                    String content = configService.getConfig(config.dataId, config.group, 3000);
                    if (content != null) {
                        logger.info("  ✅ {}.{} (内容长度: {} 字符)", 
                            config.group, config.dataId, content.length());
                    }
                } catch (NacosException e) {
                    // 忽略验证错误
                }
            }
        }
        
        logger.info("");
        logger.info("🎯 这些配置现在可以用于权限验证测试:");
        logger.info("  - 测试cf用户对cf-n12s命名空间的读写权限");
        logger.info("  - 测试其他用户对这些配置的访问权限");
        logger.info("  - 验证统一权限模型的正确性");
    }
}
