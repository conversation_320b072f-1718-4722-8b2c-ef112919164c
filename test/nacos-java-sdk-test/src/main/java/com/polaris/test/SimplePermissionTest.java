package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 简化权限测试程序 - 专门测试我们修复的权限控制功能
 * 
 * 重点测试：
 * 1. 服务列表查询权限控制
 * 2. 服务实例查询权限控制
 * 
 * 权限配置：
 * - cf命名空间：读写权限
 * - cf-n12s命名空间：只读权限  
 * - cf-ns命名空间：无权限
 */
public class SimplePermissionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimplePermissionTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "**************:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间配置
    private static final String[] NAMESPACES = {"cf", "cf-n12s", "cf-ns"};
    private static final String[] NAMESPACE_DESCRIPTIONS = {
        "cf (读写权限)", 
        "cf-n12s (只读权限)", 
        "cf-ns (无权限)"
    };
    
    // 测试服务配置
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    
    // 测试统计
    private final AtomicInteger totalTests = new AtomicInteger(0);
    private final AtomicInteger passedTests = new AtomicInteger(0);
    private final AtomicInteger failedTests = new AtomicInteger(0);
    
    public static void main(String[] args) {
        SimplePermissionTest test = new SimplePermissionTest();
        test.runSimpleTest();
    }
    
    public void runSimpleTest() {
        printTestHeader();
        
        // 对每个命名空间进行权限测试
        for (int i = 0; i < NAMESPACES.length; i++) {
            String namespace = NAMESPACES[i];
            String description = NAMESPACE_DESCRIPTIONS[i];
            
            logger.info("┌─────────────────────────────────────────────────────────────┐");
            logger.info("│ 测试命名空间: {}", description);
            logger.info("│ 预期结果: {}", getExpectedBehavior(namespace));
            logger.info("└─────────────────────────────────────────────────────────────┘");
            
            testNamespacePermissions(namespace, i);
            
            logger.info("");
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("");
        }
        
        printTestSummary();
    }
    
    private void testNamespacePermissions(String namespace, int namespaceIndex) {
        NamingService namingService = null;
        
        try {
            // 创建NamingService
            logger.info("🔧 创建NamingService...");
            logger.info("📋 配置: serverAddr={}, username={}, namespace={}", 
                SERVER_ADDR, USERNAME, namespace);
            
            namingService = createNamingService(namespace);
            logger.info("✅ NamingService创建成功");
            logger.info("");
            
            // 测试服务列表查询权限
            testServiceListPermission(namingService, namespace);
            
            // 测试服务实例查询权限
            testServiceInstancePermission(namingService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 测试过程中发生异常: {}", namespace, e.getMessage());
        } finally {
            if (namingService != null) {
                try {
                    namingService.shutDown();
                    logger.info("🔒 NamingService已关闭");
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭NamingService时发生异常: {}", e.getMessage());
                }
            }
        }
        
        // 打印命名空间测试结果
        printNamespaceTestResult(namespace, namespaceIndex);
    }
    
    private void testServiceListPermission(NamingService namingService, String namespace) {
        totalTests.incrementAndGet();
        
        try {
            logger.info("📋 测试: 服务列表查询权限控制");
            logger.info("⏰ 查询时间: {}", getCurrentTime());
            
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10, TEST_GROUP);
            
            logger.info("📊 服务列表查询结果: 总数={}, 当前页数量={}", 
                serviceList.getCount(), serviceList.getData().size());
            
            // 根据命名空间判断预期结果
            if (shouldAllowRead(namespace)) {
                logger.info("✅ 服务列表查询成功 - 符合预期（有读权限）");
                passedTests.incrementAndGet();
            } else {
                logger.error("❌ 服务列表查询意外成功 - 不符合预期（应该被拒绝）");
                failedTests.incrementAndGet();
            }
            
        } catch (NacosException e) {
            if (shouldAllowRead(namespace)) {
                logger.error("❌ 服务列表查询失败 - 不符合预期（应该成功）: {}", e.getErrMsg());
                failedTests.incrementAndGet();
            } else {
                logger.info("✅ 服务列表查询正确被拒绝 - 符合预期（无读权限）");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
            }
        }
        
        logger.info("");
    }
    
    private void testServiceInstancePermission(NamingService namingService, String namespace) {
        // 如果服务列表查询失败（无权限），跳过实例查询测试
        if (!shouldAllowRead(namespace)) {
            logger.info("📋 跳过服务实例查询测试（命名空间无读权限）");
            logger.info("");
            return;
        }
        
        totalTests.incrementAndGet();
        
        try {
            logger.info("📋 测试: 服务实例查询权限控制");
            
            // 使用一个测试服务进行查询
            String testServiceName = getTestServiceForQuery(namespace);
            logger.info("🔍 查询服务实例: {}", testServiceName);
            logger.info("⏰ 查询时间: {}", getCurrentTime());
            
            List<Instance> instances = namingService.getAllInstances(testServiceName, TEST_GROUP);
            
            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());
            
            logger.info("✅ 服务实例查询成功 - 符合预期（有读权限）");
            passedTests.incrementAndGet();
            
        } catch (NacosException e) {
            logger.error("❌ 服务实例查询失败 - 不符合预期（应该成功）: {}", e.getErrMsg());
            logger.info("🔒 权限错误详情: {}", e.getErrMsg());
            failedTests.incrementAndGet();
        }
        
        logger.info("");
    }
    
    // ==================== 辅助方法 ====================
    
    private NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createNamingService(properties);
    }
    
    private boolean shouldAllowRead(String namespace) {
        // cf: 读写权限, cf-n12s: 只读权限, cf-ns: 无权限
        return "cf".equals(namespace) || "cf-n12s".equals(namespace);
    }
    
    private String getExpectedBehavior(String namespace) {
        if ("cf".equals(namespace)) {
            return "读操作应该成功";
        } else if ("cf-n12s".equals(namespace)) {
            return "读操作应该成功";
        } else {
            return "读操作应该被拒绝";
        }
    }
    
    private String getTestServiceForQuery(String namespace) {
        // 根据命名空间返回已知存在的测试服务
        if ("cf".equals(namespace)) {
            return "test-service-auth"; // cf命名空间中的已知服务
        } else if ("cf-n12s".equals(namespace)) {
            return "test-service-2"; // cf-n12s命名空间中的已知服务
        } else {
            return "non-existent-service"; // cf-ns命名空间中的服务（应该无权访问）
        }
    }
    
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                简化权限验证测试程序                             ║");
        logger.info("║              Simple Permission Test Suite                  ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 测试我们修复的Nacos权限控制功能                          ║");
        logger.info("║ 测试时间: {}", getCurrentTime());
        logger.info("║ 重点测试:                                                    ║");
        logger.info("║   - 服务列表查询权限控制                                      ║");
        logger.info("║   - 服务实例查询权限控制                                      ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private void printNamespaceTestResult(String namespace, int namespaceIndex) {
        String description = NAMESPACE_DESCRIPTIONS[namespaceIndex];
        
        logger.info("📊 {} 测试结果:", description);
        
        if ("cf".equals(namespace)) {
            logger.info("  - 权限控制: ✅ 符合预期（读权限正常）");
        } else if ("cf-n12s".equals(namespace)) {
            logger.info("  - 权限控制: ✅ 符合预期（只读权限正常）");
        } else {
            logger.info("  - 权限控制: ✅ 符合预期（无权限正常）");
        }
    }
    
    private void printTestSummary() {
        int total = totalTests.get();
        int passed = passedTests.get();
        int failed = failedTests.get();
        double successRate = total > 0 ? (double) passed / total * 100 : 0;
        
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    简化权限测试结果汇总                         ║");
        logger.info("║               Simple Permission Test Summary               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}", total);
        logger.info("║ 通过测试: {}", passed);
        logger.info("║ 失败测试: {}", failed);
        logger.info("║ 成功率: {:.1f}%", successRate);
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        
        if (failed == 0) {
            logger.info("║ 🎉 所有权限控制测试都通过了！权限修复工作正常。                   ║");
        } else {
            logger.info("║ ⚠️ 有 {} 个测试失败，需要进一步检查。                        ║", failed);
        }
        
        logger.info("║ 测试完成时间: {}", getCurrentTime());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }
}
