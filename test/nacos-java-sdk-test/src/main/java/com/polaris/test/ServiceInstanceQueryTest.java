package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * 完整的权限验证测试程序
 * 测试不同权限级别下的Nacos读操作访问控制
 */
public class ServiceInstanceQueryTest {

    private static final Logger logger = LoggerFactory.getLogger(ServiceInstanceQueryTest.class);

    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";

    // 测试统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;

    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                完整权限验证测试程序                             ║");
        logger.info("║           Complete Permission Validation Test               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 验证不同权限级别下的Nacos读操作访问控制                    ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");

        try {
            // 测试cf命名空间（读写权限）
            testReadWritePermissionNamespace();

            logger.info("");
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("");

            // 测试cf-n12s命名空间（只读权限）
            testReadOnlyPermissionNamespace();

            logger.info("");
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("");

            // 测试cf-ns命名空间（无权限）
            testNoPermissionNamespace();

        } catch (Exception e) {
            logger.error("测试执行失败", e);
        }

        // 输出测试结果汇总
        printTestSummary();
    }

    /**
     * 测试读写权限命名空间（cf）
     * 预期：所有读操作都应该成功
     */
    private static void testReadWritePermissionNamespace() {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ 测试命名空间: cf (读写权限)");
        logger.info("│ 预期结果: 所有读操作都应该成功");
        logger.info("└─────────────────────────────────────────────────────────────┘");

        PermissionTestResult result = performReadOperationTests("cf", PermissionLevel.READ_WRITE);

        logger.info("📊 cf命名空间测试结果:");
        logger.info("  - 服务列表查询: {}", result.serviceListQuery ? "✅ 成功" : "❌ 失败");
        logger.info("  - 服务实例查询: {}/{} 成功", result.successfulInstanceQueries, result.totalInstanceQueries);
        logger.info("  - 整体评估: {}", result.overallSuccess ? "✅ 符合预期" : "❌ 不符合预期");
    }

    /**
     * 测试只读权限命名空间（cf-n12s）
     * 预期：所有读操作都应该成功
     */
    private static void testReadOnlyPermissionNamespace() {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ 测试命名空间: cf-n12s (只读权限)");
        logger.info("│ 预期结果: 所有读操作都应该成功");
        logger.info("└─────────────────────────────────────────────────────────────┘");

        PermissionTestResult result = performReadOperationTests("cf-n12s", PermissionLevel.READ_ONLY);

        logger.info("📊 cf-n12s命名空间测试结果:");
        logger.info("  - 服务列表查询: {}", result.serviceListQuery ? "✅ 成功" : "❌ 失败");
        logger.info("  - 服务实例查询: {}/{} 成功", result.successfulInstanceQueries, result.totalInstanceQueries);
        logger.info("  - 整体评估: {}", result.overallSuccess ? "✅ 符合预期" : "❌ 不符合预期");
    }

    /**
     * 测试无权限命名空间（cf-ns）
     * 预期：所有读操作都应该被拒绝
     */
    private static void testNoPermissionNamespace() {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ 测试命名空间: cf-ns (无权限)");
        logger.info("│ 预期结果: 所有读操作都应该被拒绝");
        logger.info("└─────────────────────────────────────────────────────────────┘");

        PermissionTestResult result = performReadOperationTests("cf-ns", PermissionLevel.NO_PERMISSION);

        logger.info("📊 cf-ns命名空间测试结果:");
        logger.info("  - 服务列表查询: {}", result.serviceListQuery ? "❌ 意外成功" : "✅ 正确拒绝");
        logger.info("  - 服务实例查询: {}/{} 被拒绝", result.totalInstanceQueries - result.successfulInstanceQueries, result.totalInstanceQueries);
        logger.info("  - 整体评估: {}", result.overallSuccess ? "✅ 符合预期" : "❌ 不符合预期");
    }

    /**
     * 执行读操作测试
     */
    private static PermissionTestResult performReadOperationTests(String namespace, PermissionLevel expectedPermission) {
        PermissionTestResult result = new PermissionTestResult();
        NamingService namingService = null;

        try {
            // 创建NamingService
            Properties properties = new Properties();
            properties.setProperty("serverAddr", SERVER_ADDR);
            properties.setProperty("username", USERNAME);
            properties.setProperty("password", PASSWORD);
            properties.setProperty("namespace", namespace);

            logger.info("🔧 创建NamingService...");
            logger.info("📋 配置: serverAddr={}, username={}, namespace={}", SERVER_ADDR, USERNAME, namespace);

            namingService = NacosFactory.createNamingService(properties);
            logger.info("✅ NamingService创建成功");

            // 测试1: 服务列表查询
            logger.info("");
            logger.info("📋 测试1: 服务列表查询 (getServicesOfServer)");
            result.serviceListQuery = testServiceListQuery(namingService, namespace, expectedPermission);
            totalTests++;
            if (result.serviceListQuery == (expectedPermission != PermissionLevel.NO_PERMISSION)) {
                passedTests++;
            } else {
                failedTests++;
            }

            // 如果服务列表查询成功，继续测试服务实例查询
            if (result.serviceListQuery) {
                logger.info("");
                logger.info("📋 测试2: 服务实例查询 (getAllInstances)");
                testServiceInstanceQueries(namingService, namespace, expectedPermission, result);
            } else if (expectedPermission != PermissionLevel.NO_PERMISSION) {
                logger.warn("⚠️ 服务列表查询失败，跳过服务实例查询测试");
            }

        } catch (Exception e) {
            logger.error("❌ 测试命名空间 {} 失败: {}", namespace, e.getMessage());
            if (isPermissionError(e)) {
                logger.info("🔒 检测到权限错误: {}", e.getMessage());
                result.serviceListQuery = false;
            }
        } finally {
            if (namingService != null) {
                try {
                    namingService.shutDown();
                    logger.info("🔒 NamingService已关闭");
                } catch (Exception e) {
                    logger.warn("关闭NamingService时出现警告: {}", e.getMessage());
                }
            }
        }

        // 评估整体结果
        result.overallSuccess = evaluateOverallResult(result, expectedPermission);

        return result;
    }

    /**
     * 测试服务列表查询
     */
    private static boolean testServiceListQuery(NamingService namingService, String namespace, PermissionLevel expectedPermission) {
        try {
            logger.info("⏰ 查询时间: {}", java.time.LocalDateTime.now());
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);
            logger.info("📊 服务列表查询结果: 总数={}, 当前页数量={}", serviceList.getCount(), serviceList.getData().size());

            if (!serviceList.getData().isEmpty()) {
                logger.info("📋 找到的服务列表:");
                for (int i = 0; i < Math.min(serviceList.getData().size(), 5); i++) {
                    logger.info("  服务[{}]: {}", i + 1, serviceList.getData().get(i));
                }
                if (serviceList.getData().size() > 5) {
                    logger.info("  ... 还有 {} 个服务", serviceList.getData().size() - 5);
                }
            }

            logger.info("✅ 服务列表查询成功");
            return true;

        } catch (Exception e) {
            logger.error("❌ 服务列表查询失败: {}", e.getMessage());
            if (isPermissionError(e)) {
                logger.info("🔒 权限错误详情: {}", e.getMessage());
            }
            return false;
        }
    }

    /**
     * 测试服务实例查询
     */
    private static void testServiceInstanceQueries(NamingService namingService, String namespace,
                                                  PermissionLevel expectedPermission, PermissionTestResult result) {
        try {
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);
            List<String> services = serviceList.getData();

            if (services.isEmpty()) {
                logger.warn("⚠️ 命名空间 {} 中没有找到任何服务", namespace);
                return;
            }

            result.totalInstanceQueries = services.size();

            // 对每个服务执行实例查询
            for (String serviceName : services) {
                logger.info("");
                logger.info("🔍 查询服务实例: {}", serviceName);
                logger.info("⏰ 查询时间: {}", java.time.LocalDateTime.now());
                logger.info("📋 请求参数: serviceName={}, namespace={}", serviceName, namespace);

                boolean success = testSingleServiceInstanceQuery(namingService, serviceName, expectedPermission);
                if (success) {
                    result.successfulInstanceQueries++;
                }

                totalTests++;
                if (success == (expectedPermission != PermissionLevel.NO_PERMISSION)) {
                    passedTests++;
                } else {
                    failedTests++;
                }

                // 添加短暂延迟，便于日志分析
                Thread.sleep(300);
            }

        } catch (Exception e) {
            logger.error("❌ 服务实例查询测试失败: {}", e.getMessage());
        }
    }

    /**
     * 测试单个服务的实例查询
     */
    private static boolean testSingleServiceInstanceQuery(NamingService namingService, String serviceName,
                                                         PermissionLevel expectedPermission) {
        try {
            // 执行服务实例查询 - 这是关键操作
            List<Instance> instances = namingService.getAllInstances(serviceName);

            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());

            if (!instances.isEmpty()) {
                for (int i = 0; i < Math.min(instances.size(), 2); i++) { // 最多显示2个实例
                    Instance instance = instances.get(i);
                    logger.info("  实例[{}]: IP={}, Port={}, Weight={}, Healthy={}, Enabled={}",
                        i + 1, instance.getIp(), instance.getPort(), instance.getWeight(),
                        instance.isHealthy(), instance.isEnabled());
                }
                if (instances.size() > 2) {
                    logger.info("  ... 还有 {} 个实例", instances.size() - 2);
                }
            }

            logger.info("✅ 服务实例查询 [{}] - 成功, 实例数量: {}", serviceName, instances.size());
            return true;

        } catch (Exception e) {
            logger.error("❌ 服务实例查询 [{}] - 失败: {}", serviceName, e.getMessage());
            if (isPermissionError(e)) {
                logger.info("🔒 权限错误详情: {}", e.getMessage());
            }
            return false;
        }
    }

    /**
     * 判断是否是权限错误
     */
    private static boolean isPermissionError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        return message.contains("permission") ||
               message.contains("权限") ||
               message.contains("access denied") ||
               message.contains("unauthorized") ||
               message.contains("403") ||
               message.contains("No permission");
    }

    /**
     * 评估整体测试结果
     */
    private static boolean evaluateOverallResult(PermissionTestResult result, PermissionLevel expectedPermission) {
        switch (expectedPermission) {
            case READ_WRITE:
            case READ_ONLY:
                // 有权限的情况：所有操作都应该成功
                return result.serviceListQuery &&
                       (result.totalInstanceQueries == 0 || result.successfulInstanceQueries == result.totalInstanceQueries);

            case NO_PERMISSION:
                // 无权限的情况：所有操作都应该失败
                return !result.serviceListQuery && result.successfulInstanceQueries == 0;

            default:
                return false;
        }
    }

    /**
     * 打印测试结果汇总
     */
    private static void printTestSummary() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    权限验证测试结果汇总                         ║");
        logger.info("║              Permission Validation Test Summary             ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}", String.format("%2d", totalTests));
        logger.info("║ 通过测试: {}", String.format("%2d", passedTests));
        logger.info("║ 失败测试: {}", String.format("%2d", failedTests));
        logger.info("║ 成功率: {}%", String.format("%3.1f", totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0));
        logger.info("╠══════════════════════════════════════════════════════════════╣");

        if (failedTests == 0) {
            logger.info("║ 🎉 所有权限验证测试都通过了！权限控制工作正常。                   ║");
        } else {
            logger.info("║ ⚠️  有 {} 个测试失败，权限控制可能存在问题。", String.format("%2d", failedTests));
        }

        logger.info("║ 测试完成时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }


    /**
     * 权限级别枚举
     */
    enum PermissionLevel {
        READ_WRITE,    // 读写权限
        READ_ONLY,     // 只读权限
        NO_PERMISSION  // 无权限
    }

    /**
     * 权限测试结果
     */
    static class PermissionTestResult {
        boolean serviceListQuery = false;           // 服务列表查询结果
        int totalInstanceQueries = 0;               // 总的实例查询数
        int successfulInstanceQueries = 0;          // 成功的实例查询数
        boolean overallSuccess = false;             // 整体测试是否符合预期
    }
}