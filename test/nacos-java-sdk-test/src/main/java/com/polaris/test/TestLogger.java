package com.polaris.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试日志工具类
 * 提供详细的中文日志记录功能
 */
public class TestLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(TestLogger.class);
    
    // 日志标记
    private static final Marker SUMMARY_MARKER = MarkerFactory.getMarker("SUMMARY");
    private static final Marker PERMISSION_MARKER = MarkerFactory.getMarker("PERMISSION");
    
    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 记录测试开始信息
     */
    public static void logTestStart(String testName) {
        String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
        String message = String.format("🚀 开始执行测试: %s (时间: %s)", testName, timestamp);
        
        logger.info(message);
        logger.info(SUMMARY_MARKER, message);
        logger.info(PERMISSION_MARKER, message);
        
        // 打印分隔线
        String separator = "=".repeat(80);
        logger.info(separator);
        logger.info(PERMISSION_MARKER, separator);
    }
    
    /**
     * 记录测试结束信息
     */
    public static void logTestEnd(String testName, boolean success, String message) {
        String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
        String status = success ? "✅ 成功" : "❌ 失败";
        String logMessage = String.format("🏁 测试完成: %s - %s (时间: %s)", testName, status, timestamp);
        
        if (success) {
            logger.info(logMessage);
            logger.info("📝 结果详情: {}", message);
        } else {
            logger.error(logMessage);
            logger.error("📝 失败原因: {}", message);
        }
        
        // 记录到摘要日志
        logger.info(SUMMARY_MARKER, "{} - {} - {}", testName, status, message);
        
        // 记录到权限日志
        logger.info(PERMISSION_MARKER, "{} - {} - {}", testName, status, message);
        
        // 打印分隔线
        String separator = "-".repeat(80);
        logger.info(separator);
        logger.info(PERMISSION_MARKER, separator);
    }
    
    /**
     * 记录权限检查信息
     */
    public static void logPermissionCheck(String operation, String namespace, String user, boolean hasPermission) {
        String status = hasPermission ? "✅ 有权限" : "❌ 无权限";
        String message = String.format("🔐 权限检查 - 操作: %s, 命名空间: %s, 用户: %s, 结果: %s", 
                                      operation, namespace, user, status);
        
        logger.info(message);
        logger.info(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录服务操作信息
     */
    public static void logServiceOperation(String operation, String serviceName, String namespace, 
                                         String ip, int port, boolean success) {
        String status = success ? "✅ 成功" : "❌ 失败";
        String message = String.format("🔧 服务操作 - %s: 服务=%s, 命名空间=%s, 地址=%s:%d, 结果=%s", 
                                      operation, serviceName, namespace, ip, port, status);
        
        if (success) {
            logger.info(message);
        } else {
            logger.warn(message);
        }
        
        logger.info(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录权限绕过检测信息
     */
    public static void logPermissionBypassCheck(String testName, boolean bypassDetected, String details) {
        if (bypassDetected) {
            String message = String.format("🚨 权限绕过检测 - 测试: %s, 状态: 发现绕过, 详情: %s", 
                                          testName, details);
            logger.error(message);
            logger.error(SUMMARY_MARKER, "权限绕过漏洞: {} - {}", testName, details);
            logger.error(PERMISSION_MARKER, message);
        } else {
            String message = String.format("🛡️ 权限绕过检测 - 测试: %s, 状态: 安全正常, 详情: %s", 
                                          testName, details);
            logger.info(message);
            logger.info(PERMISSION_MARKER, message);
        }
    }
    
    /**
     * 记录测试环境信息
     */
    public static void logEnvironmentInfo(String serverAddr, String user, String authorizedNs, String unauthorizedNs) {
        logger.info("🌍 测试环境配置:");
        logger.info("   📡 服务端地址: {}", serverAddr);
        logger.info("   👤 测试用户: {}", user);
        logger.info("   ✅ 有权限命名空间: {}", authorizedNs);
        logger.info("   ❌ 无权限命名空间: {}", unauthorizedNs);
        
        String envInfo = String.format("测试环境 - 服务端: %s, 用户: %s, 有权限NS: %s, 无权限NS: %s", 
                                      serverAddr, user, authorizedNs, unauthorizedNs);
        logger.info(SUMMARY_MARKER, envInfo);
        logger.info(PERMISSION_MARKER, envInfo);
    }
    
    /**
     * 记录异常信息
     */
    public static void logException(String context, Exception e) {
        String message = String.format("⚠️ 异常发生 - 上下文: %s, 异常类型: %s, 异常消息: %s", 
                                      context, e.getClass().getSimpleName(), e.getMessage());
        logger.error(message, e);
        logger.error(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录网络连接信息
     */
    public static void logNetworkConnection(String serverAddr, boolean connected, String details) {
        String status = connected ? "✅ 连接成功" : "❌ 连接失败";
        String message = String.format("🌐 网络连接 - 服务端: %s, 状态: %s, 详情: %s", 
                                      serverAddr, status, details);
        
        if (connected) {
            logger.info(message);
        } else {
            logger.error(message);
        }
        
        logger.info(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录测试步骤信息
     */
    public static void logTestStep(String stepName, String description) {
        String message = String.format("📋 测试步骤: %s - %s", stepName, description);
        logger.info(message);
        logger.info(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录测试数据信息
     */
    public static void logTestData(String dataType, Object data) {
        String message = String.format("📊 测试数据 - %s: %s", dataType, data.toString());
        logger.debug(message);
        logger.debug(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录性能信息
     */
    public static void logPerformance(String operation, long durationMs) {
        String message = String.format("⏱️ 性能统计 - 操作: %s, 耗时: %d ms", operation, durationMs);
        logger.info(message);
        logger.info(PERMISSION_MARKER, message);
    }
    
    /**
     * 记录测试总结信息
     */
    public static void logTestSummary(int totalTests, int passedTests, int failedTests, double passRate) {
        String message = String.format("📈 测试总结 - 总数: %d, 通过: %d, 失败: %d, 通过率: %.1f%%", 
                                      totalTests, passedTests, failedTests, passRate);
        
        logger.info(message);
        logger.info(SUMMARY_MARKER, message);
        logger.info(PERMISSION_MARKER, message);
        
        if (failedTests == 0) {
            String successMessage = "🎉 所有测试都通过了！权限控制功能正常工作。";
            logger.info(successMessage);
            logger.info(SUMMARY_MARKER, successMessage);
        } else {
            String failureMessage = String.format("⚠️ 有 %d 个测试失败，请检查权限控制功能。", failedTests);
            logger.warn(failureMessage);
            logger.warn(SUMMARY_MARKER, failureMessage);
        }
    }
    
    /**
     * 记录重要提示信息
     */
    public static void logImportantNote(String note) {
        String message = String.format("💡 重要提示: %s", note);
        logger.warn(message);
        logger.warn(SUMMARY_MARKER, message);
        logger.warn(PERMISSION_MARKER, message);
    }
}
