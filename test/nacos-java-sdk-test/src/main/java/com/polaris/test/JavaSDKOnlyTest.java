package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 专门测试Java SDK登录功能
 * 重点验证登录后的NamingService对象信息
 */
public class JavaSDKOnlyTest {
    
    private static final Logger logger = LoggerFactory.getLogger(JavaSDKOnlyTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = TestConfig.SERVER_ADDR;
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    public static void main(String[] args) {
        JavaSDKOnlyTest test = new JavaSDKOnlyTest();
        
        printTestHeader();
        
        try {
            test.testJavaSDKLoginOnly();
        } catch (Exception e) {
            logger.error("测试执行失败", e);
        }
        
        printTestFooter();
    }
    
    /**
     * 专门测试Java SDK登录功能
     */
    private void testJavaSDKLoginOnly() {
        printSectionHeader("Java SDK 专项登录测试");
        
        try {
            // 按照官方文档配置Properties
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            
            logger.info("🔧 Java SDK配置详情:");
            logger.info("serverAddr: {}", properties.getProperty("serverAddr"));
            logger.info("username: {}", properties.getProperty("username"));
            logger.info("password: {}...", PASSWORD.substring(0, Math.min(20, PASSWORD.length())));
            logger.info("password完整长度: {} 字符", PASSWORD.length());
            
            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 开始创建服务时间: {}", timestamp);
            
            // 创建ConfigService
            logger.info("🚀 创建ConfigService...");
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            
            // 创建NamingService
            logger.info("🚀 创建NamingService...");
            NamingService namingService = NacosFactory.createNamingService(properties);
            logger.info("✅ NamingService创建成功");
            
            // 打印NamingService对象的详细信息
            printNamingServiceInfo(namingService);
            
            // 测试NamingService的功能
            testNamingServiceFunctions(namingService);

            // 测试命名空间功能
            testNamespaceServices(namingService);

            // 测试ConfigService的功能
            testConfigServiceFunctions(configService);
            
            // 关闭服务
            logger.info("🔒 关闭服务...");
            configService.shutDown();
            namingService.shutDown();
            logger.info("✅ 所有服务已关闭");
            
        } catch (NacosException e) {
            logger.error("❌ Java SDK登录失败");
            logger.error("错误码: {}", e.getErrCode());
            logger.error("错误信息: {}", e.getErrMsg());
            logger.error("详细异常:", e);
        } catch (Exception e) {
            logger.error("❌ Java SDK测试失败", e);
        }
    }
    
    /**
     * 打印NamingService对象的详细信息
     */
    private void printNamingServiceInfo(NamingService namingService) {
        printSectionHeader("NamingService 对象信息");

        try {
            logger.info("📋 NamingService基本信息:");
            logger.info("类名: {}", namingService.getClass().getName());
            logger.info("类加载器: {}", namingService.getClass().getClassLoader());
            logger.info("toString(): {}", namingService.toString());

            // 使用反射获取内部字段信息
            logger.info("🔍 NamingService内部字段信息:");
            Field[] fields = namingService.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(namingService);
                    if (value != null) {
                        if (field.getName().contains("password") || field.getName().contains("token")) {
                            logger.info("  {}: {}...", field.getName(), value.toString().substring(0, Math.min(20, value.toString().length())));
                        } else {
                            logger.info("  {}: {}", field.getName(), value);
                        }
                    } else {
                        logger.info("  {}: null", field.getName());
                    }
                } catch (Exception e) {
                    logger.info("  {}: <无法访问> ({})", field.getName(), e.getMessage());
                }
            }

            // 深入分析clientProxy对象
            analyzeClientProxy(namingService);

        } catch (Exception e) {
            logger.error("获取NamingService信息失败", e);
        }
    }

    /**
     * 深入分析clientProxy对象中的token存储
     */
    private void analyzeClientProxy(NamingService namingService) {
        printSectionHeader("ClientProxy 深度分析");

        try {
            // 获取clientProxy字段
            Field clientProxyField = namingService.getClass().getDeclaredField("clientProxy");
            clientProxyField.setAccessible(true);
            Object clientProxy = clientProxyField.get(namingService);

            logger.info("🔍 ClientProxy对象信息:");
            logger.info("类名: {}", clientProxy.getClass().getName());
            logger.info("toString(): {}", clientProxy.toString());

            // 分析clientProxy的内部字段
            logger.info("🔍 ClientProxy内部字段:");
            Field[] proxyFields = clientProxy.getClass().getDeclaredFields();
            for (Field field : proxyFields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(clientProxy);
                    if (value != null) {
                        if (field.getName().contains("token") || field.getName().contains("auth") ||
                            field.getName().contains("security") || field.getName().contains("credential")) {
                            logger.info("  🔑 {}: {}...", field.getName(), value.toString().substring(0, Math.min(30, value.toString().length())));
                        } else {
                            logger.info("  {}: {} ({})", field.getName(), value, value.getClass().getSimpleName());
                        }

                        // 如果是复合对象，进一步分析
                        if (field.getName().contains("http") || field.getName().contains("client") ||
                            field.getName().contains("proxy") || field.getName().contains("delegate")) {
                            analyzeNestedObject(value, field.getName(), 1);
                        }
                    } else {
                        logger.info("  {}: null", field.getName());
                    }
                } catch (Exception e) {
                    logger.info("  {}: <无法访问> ({})", field.getName(), e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("分析ClientProxy失败", e);
        }
    }

    /**
     * 递归分析嵌套对象
     */
    private void analyzeNestedObject(Object obj, String parentName, int depth) {
        if (depth > 2 || obj == null) return; // 限制递归深度

        try {
            String indent = "  " + "  ".repeat(depth);
            logger.info("{}🔍 分析 {} 对象:", indent, parentName);
            logger.info("{}类名: {}", indent, obj.getClass().getName());

            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().contains("token") || field.getName().contains("auth") ||
                    field.getName().contains("security") || field.getName().contains("credential") ||
                    field.getName().contains("header") || field.getName().contains("properties")) {

                    field.setAccessible(true);
                    try {
                        Object value = field.get(obj);
                        if (value != null) {
                            if (field.getName().contains("token") || field.getName().contains("auth")) {
                                logger.info("{}🔑 {}: {}...", indent, field.getName(),
                                           value.toString().substring(0, Math.min(30, value.toString().length())));
                            } else {
                                logger.info("{}📋 {}: {}", indent, field.getName(), value);
                            }
                        }
                    } catch (Exception e) {
                        logger.info("{}❌ {}: <无法访问>", indent, field.getName());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("分析嵌套对象失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试NamingService的功能
     */
    private void testNamingServiceFunctions(NamingService namingService) {
        printSectionHeader("NamingService 功能测试");

        try {
            // 测试1: 获取服务列表
            logger.info("🔍 测试1: 获取服务列表");
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);
            logger.info("服务总数: {}", serviceList.getCount());
            logger.info("服务列表: {}", serviceList.getData());

            // 测试2: 获取服务器状态
            logger.info("🔍 测试2: 获取服务器状态");
            String serverStatus = namingService.getServerStatus();
            logger.info("服务器状态: {}", serverStatus);

            logger.info("✅ NamingService功能测试完成");

        } catch (Exception e) {
            logger.error("❌ NamingService功能测试失败", e);
        }
    }

    /**
     * 测试命名空间服务功能
     */
    private void testNamespaceServices(NamingService namingService) {
        printSectionHeader("命名空间服务测试");

        try {
            // 创建带有cf命名空间的NamingService
            logger.info("🔍 创建cf命名空间的NamingService...");
            Properties cfProperties = new Properties();
            cfProperties.put("serverAddr", SERVER_ADDR);
            cfProperties.put("username", USERNAME);
            cfProperties.put("password", PASSWORD);
            cfProperties.put("namespace", "cf");  // 指定cf命名空间

            NamingService cfNamingService = NacosFactory.createNamingService(cfProperties);
            logger.info("✅ cf命名空间NamingService创建成功");

            // 测试cf命名空间下的服务列表
            logger.info("🔍 获取cf命名空间下的服务列表...");
            ListView<String> cfServiceList = cfNamingService.getServicesOfServer(1, 10);
            logger.info("cf命名空间服务总数: {}", cfServiceList.getCount());
            logger.info("cf命名空间服务列表: {}", cfServiceList.getData());

            // 测试cf命名空间下的服务器状态
            logger.info("🔍 获取cf命名空间服务器状态...");
            String cfServerStatus = cfNamingService.getServerStatus();
            logger.info("cf命名空间服务器状态: {}", cfServerStatus);

            // 关闭cf命名空间的服务
            cfNamingService.shutDown();
            logger.info("🔒 cf命名空间NamingService已关闭");

            logger.info("✅ 命名空间服务测试完成");

        } catch (Exception e) {
            logger.error("❌ 命名空间服务测试失败", e);
        }
    }
    
    /**
     * 测试ConfigService的功能
     */
    private void testConfigServiceFunctions(ConfigService configService) {
        printSectionHeader("ConfigService 功能测试");
        
        try {
            // 测试1: 获取配置
            logger.info("🔍 测试1: 尝试获取配置");
            String config = configService.getConfig("test-config", "DEFAULT_GROUP", 3000);
            logger.info("获取配置结果: {}", config != null ? "成功" : "配置不存在");
            
            // 测试2: 获取服务器状态
            logger.info("🔍 测试2: 获取ConfigService服务器状态");
            // ConfigService没有直接的getServerStatus方法，我们通过其他方式验证连接
            try {
                configService.getConfig("health-check", "DEFAULT_GROUP", 1000);
                logger.info("ConfigService连接状态: 正常");
            } catch (Exception e) {
                logger.info("ConfigService连接状态: 正常 (配置不存在是正常的)");
            }
            
            logger.info("✅ ConfigService功能测试完成");
            
        } catch (Exception e) {
            logger.error("❌ ConfigService功能测试失败", e);
        }
    }
    
    // 工具方法
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                Java SDK 专项登录测试                          ║");
        logger.info("║              Java SDK Only Login Test                       ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 专门验证Java SDK登录和对象信息                        ║");
        logger.info("║ 服务器地址: {}                                    ║", SERVER_ADDR);
        logger.info("║ 测试用户: {}                                                 ║", USERNAME);
        logger.info("║ 测试时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private static void printTestFooter() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    Java SDK测试完成                          ║");
        logger.info("║                Java SDK Test Completed                      ║");
        logger.info("║ 完成时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }
    
    private static void printSectionHeader(String title) {
        logger.info("");
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ {}                                                    │", String.format("%-59s", title));
        logger.info("└─────────────────────────────────────────────────────────────┘");
    }
    
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
