package com.polaris.test;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * 增强版客户端鉴权测试
 * 包含详细的服务端响应分析和日志监控提示
 */
public class EnhancedClientAuthTest {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedClientAuthTest.class);
    
    // 测试结果统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    private static final List<String> testResults = new ArrayList<>();
    
    public static void main(String[] args) {
        printTestHeader();
        
        try {
            // 打印测试配置
            TestUserConfig.printConfig();
            
            // 提示用户准备日志监控
            promptLogMonitoring();
            
            // 执行权限验证测试
            runPermissionTests();
            
            // 输出测试总结
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("❌ 增强版客户端鉴权测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 提示用户准备日志监控
     */
    private static void promptLogMonitoring() {
        logger.info("🔍 准备开始测试，建议同时监控服务端日志");
        logger.info("📋 请在另一个终端窗口运行以下命令监控服务端日志:");
        logger.info("   kubectl logs -f registry-server-0 -n mse-zdu1rgey");
        logger.info("");
        logger.info("⏳ 请确认日志监控已开始，然后按回车键继续测试...");
        
        Scanner scanner = new Scanner(System.in);
        scanner.nextLine();
        
        logger.info("🚀 开始执行测试...");
        logger.info("");
    }
    
    /**
     * 执行权限验证测试
     */
    private static void runPermissionTests() {
        logger.info("🔍 开始执行增强版权限验证测试...");
        logger.info("");
        
        String[] namespaces = TestUserConfig.getAllNamespaces();
        
        for (String namespace : namespaces) {
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("🎯 测试命名空间: {} ({})", namespace, TestUserConfig.getNamespacePermission(namespace));
            logger.info("═══════════════════════════════════════════════════════════════");
            
            // 测试注册中心权限
            testNamingServiceWithDetails(namespace);
            
            // 测试配置中心权限
            testConfigServiceWithDetails(namespace);
            
            logger.info("");
            logger.info("⏸️ 命名空间 {} 测试完成，请检查服务端日志", namespace);
            logger.info("按回车键继续下一个命名空间的测试...");
            Scanner scanner = new Scanner(System.in);
            scanner.nextLine();
            logger.info("");
        }
    }
    
    /**
     * 详细测试注册中心权限
     */
    private static void testNamingServiceWithDetails(String namespace) {
        logger.info("🚀 开始测试注册中心权限...");
        
        NamingService namingService = null;
        try {
            // 创建NamingService
            logger.info("📡 正在创建 NamingService 连接...");
            namingService = ClientAuthTestUtil.createNamingService(namespace);
            logger.info("✅ NamingService 创建成功");
            
            // 测试1: 服务列表查询（读权限）
            testServiceListQueryWithDetails(namingService, namespace);
            
            // 测试2: 服务注册（写权限）
            testServiceRegistrationWithDetails(namingService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 注册中心权限测试失败:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            logger.error("  - 完整异常: ", e);
            recordTestResult(namespace, "注册中心连接", false, e.getMessage());
        } finally {
            if (namingService != null) {
                ClientAuthTestUtil.shutdown(namingService);
            }
        }
    }
    
    /**
     * 详细测试配置中心权限
     */
    private static void testConfigServiceWithDetails(String namespace) {
        logger.info("📝 开始测试配置中心权限...");
        
        ConfigService configService = null;
        try {
            // 创建ConfigService
            logger.info("📡 正在创建 ConfigService 连接...");
            configService = ClientAuthTestUtil.createConfigService(namespace);
            logger.info("✅ ConfigService 创建成功");
            
            // 测试1: 配置发布（写权限）
            testConfigPublishWithDetails(configService, namespace);
            
            // 测试2: 配置读取（读权限）
            testConfigReadWithDetails(configService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 配置中心权限测试失败:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            logger.error("  - 完整异常: ", e);
            recordTestResult(namespace, "配置中心连接", false, e.getMessage());
        } finally {
            if (configService != null) {
                ClientAuthTestUtil.shutdown(configService);
            }
        }
    }
    
    /**
     * 详细测试服务列表查询
     */
    private static void testServiceListQueryWithDetails(NamingService namingService, String namespace) {
        String testName = "服务列表查询";
        logger.info("🔍 执行测试: {} ({})", testName, namespace);
        logger.info("📊 预期结果: {}", TestUserConfig.shouldAllowRead(namespace) ? "应该成功" : "应该被拒绝");
        
        try {
            ListView<String> services = ClientAuthTestUtil.getServicesOfServer(namingService, 1, 10, TestUserConfig.TEST_GROUP);
            
            if (services != null) {
                logger.info("📥 服务端响应详情:");
                logger.info("  - 响应状态: 成功");
                logger.info("  - 服务总数: {}", services.getCount());
                logger.info("  - 服务列表: {}", services.getData());
                
                boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
                if (shouldAllow) {
                    logger.info("✅ {} 成功 - 符合预期（有读权限）", testName);
                    recordTestResult(namespace, testName, true, "成功，服务数量: " + services.getCount());
                } else {
                    logger.error("❌ {} 成功 - 不符合预期（应该被拒绝）", testName);
                    recordTestResult(namespace, testName, false, "权限控制失效，不应该成功");
                }
            } else {
                logger.error("❌ {} 失败 - 返回null", testName);
                recordTestResult(namespace, testName, false, "返回null");
            }
            
        } catch (Exception e) {
            logger.error("📥 服务端响应详情:");
            logger.error("  - 响应状态: 异常");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            
            boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）", testName);
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无读权限）", testName);
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 详细测试服务注册
     */
    private static void testServiceRegistrationWithDetails(NamingService namingService, String namespace) {
        String testName = "服务注册";
        logger.info("🔍 执行测试: {} ({})", testName, namespace);
        logger.info("📊 预期结果: {}", TestUserConfig.shouldAllowWrite(namespace) ? "应该成功" : "应该被拒绝");
        
        String serviceName = TestUserConfig.getTestServiceName("enhanced-test");
        String ip = TestUserConfig.TEST_IP;
        int port = TestUserConfig.getTestPort(100);
        
        try {
            boolean result = ClientAuthTestUtil.registerInstance(namingService, serviceName, ip, port, TestUserConfig.TEST_GROUP);
            
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (result) {
                if (shouldAllow) {
                    logger.info("✅ {} 成功 - 符合预期（有写权限）", testName);
                    recordTestResult(namespace, testName, true, "注册成功");
                    
                    // 清理：注销服务
                    ClientAuthTestUtil.deregisterInstance(namingService, serviceName, ip, port, TestUserConfig.TEST_GROUP);
                } else {
                    logger.error("❌ {} 成功 - 不符合预期（应该被拒绝）", testName);
                    recordTestResult(namespace, testName, false, "权限控制失效，不应该成功");
                }
            } else {
                logger.error("❌ {} 失败", testName);
                recordTestResult(namespace, testName, false, "注册失败");
            }
            
        } catch (Exception e) {
            logger.error("📥 服务端响应详情:");
            logger.error("  - 响应状态: 异常");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）", testName);
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无写权限）", testName);
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 详细测试配置发布
     */
    private static void testConfigPublishWithDetails(ConfigService configService, String namespace) {
        String testName = "配置发布";
        logger.info("🔍 执行测试: {} ({})", testName, namespace);
        logger.info("📊 预期结果: {}", TestUserConfig.shouldAllowWrite(namespace) ? "应该成功" : "应该被拒绝");
        
        String dataId = TestUserConfig.getTestConfigId("enhanced-test");
        String group = TestUserConfig.TEST_CONFIG_GROUP;
        String content = "enhanced-test-content-" + System.currentTimeMillis() + "-namespace-" + namespace;
        
        try {
            boolean result = ClientAuthTestUtil.publishConfig(configService, dataId, group, content);
            
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (result) {
                if (shouldAllow) {
                    logger.info("✅ {} 成功 - 符合预期（有写权限）", testName);
                    recordTestResult(namespace, testName, true, "发布成功");
                    
                    // 清理：删除配置
                    ClientAuthTestUtil.removeConfig(configService, dataId, group);
                } else {
                    logger.error("❌ {} 成功 - 不符合预期（应该被拒绝）", testName);
                    recordTestResult(namespace, testName, false, "权限控制失效，不应该成功");
                }
            } else {
                logger.error("❌ {} 失败", testName);
                recordTestResult(namespace, testName, false, "发布失败");
            }
            
        } catch (Exception e) {
            logger.error("📥 服务端响应详情:");
            logger.error("  - 响应状态: 异常");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）", testName);
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无写权限）", testName);
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 详细测试配置读取
     */
    private static void testConfigReadWithDetails(ConfigService configService, String namespace) {
        String testName = "配置读取";
        logger.info("🔍 执行测试: {} ({})", testName, namespace);
        logger.info("📊 预期结果: {}", TestUserConfig.shouldAllowRead(namespace) ? "应该成功" : "应该被拒绝");
        
        String dataId = "test-config-for-read";
        String group = TestUserConfig.TEST_CONFIG_GROUP;
        
        try {
            String content = ClientAuthTestUtil.getConfig(configService, dataId, group, 3000);
            
            boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
            if (shouldAllow) {
                logger.info("✅ {} 成功 - 符合预期（有读权限）", testName);
                recordTestResult(namespace, testName, true, "读取成功，内容: " + (content != null ? content : "null"));
            } else {
                if (content == null) {
                    logger.info("✅ {} 正确被拒绝 - 符合预期（无读权限）", testName);
                    recordTestResult(namespace, testName, true, "正确拒绝");
                } else {
                    logger.error("❌ {} 成功 - 不符合预期（应该被拒绝）", testName);
                    recordTestResult(namespace, testName, false, "权限控制失效，不应该成功");
                }
            }
            
        } catch (Exception e) {
            logger.error("📥 服务端响应详情:");
            logger.error("  - 响应状态: 异常");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            
            boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）", testName);
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无读权限）", testName);
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testName, boolean passed, String message) {
        totalTests++;
        if (passed) {
            passedTests++;
        } else {
            failedTests++;
        }
        
        String result = String.format("%s - %s - %s: %s", 
                                    namespace, testName, 
                                    passed ? "PASS" : "FAIL", 
                                    message);
        testResults.add(result);
    }
    
    /**
     * 打印测试头部信息
     */
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            增强版客户端鉴权功能测试                            ║");
        logger.info("║         Enhanced Client Authentication Test              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试特性: 详细响应分析 + 服务端日志监控                        ║");
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 打印测试总结
     */
    private static void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  增强版测试结果总结                            ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}                                                ║", totalTests);
        logger.info("║ 通过测试: {}                                                ║", passedTests);
        logger.info("║ 失败测试: {}                                                ║", failedTests);
        logger.info("║ 成功率: {:.1f}%                                            ║", 
                   totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("📋 详细测试结果:");
        for (String result : testResults) {
            logger.info("  - {}", result);
        }
        
        logger.info("");
        logger.info("🎯 测试完成时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("📊 请结合服务端日志分析权限控制的具体实现效果");
    }
}
