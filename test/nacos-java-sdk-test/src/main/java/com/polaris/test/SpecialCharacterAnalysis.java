package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 特殊字符处理分析测试
 * 专门分析特殊字符读取不一致的原因
 */
public class SpecialCharacterAnalysis {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterAnalysis.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              特殊字符处理分析测试                               ║");
        logger.info("║         Special Character Analysis Test                ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分析特殊字符读取不一致的具体原因                          ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 测试所有命名空间的特殊字符处理
            String[] namespaces = {"cf", "cf-ns", "cf-n12s"};
            
            for (String namespace : namespaces) {
                logger.info("═══════════════════════════════════════════════════════════════");
                logger.info("🔍 分析命名空间: {}", namespace);
                logger.info("═══════════════════════════════════════════════════════════════");
                analyzeSpecialCharacters(namespace);
                logger.info("");
            }
            
        } catch (Exception e) {
            logger.error("❌ 特殊字符分析测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 分析指定命名空间的特殊字符处理
     */
    private static void analyzeSpecialCharacters(String namespace) {
        try {
            // 创建ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            
            // 测试特殊字符配置
            String dataId = "special-char-config";
            String group = "DEFAULT_GROUP";
            
            // 定义包含特殊字符的测试内容
            String originalContent = "测试特殊字符: !@#$%^&*()_+-=[]{}|;':\",./<>?`~\n" +
                                   "中文字符: 你好世界\n" +
                                   "Unicode: \u4e2d\u6587\n" +
                                   "Emoji: 😀🎉🔥\n" +
                                   "换行符测试\n" +
                                   "制表符:\t测试\n" +
                                   "引号测试: \"双引号\" '单引号'\n" +
                                   "反斜杠: \\\\ \\n \\t";
            
            logger.info("🔍 测试特殊字符配置处理...");
            logger.info("原始内容长度: {} 字符", originalContent.length());
            logger.info("原始内容预览: {}", originalContent.substring(0, Math.min(50, originalContent.length())) + "...");
            
            try {
                // 1. 尝试创建特殊字符配置
                logger.info("📝 步骤1: 创建特殊字符配置");
                boolean publishResult = configService.publishConfig(dataId, group, originalContent);
                
                if (publishResult) {
                    logger.info("✅ 特殊字符配置创建成功");
                    
                    // 等待一下确保配置生效
                    Thread.sleep(1000);
                    
                    // 2. 读取配置并比较
                    logger.info("📖 步骤2: 读取特殊字符配置");
                    String retrievedContent = configService.getConfig(dataId, group, 5000);
                    
                    if (retrievedContent != null) {
                        logger.info("✅ 特殊字符配置读取成功");
                        logger.info("读取内容长度: {} 字符", retrievedContent.length());
                        logger.info("读取内容预览: {}", retrievedContent.substring(0, Math.min(50, retrievedContent.length())) + "...");
                        
                        // 3. 详细比较内容
                        logger.info("🔍 步骤3: 详细内容比较");
                        compareContent(originalContent, retrievedContent);
                        
                        // 4. 字符编码分析
                        logger.info("🔍 步骤4: 字符编码分析");
                        analyzeEncoding(originalContent, retrievedContent);
                        
                    } else {
                        logger.warn("⚠️ 读取到空配置");
                    }
                    
                    // 5. 清理测试配置
                    logger.info("🧹 步骤5: 清理测试配置");
                    boolean deleteResult = configService.removeConfig(dataId, group);
                    if (deleteResult) {
                        logger.info("✅ 测试配置清理成功");
                    } else {
                        logger.warn("⚠️ 测试配置清理失败");
                    }
                    
                } else {
                    logger.error("❌ 特殊字符配置创建失败");
                }
                
            } catch (NacosException e) {
                logger.error("❌ Nacos异常: {} - {}", e.getErrCode(), e.getErrMsg());
            } catch (Exception e) {
                logger.error("❌ 其他异常: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 特殊字符分析失败: {}", namespace, e.getMessage());
        }
    }
    
    /**
     * 比较原始内容和读取内容
     */
    private static void compareContent(String original, String retrieved) {
        logger.info("📊 内容比较结果:");
        logger.info("  - 长度比较: 原始={}, 读取={}, 相等={}", 
                   original.length(), retrieved.length(), original.length() == retrieved.length());
        
        boolean contentEqual = original.equals(retrieved);
        logger.info("  - 内容相等: {}", contentEqual);
        
        if (!contentEqual) {
            logger.warn("⚠️ 内容不一致，进行逐字符分析:");
            
            int minLength = Math.min(original.length(), retrieved.length());
            int diffCount = 0;
            
            for (int i = 0; i < minLength && diffCount < 10; i++) {
                char origChar = original.charAt(i);
                char retrChar = retrieved.charAt(i);
                
                if (origChar != retrChar) {
                    diffCount++;
                    logger.warn("    位置{}: 原始='{}' ({}), 读取='{}' ({})", 
                               i, origChar, (int)origChar, retrChar, (int)retrChar);
                }
            }
            
            if (original.length() != retrieved.length()) {
                logger.warn("    长度差异: {}", Math.abs(original.length() - retrieved.length()));
            }
            
            if (diffCount >= 10) {
                logger.warn("    ... (还有更多差异)");
            }
        }
    }
    
    /**
     * 分析字符编码
     */
    private static void analyzeEncoding(String original, String retrieved) {
        try {
            logger.info("📊 字符编码分析:");
            
            byte[] originalBytes = original.getBytes("UTF-8");
            byte[] retrievedBytes = retrieved.getBytes("UTF-8");
            
            logger.info("  - UTF-8字节长度: 原始={}, 读取={}", originalBytes.length, retrievedBytes.length);
            
            // 检查是否包含特殊字符
            boolean hasSpecialChars = original.matches(".*[\\p{Cntrl}\\p{Punct}\\p{IsEmoji}].*");
            boolean hasUnicode = original.matches(".*[\\u4e00-\\u9fff].*");
            
            logger.info("  - 包含特殊字符: {}", hasSpecialChars);
            logger.info("  - 包含中文字符: {}", hasUnicode);
            
        } catch (Exception e) {
            logger.error("❌ 编码分析失败: {}", e.getMessage());
        }
    }
}
