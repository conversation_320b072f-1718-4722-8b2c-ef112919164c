package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;
import java.util.Scanner;

/**
 * 权限验证交互测试用例
 * 
 * 目标：验证Nacos配置中心权限控制行为的假设
 * 假设：Nacos对不存在的配置没有进行权限检查，但对存在的配置会进行权限检查
 * 
 * 测试流程：
 * 1. 在cf命名空间中创建测试配置（cf用户有读写权限）
 * 2. 等待用户在控制台取消cf用户对cf命名空间的读权限
 * 3. 验证权限变更后的行为差异：
 *    - 查询存在的配置：应该抛出权限异常
 *    - 查询不存在的配置：返回null（权限检查被绕过）
 */
public class PermissionVerificationInteractiveTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionVerificationInteractiveTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间（使用cf命名空间进行测试）
    private static final String TEST_NAMESPACE = "cf";
    
    // 测试配置标识符
    private static String testDataId;
    private static String testGroup;
    private static String testContent;
    
    public static void main(String[] args) {
        PermissionVerificationInteractiveTest test = new PermissionVerificationInteractiveTest();
        test.runPermissionVerificationTest();
    }
    
    public void runPermissionVerificationTest() {
        Scanner scanner = new Scanner(System.in);
        
        printTestHeader();
        
        try {
            // 第一步：创建测试配置
            boolean configCreated = step1CreateTestConfig();
            
            if (!configCreated) {
                logger.error("❌ 测试配置创建失败，无法继续测试");
                return;
            }
            
            // 第二步：等待权限变更
            step2WaitForPermissionChange(scanner);
            
            // 第三步：验证权限控制行为
            step3VerifyPermissionBehavior();
            
            // 第四步：清理测试数据（可选）
            step4CleanupTestData(scanner);
            
        } catch (Exception e) {
            logger.error("❌ 权限验证测试异常", e);
        } finally {
            scanner.close();
        }
        
        printTestSummary();
    }
    
    /**
     * 第一步：在cf命名空间中创建测试配置
     */
    private boolean step1CreateTestConfig() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    第一步：创建测试配置                        ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        // 生成唯一的测试标识符
        long timestamp = System.currentTimeMillis();
        testDataId = "permission-verify-config-" + timestamp;
        testGroup = "PERMISSION_VERIFY_GROUP";
        testContent = generateTestConfigContent();
        
        logger.info("🔧 测试配置信息:");
        logger.info("  - 命名空间: {}", TEST_NAMESPACE);
        logger.info("  - DataId: {}", testDataId);
        logger.info("  - Group: {}", testGroup);
        logger.info("  - 内容长度: {} 字符", testContent.length());
        logger.info("");
        
        ConfigService configService = null;
        
        try {
            // 创建ConfigService
            configService = createConfigService(TEST_NAMESPACE);
            logger.info("✅ ConfigService创建成功");
            
            // 发布配置
            logger.info("🚀 正在发布测试配置...");
            boolean result = configService.publishConfig(testDataId, testGroup, testContent);
            
            if (result) {
                logger.info("✅ 测试配置发布成功");
                
                // 验证配置是否可以查询
                Thread.sleep(1000); // 等待同步
                String retrievedContent = configService.getConfig(testDataId, testGroup, 5000);
                
                if (retrievedContent != null && retrievedContent.equals(testContent)) {
                    logger.info("✅ 测试配置查询验证成功");
                    logger.info("📋 配置内容匹配，长度: {} 字符", retrievedContent.length());
                    return true;
                } else {
                    logger.error("❌ 测试配置查询验证失败");
                    if (retrievedContent == null) {
                        logger.error("  - 查询结果为null");
                    } else {
                        logger.error("  - 内容不匹配，期望长度: {}, 实际长度: {}", 
                                   testContent.length(), retrievedContent.length());
                    }
                    return false;
                }
            } else {
                logger.error("❌ 测试配置发布失败");
                return false;
            }
            
        } catch (NacosException e) {
            logger.error("❌ 创建测试配置时发生Nacos异常");
            logger.error("  - 错误代码: {}", e.getErrCode());
            logger.error("  - 错误消息: {}", e.getErrMsg());
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("❌ 等待过程中被中断");
            return false;
        } catch (Exception e) {
            logger.error("❌ 创建测试配置时发生异常", e);
            return false;
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 第二步：等待权限变更
     */
    private void step2WaitForPermissionChange(Scanner scanner) {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  第二步：等待权限变更                          ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("📋 现在请按照以下步骤操作：");
        logger.info("");
        logger.info("1. 打开Nacos控制台: http://{}/#/", SERVER_ADDR);
        logger.info("2. 登录管理员账户");
        logger.info("3. 进入权限控制 → 用户管理");
        logger.info("4. 找到用户 '{}' 并编辑权限", USERNAME);
        logger.info("5. 取消该用户对 '{}' 命名空间的读权限", TEST_NAMESPACE);
        logger.info("6. 保存权限变更");
        logger.info("");
        logger.info("⚠️  重要提示：");
        logger.info("   - 请确保只取消读权限，保留其他权限（如果有）");
        logger.info("   - 权限变更可能需要几秒钟生效");
        logger.info("   - 测试完成后可以恢复原有权限");
        logger.info("");
        logger.info("🔧 测试配置信息（供控制台验证使用）：");
        logger.info("   - 命名空间: {}", TEST_NAMESPACE);
        logger.info("   - DataId: {}", testDataId);
        logger.info("   - Group: {}", testGroup);
        logger.info("");
        
        logger.info("完成权限变更后，请按回车键继续测试...");
        scanner.nextLine();
        
        logger.info("✅ 权限变更确认完成，开始验证测试");
        logger.info("");
    }
    
    /**
     * 第三步：验证权限控制行为
     */
    private void step3VerifyPermissionBehavior() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                第三步：验证权限控制行为                        ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        ConfigService configService = null;
        
        try {
            // 创建ConfigService
            configService = createConfigService(TEST_NAMESPACE);
            logger.info("✅ ConfigService创建成功");
            logger.info("");
            
            // 测试1：查询存在的配置（应该抛出权限异常）
            testQueryExistingConfig(configService);
            
            logger.info("");
            
            // 测试2：查询不存在的配置（应该返回null，权限检查被绕过）
            testQueryNonExistentConfig(configService);
            
        } catch (Exception e) {
            logger.error("❌ 验证权限控制行为时发生异常", e);
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 测试查询存在的配置
     */
    private void testQueryExistingConfig(ConfigService configService) {
        logger.info("🧪 测试1: 查询存在的配置");
        logger.info("🔍 查询配置: dataId={}, group={}", testDataId, testGroup);
        logger.info("🎯 预期结果: 抛出权限异常（配置存在，应该进行权限检查）");
        logger.info("");
        
        try {
            String content = configService.getConfig(testDataId, testGroup, 5000);
            
            // 如果没有抛出异常，说明查询成功了
            logger.error("❌ 测试1失败: 查询存在的配置意外成功");
            logger.error("🔍 查询结果详情:");
            if (content == null) {
                logger.error("  - 内容: null");
            } else {
                logger.error("  - 内容长度: {} 字符", content.length());
                logger.error("  - 内容匹配: {}", content.equals(testContent));
            }
            logger.error("💡 这表明权限控制可能存在问题或权限变更未生效");
            
        } catch (NacosException e) {
            // 抛出异常是预期的行为
            logger.info("✅ 测试1成功: 查询存在的配置正确被拒绝");
            logger.info("🔒 权限异常详情:");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("💡 这证明Nacos对存在的配置进行了正确的权限检查");
        }
    }
    
    /**
     * 测试查询不存在的配置
     */
    private void testQueryNonExistentConfig(ConfigService configService) {
        logger.info("🧪 测试2: 查询不存在的配置");
        
        // 生成一个肯定不存在的配置ID
        String nonExistentDataId = "non-existent-config-" + System.currentTimeMillis();
        String nonExistentGroup = "NON_EXISTENT_GROUP";
        
        logger.info("🔍 查询配置: dataId={}, group={}", nonExistentDataId, nonExistentGroup);
        logger.info("🎯 预期结果: 返回null（权限检查被绕过，不抛出异常）");
        logger.info("");
        
        try {
            String content = configService.getConfig(nonExistentDataId, nonExistentGroup, 5000);
            
            // 查询成功，检查结果
            if (content == null) {
                logger.info("✅ 测试2成功: 查询不存在的配置返回null");
                logger.info("💡 这证明了我们的假设：Nacos对不存在的配置没有进行权限检查");
                logger.info("🔍 权限绕过行为确认：");
                logger.info("  - 用户无读权限但查询成功");
                logger.info("  - 返回null而不是权限异常");
                logger.info("  - 这是一个权限控制的设计缺陷");
            } else {
                logger.warn("⚠️ 测试2异常: 查询不存在的配置返回了内容");
                logger.warn("  - 内容长度: {} 字符", content.length());
                logger.warn("  - 这可能表明配置意外存在");
            }
            
        } catch (NacosException e) {
            // 如果抛出异常，说明权限检查生效了
            logger.info("❌ 测试2失败: 查询不存在的配置被拒绝");
            logger.info("🔒 权限异常详情:");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("💡 这表明Nacos对所有配置查询都进行了权限检查");
            logger.info("🤔 这与我们之前的观察不一致，可能需要进一步分析");
        }
    }
    
    /**
     * 第四步：清理测试数据（可选）
     */
    private void step4CleanupTestData(Scanner scanner) {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  第四步：清理测试数据                          ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("🧹 是否需要清理测试配置？");
        logger.info("📋 测试配置信息:");
        logger.info("  - DataId: {}", testDataId);
        logger.info("  - Group: {}", testGroup);
        logger.info("");
        logger.info("⚠️  注意：清理需要恢复cf用户对cf命名空间的写权限");
        logger.info("");
        logger.info("输入 'yes' 进行清理，或按回车键跳过清理：");
        
        String input = scanner.nextLine().trim();
        
        if ("yes".equalsIgnoreCase(input)) {
            logger.info("🚀 开始清理测试配置...");
            
            ConfigService configService = null;
            try {
                configService = createConfigService(TEST_NAMESPACE);
                boolean result = configService.removeConfig(testDataId, testGroup);
                
                if (result) {
                    logger.info("✅ 测试配置清理成功");
                } else {
                    logger.warn("⚠️ 测试配置清理失败");
                }
                
            } catch (NacosException e) {
                logger.warn("⚠️ 清理测试配置时发生异常: {}", e.getErrMsg());
            } finally {
                if (configService != null) {
                    try {
                        configService.shutDown();
                    } catch (Exception e) {
                        logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                    }
                }
            }
        } else {
            logger.info("⏭️ 跳过清理，测试配置将保留");
            logger.info("💡 您可以稍后在控制台手动删除该配置");
        }
    }
    
    // ==================== 辅助方法 ====================
    
    private ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createConfigService(properties);
    }
    
    private String generateTestConfigContent() {
        StringBuilder sb = new StringBuilder();
        sb.append("# 权限验证交互测试配置\n");
        sb.append("# 创建时间: ").append(getCurrentTime()).append("\n");
        sb.append("# 时间戳: ").append(System.currentTimeMillis()).append("\n");
        sb.append("# 用途: 验证Nacos配置中心权限控制行为\n");
        sb.append("\n");
        sb.append("test.purpose=permission-verification\n");
        sb.append("test.namespace=").append(TEST_NAMESPACE).append("\n");
        sb.append("test.user=").append(USERNAME).append("\n");
        sb.append("test.hypothesis=nacos-skips-permission-check-for-non-existent-configs\n");
        sb.append("\n");
        sb.append("# 测试配置项\n");
        sb.append("app.name=permission-verification-test\n");
        sb.append("app.version=1.0.0\n");
        sb.append("app.environment=test\n");
        
        return sb.toString();
    }
    
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                权限验证交互测试用例                            ║");
        logger.info("║          Permission Verification Interactive Test          ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 验证Nacos配置中心权限控制行为假设                       ║");
        logger.info("║ 假设: Nacos对不存在的配置没有进行权限检查                     ║");
        logger.info("║ 方法: 对比查询存在vs不存在配置的权限控制行为                  ║");
        logger.info("║ 测试时间: {}", getCurrentTime());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private void printTestSummary() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  权限验证测试总结                             ║");
        logger.info("║            Permission Verification Test Summary           ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试完成时间: {}", getCurrentTime());
        logger.info("║                                                              ║");
        logger.info("║ 关键发现:                                                    ║");
        logger.info("║   请查看上述详细测试结果分析                                  ║");
        logger.info("║   特别关注存在vs不存在配置的权限控制行为差异                  ║");
        logger.info("║                                                              ║");
        logger.info("║ 后续建议:                                                    ║");
        logger.info("║   1. 记录测试结果用于问题报告                                ║");
        logger.info("║   2. 考虑在应用层增加额外的权限检查                          ║");
        logger.info("║   3. 恢复cf用户的原有权限配置                                ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("感谢您使用权限验证交互测试用例！");
    }
}
