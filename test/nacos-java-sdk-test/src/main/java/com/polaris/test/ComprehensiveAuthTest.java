package com.polaris.test;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全面的客户端鉴权测试程序
 * 包含详细的响应记录、时间戳和内容验证
 */
public class ComprehensiveAuthTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveAuthTest.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 测试结果统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    private static final List<TestResult> testResults = new ArrayList<>();
    
    // 测试数据存储
    private static final Map<String, List<String>> createdServices = new HashMap<>();
    private static final Map<String, List<String>> createdConfigs = new HashMap<>();
    
    public static void main(String[] args) {
        if (args.length < 1) {
            logger.error("❌ 请指定测试阶段: auth-off 或 auth-on");
            System.exit(1);
        }
        
        String testPhase = args[0];
        boolean authEnabled = "auth-on".equals(testPhase);
        
        printTestHeader(testPhase, authEnabled);
        
        try {
            // 初始化测试数据存储
            initializeTestData();
            
            // 打印测试配置
            TestUserConfig.printConfig();
            
            if (authEnabled) {
                // 鉴权开启状态测试
                runAuthEnabledTests();
            } else {
                // 鉴权关闭状态测试
                runAuthDisabledTests();
            }
            
            // 输出测试总结
            printTestSummary(testPhase);
            
        } catch (Exception e) {
            logger.error("❌ 客户端鉴权测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 初始化测试数据存储
     */
    private static void initializeTestData() {
        for (String namespace : TestUserConfig.getAllNamespaces()) {
            createdServices.put(namespace, new ArrayList<>());
            createdConfigs.put(namespace, new ArrayList<>());
        }
    }
    
    /**
     * 运行鉴权关闭状态测试
     */
    private static void runAuthDisabledTests() {
        logger.info("🔓 开始执行鉴权关闭状态测试...");
        logger.info("📋 预期结果: 所有操作都应该成功（权限控制被绕过）");
        logger.info("");
        
        for (String namespace : TestUserConfig.getAllNamespaces()) {
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("🔍 测试命名空间: {} ({})", namespace, TestUserConfig.getNamespacePermission(namespace));
            logger.info("═══════════════════════════════════════════════════════════════");
            
            // 注册中心功能测试
            testNamingServiceFull(namespace, false);
            
            // 配置中心功能测试
            testConfigServiceFull(namespace, false);
            
            logger.info("");
        }
    }
    
    /**
     * 运行鉴权开启状态测试
     */
    private static void runAuthEnabledTests() {
        logger.info("🔒 开始执行鉴权开启状态测试...");
        logger.info("📋 预期结果: 根据权限设置控制访问");
        logger.info("  - wr-auth: 所有操作成功");
        logger.info("  - read-only: 只有读操作成功");
        logger.info("  - noauth: 所有操作失败");
        logger.info("");
        
        for (String namespace : TestUserConfig.getAllNamespaces()) {
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("🔍 测试命名空间: {} ({})", namespace, TestUserConfig.getNamespacePermission(namespace));
            logger.info("═══════════════════════════════════════════════════════════════");
            
            // 注册中心功能测试
            testNamingServiceFull(namespace, true);
            
            // 配置中心功能测试
            testConfigServiceFull(namespace, true);
            
            logger.info("");
        }
    }
    
    /**
     * 完整的注册中心功能测试
     */
    private static void testNamingServiceFull(String namespace, boolean authEnabled) {
        logger.info("🚀 开始注册中心功能测试...");
        
        NamingService namingService = null;
        try {
            // 创建NamingService
            String timestamp = getCurrentTimestamp();
            logger.info("🕐 [{}] 创建 NamingService - 命名空间: {}", timestamp, namespace);
            
            namingService = ClientAuthTestUtil.createNamingService(namespace);
            logger.info("✅ NamingService创建成功");
            
            // 1. 服务注册测试
            testServiceRegistration(namingService, namespace, authEnabled);
            
            // 2. 服务列表查询测试
            testServiceListQuery(namingService, namespace, authEnabled);
            
            // 3. 服务实例查询测试
            testServiceInstanceQuery(namingService, namespace, authEnabled);
            
            // 4. 服务删除测试（如果有创建的服务）
            if (!createdServices.get(namespace).isEmpty()) {
                testServiceDeregistration(namingService, namespace, authEnabled);
            }
            
        } catch (Exception e) {
            logger.error("❌ 注册中心功能测试失败: {}", e.getMessage());
            recordTestResult(namespace, "注册中心连接", false, "连接异常: " + e.getMessage(), authEnabled);
        } finally {
            if (namingService != null) {
                ClientAuthTestUtil.shutdown(namingService);
            }
        }
    }
    
    /**
     * 完整的配置中心功能测试
     */
    private static void testConfigServiceFull(String namespace, boolean authEnabled) {
        logger.info("📝 开始配置中心功能测试...");
        
        ConfigService configService = null;
        try {
            // 创建ConfigService
            String timestamp = getCurrentTimestamp();
            logger.info("🕐 [{}] 创建 ConfigService - 命名空间: {}", timestamp, namespace);
            
            configService = ClientAuthTestUtil.createConfigService(namespace);
            logger.info("✅ ConfigService创建成功");
            
            // 1. 配置发布测试
            testConfigPublish(configService, namespace, authEnabled);
            
            // 2. 配置读取测试
            testConfigRead(configService, namespace, authEnabled);
            
            // 3. 配置修改测试（如果有创建的配置）
            if (!createdConfigs.get(namespace).isEmpty()) {
                testConfigModify(configService, namespace, authEnabled);
            }
            
            // 4. 配置删除测试（如果有创建的配置）
            if (!createdConfigs.get(namespace).isEmpty()) {
                testConfigDelete(configService, namespace, authEnabled);
            }
            
        } catch (Exception e) {
            logger.error("❌ 配置中心功能测试失败: {}", e.getMessage());
            recordTestResult(namespace, "配置中心连接", false, "连接异常: " + e.getMessage(), authEnabled);
        } finally {
            if (configService != null) {
                ClientAuthTestUtil.shutdown(configService);
            }
        }
    }
    
    /**
     * 测试服务注册
     */
    private static void testServiceRegistration(NamingService namingService, String namespace, boolean authEnabled) {
        String testName = "服务注册";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);
        
        int serviceCount = authEnabled ? 1 : 3; // 鉴权关闭时注册3个服务，开启时注册1个
        
        for (int i = 1; i <= serviceCount; i++) {
            String serviceName = TestUserConfig.getTestServiceName("svc" + i);
            String ip = TestUserConfig.TEST_IP;
            int port = TestUserConfig.getTestPort(i);
            
            try {
                boolean result = ClientAuthTestUtil.registerInstance(namingService, serviceName, ip, port, "DEFAULT");
                
                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
                boolean testPassed = (result == shouldSucceed);
                
                if (result) {
                    createdServices.get(namespace).add(serviceName);
                    logger.info("✅ 服务注册成功: {}", serviceName);
                } else {
                    logger.info("❌ 服务注册失败: {}", serviceName);
                }
                
                String details = String.format("服务: %s, 结果: %s, 符合预期: %s", 
                                              serviceName, result ? "成功" : "失败", testPassed ? "是" : "否");
                recordTestResult(namespace, testName + "-" + i, testPassed, details, authEnabled);
                
            } catch (Exception e) {
                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
                boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过
                
                logger.info("🔒 服务注册异常: {} - {}", serviceName, e.getMessage());
                recordTestResult(namespace, testName + "-" + i, testPassed, "异常: " + e.getMessage(), authEnabled);
            }
        }
        
        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }
    
    /**
     * 测试服务列表查询
     */
    private static void testServiceListQuery(NamingService namingService, String namespace, boolean authEnabled) {
        String testName = "服务列表查询";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);
        
        try {
            ListView<String> services = ClientAuthTestUtil.getServicesOfServer(namingService, 1, 10, "DEFAULT");
            
            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
            boolean result = (services != null);
            boolean testPassed = (result == shouldSucceed);
            
            if (result) {
                logger.info("✅ 服务列表查询成功，服务数量: {}", services.getCount());
            } else {
                logger.info("❌ 服务列表查询失败");
            }
            
            String details = String.format("结果: %s, 服务数量: %d, 符合预期: %s", 
                                          result ? "成功" : "失败", 
                                          services != null ? services.getCount() : 0, 
                                          testPassed ? "是" : "否");
            recordTestResult(namespace, testName, testPassed, details, authEnabled);
            
        } catch (Exception e) {
            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
            boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过
            
            logger.info("🔒 服务列表查询异常: {}", e.getMessage());
            recordTestResult(namespace, testName, testPassed, "异常: " + e.getMessage(), authEnabled);
        }
        
        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }
    
    /**
     * 获取当前时间戳
     */
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(TIMESTAMP_FORMAT);
    }
    
    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testName, boolean passed, String details, boolean authEnabled) {
        totalTests++;
        if (passed) {
            passedTests++;
        } else {
            failedTests++;
        }
        
        TestResult result = new TestResult();
        result.namespace = namespace;
        result.testName = testName;
        result.passed = passed;
        result.details = details;
        result.authEnabled = authEnabled;
        result.timestamp = getCurrentTimestamp();
        
        testResults.add(result);
    }
    
    /**
     * 测试结果数据类
     */
    private static class TestResult {
        String namespace;
        String testName;
        boolean passed;
        String details;
        boolean authEnabled;
        String timestamp;
    }
    
    /**
     * 打印测试头部信息
     */
    private static void printTestHeader(String testPhase, boolean authEnabled) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              全面客户端鉴权功能测试                            ║");
        logger.info("║           Comprehensive Client Auth Test                ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试阶段: {} ({})", testPhase, authEnabled ? "鉴权开启" : "鉴权关闭");
        logger.info("║ 测试时间: {}", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 打印测试总结
     */
    private static void printTestSummary(String testPhase) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    测试结果总结                              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试阶段: {}                                               ║", testPhase);
        logger.info("║ 总测试数: {}                                                ║", totalTests);
        logger.info("║ 通过测试: {}                                                ║", passedTests);
        logger.info("║ 失败测试: {}                                                ║", failedTests);
        logger.info("║ 成功率: {:.1f}%                                            ║", 
                   totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("📋 详细测试结果:");
        for (TestResult result : testResults) {
            String status = result.passed ? "PASS" : "FAIL";
            logger.info("  [{}] {} - {} - {} - {}", result.timestamp, result.namespace, result.testName, status, result.details);
        }
        
        logger.info("");
        logger.info("🎯 测试完成时间: {}", getCurrentTimestamp());
    }

    /**
     * 测试服务实例查询
     */
    private static void testServiceInstanceQuery(NamingService namingService, String namespace, boolean authEnabled) {
        String testName = "服务实例查询";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        // 只查询已创建的服务
        List<String> servicesToQuery = createdServices.get(namespace);
        if (servicesToQuery.isEmpty()) {
            logger.info("⚠️ 没有已创建的服务，跳过实例查询测试");
            return;
        }

        for (String serviceName : servicesToQuery) {
            try {
                List<Instance> instances = ClientAuthTestUtil.getAllInstances(namingService, serviceName, "DEFAULT");

                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
                boolean result = (instances != null);
                boolean testPassed = (result == shouldSucceed);

                if (result) {
                    logger.info("✅ 服务实例查询成功: {}, 实例数量: {}", serviceName, instances.size());
                } else {
                    logger.info("❌ 服务实例查询失败: {}", serviceName);
                }

                String details = String.format("服务: %s, 结果: %s, 实例数: %d, 符合预期: %s",
                                              serviceName, result ? "成功" : "失败",
                                              instances != null ? instances.size() : 0,
                                              testPassed ? "是" : "否");
                recordTestResult(namespace, testName + "-" + serviceName, testPassed, details, authEnabled);

            } catch (Exception e) {
                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
                boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

                logger.info("🔒 服务实例查询异常: {} - {}", serviceName, e.getMessage());
                recordTestResult(namespace, testName + "-" + serviceName, testPassed, "异常: " + e.getMessage(), authEnabled);
            }
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }

    /**
     * 测试服务删除
     */
    private static void testServiceDeregistration(NamingService namingService, String namespace, boolean authEnabled) {
        String testName = "服务删除";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        // 删除一个已创建的服务
        List<String> servicesToDelete = new ArrayList<>(createdServices.get(namespace));
        if (servicesToDelete.isEmpty()) {
            logger.info("⚠️ 没有已创建的服务，跳过删除测试");
            return;
        }

        String serviceToDelete = servicesToDelete.get(0);
        String ip = TestUserConfig.TEST_IP;
        int port = TestUserConfig.getTestPort(1);

        try {
            boolean result = ClientAuthTestUtil.deregisterInstance(namingService, serviceToDelete, ip, port, "DEFAULT");

            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = (result == shouldSucceed);

            if (result) {
                createdServices.get(namespace).remove(serviceToDelete);
                logger.info("✅ 服务删除成功: {}", serviceToDelete);
            } else {
                logger.info("❌ 服务删除失败: {}", serviceToDelete);
            }

            String details = String.format("服务: %s, 结果: %s, 符合预期: %s",
                                          serviceToDelete, result ? "成功" : "失败", testPassed ? "是" : "否");
            recordTestResult(namespace, testName, testPassed, details, authEnabled);

        } catch (Exception e) {
            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

            logger.info("🔒 服务删除异常: {} - {}", serviceToDelete, e.getMessage());
            recordTestResult(namespace, testName, testPassed, "异常: " + e.getMessage(), authEnabled);
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }

    /**
     * 测试配置发布
     */
    private static void testConfigPublish(ConfigService configService, String namespace, boolean authEnabled) {
        String testName = "配置发布";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        int configCount = authEnabled ? 1 : 3; // 鉴权关闭时发布3个配置，开启时发布1个

        for (int i = 1; i <= configCount; i++) {
            String dataId = TestUserConfig.getTestConfigDataId("config" + i);
            String group = "DEFAULT";
            String content = TestUserConfig.getTestConfigContent("测试配置内容" + i, namespace);

            try {
                boolean result = ClientAuthTestUtil.publishConfig(configService, dataId, group, content);

                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
                boolean testPassed = (result == shouldSucceed);

                if (result) {
                    createdConfigs.get(namespace).add(dataId);
                    logger.info("✅ 配置发布成功: {}", dataId);
                } else {
                    logger.info("❌ 配置发布失败: {}", dataId);
                }

                String details = String.format("配置: %s, 结果: %s, 符合预期: %s",
                                              dataId, result ? "成功" : "失败", testPassed ? "是" : "否");
                recordTestResult(namespace, testName + "-" + i, testPassed, details, authEnabled);

            } catch (Exception e) {
                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
                boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

                logger.info("🔒 配置发布异常: {} - {}", dataId, e.getMessage());
                recordTestResult(namespace, testName + "-" + i, testPassed, "异常: " + e.getMessage(), authEnabled);
            }
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }

    /**
     * 测试配置读取
     */
    private static void testConfigRead(ConfigService configService, String namespace, boolean authEnabled) {
        String testName = "配置读取";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        // 只读取已创建的配置
        List<String> configsToRead = createdConfigs.get(namespace);
        if (configsToRead.isEmpty()) {
            logger.info("⚠️ 没有已创建的配置，跳过读取测试");
            return;
        }

        for (String dataId : configsToRead) {
            String group = "DEFAULT";

            try {
                String content = ClientAuthTestUtil.getConfig(configService, dataId, group, 3000);

                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
                boolean result = (content != null && !content.isEmpty());
                boolean testPassed = (result == shouldSucceed);

                if (result) {
                    logger.info("✅ 配置读取成功: {}, 内容长度: {}", dataId, content.length());
                    logger.info("📄 配置内容: {}", content);
                } else {
                    logger.info("❌ 配置读取失败: {}", dataId);
                }

                String details = String.format("配置: %s, 结果: %s, 内容长度: %d, 符合预期: %s",
                                              dataId, result ? "成功" : "失败",
                                              content != null ? content.length() : 0,
                                              testPassed ? "是" : "否");
                recordTestResult(namespace, testName + "-" + dataId, testPassed, details, authEnabled);

            } catch (Exception e) {
                boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowRead(namespace);
                boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

                logger.info("🔒 配置读取异常: {} - {}", dataId, e.getMessage());
                recordTestResult(namespace, testName + "-" + dataId, testPassed, "异常: " + e.getMessage(), authEnabled);
            }
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }

    /**
     * 测试配置修改
     */
    private static void testConfigModify(ConfigService configService, String namespace, boolean authEnabled) {
        String testName = "配置修改";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        // 修改一个已创建的配置
        List<String> configsToModify = new ArrayList<>(createdConfigs.get(namespace));
        if (configsToModify.isEmpty()) {
            logger.info("⚠️ 没有已创建的配置，跳过修改测试");
            return;
        }

        String dataId = configsToModify.get(0);
        String group = "DEFAULT";
        String newContent = TestUserConfig.getTestConfigContent("修改后的配置内容", namespace);

        try {
            boolean result = ClientAuthTestUtil.publishConfig(configService, dataId, group, newContent);

            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = (result == shouldSucceed);

            if (result) {
                logger.info("✅ 配置修改成功: {}", dataId);
            } else {
                logger.info("❌ 配置修改失败: {}", dataId);
            }

            String details = String.format("配置: %s, 结果: %s, 符合预期: %s",
                                          dataId, result ? "成功" : "失败", testPassed ? "是" : "否");
            recordTestResult(namespace, testName, testPassed, details, authEnabled);

        } catch (Exception e) {
            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

            logger.info("🔒 配置修改异常: {} - {}", dataId, e.getMessage());
            recordTestResult(namespace, testName, testPassed, "异常: " + e.getMessage(), authEnabled);
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }

    /**
     * 测试配置删除
     */
    private static void testConfigDelete(ConfigService configService, String namespace, boolean authEnabled) {
        String testName = "配置删除";
        String timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 开始测试: {} ({})", timestamp, testName, namespace);

        // 删除一个已创建的配置
        List<String> configsToDelete = new ArrayList<>(createdConfigs.get(namespace));
        if (configsToDelete.isEmpty()) {
            logger.info("⚠️ 没有已创建的配置，跳过删除测试");
            return;
        }

        String dataId = configsToDelete.get(0);
        String group = "DEFAULT";

        try {
            boolean result = ClientAuthTestUtil.removeConfig(configService, dataId, group);

            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = (result == shouldSucceed);

            if (result) {
                createdConfigs.get(namespace).remove(dataId);
                logger.info("✅ 配置删除成功: {}", dataId);
            } else {
                logger.info("❌ 配置删除失败: {}", dataId);
            }

            String details = String.format("配置: %s, 结果: %s, 符合预期: %s",
                                          dataId, result ? "成功" : "失败", testPassed ? "是" : "否");
            recordTestResult(namespace, testName, testPassed, details, authEnabled);

        } catch (Exception e) {
            boolean shouldSucceed = !authEnabled || TestUserConfig.shouldAllowWrite(namespace);
            boolean testPassed = !shouldSucceed; // 异常时，如果不应该成功则测试通过

            logger.info("🔒 配置删除异常: {} - {}", dataId, e.getMessage());
            recordTestResult(namespace, testName, testPassed, "异常: " + e.getMessage(), authEnabled);
        }

        timestamp = getCurrentTimestamp();
        logger.info("🕐 [{}] 完成测试: {}", timestamp, testName);
    }
}
