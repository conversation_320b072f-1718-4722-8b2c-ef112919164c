package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;
import java.util.stream.Stream;

/**
 * Nacos客户端缓存机制分析测试
 * 
 * 目标：
 * 1. 分析客户端缓存的存储位置
 * 2. 查看缓存文件的内容
 * 3. 测试禁用缓存的方法
 */
public class ClientCacheAnalysisTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientCacheAnalysisTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String TARGET_NAMESPACE = "cf-n12s";
    
    // 测试配置
    private static final String TEST_DATA_ID = "test-read";
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            Nacos客户端缓存机制分析测试                         ║");
        logger.info("║         Nacos Client Cache Analysis Test                ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分析客户端缓存机制并测试禁用方法                         ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 阶段1：分析默认缓存行为
            logger.info("🔍 阶段1：分析默认缓存行为");
            analyzeDefaultCacheBehavior();
            
            // 阶段2：查看缓存文件位置和内容
            logger.info("🔍 阶段2：查看缓存文件位置和内容");
            analyzeCacheFiles();
            
            // 阶段3：测试禁用缓存的方法
            logger.info("🔍 阶段3：测试禁用缓存的方法");
            testDisableCache();
            
        } catch (Exception e) {
            logger.error("客户端缓存分析测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 阶段1：分析默认缓存行为
     */
    private static void analyzeDefaultCacheBehavior() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段1：分析默认缓存行为");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        try {
            // 创建默认ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ 默认ConfigService创建成功");
            
            // 读取配置（这会触发缓存）
            logger.info("📖 读取配置以触发缓存...");
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            
            if (content != null) {
                logger.info("✅ 配置读取成功");
                logger.info("  - 内容长度: {} 字符", content.length());
                logger.info("  - 内容: {}", content);
                logger.info("  - 此配置现在应该被缓存到客户端");
            } else {
                logger.warn("⚠️ 配置不存在或读取失败");
            }
            
        } catch (NacosException e) {
            logger.error("❌ 默认缓存行为分析失败: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    /**
     * 阶段2：查看缓存文件位置和内容
     */
    private static void analyzeCacheFiles() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段2：查看缓存文件位置和内容");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        // Nacos客户端默认缓存目录
        String userHome = System.getProperty("user.home");
        String[] possibleCacheDirs = {
            userHome + "/nacos/config",
            userHome + "/.nacos/config", 
            System.getProperty("java.io.tmpdir") + "/nacos/config",
            "./nacos/config",
            "nacos/config"
        };
        
        logger.info("🔍 搜索Nacos缓存目录...");
        logger.info("用户主目录: {}", userHome);
        logger.info("临时目录: {}", System.getProperty("java.io.tmpdir"));
        logger.info("当前工作目录: {}", System.getProperty("user.dir"));
        logger.info("");
        
        boolean foundCache = false;
        
        for (String cacheDir : possibleCacheDirs) {
            Path cachePath = Paths.get(cacheDir);
            logger.info("🔍 检查缓存目录: {}", cachePath.toAbsolutePath());
            
            if (Files.exists(cachePath)) {
                logger.info("✅ 找到缓存目录: {}", cachePath.toAbsolutePath());
                foundCache = true;
                
                try {
                    // 列出缓存目录内容
                    logger.info("📁 缓存目录内容:");
                    listDirectoryContents(cachePath, 0);
                    
                    // 查找我们的测试配置缓存文件
                    findTestConfigCache(cachePath);
                    
                } catch (IOException e) {
                    logger.error("❌ 读取缓存目录失败: {}", e.getMessage());
                }
                
                logger.info("");
            } else {
                logger.info("❌ 缓存目录不存在");
            }
        }
        
        if (!foundCache) {
            logger.warn("⚠️ 未找到Nacos缓存目录");
            logger.info("💡 可能的原因:");
            logger.info("  1. 缓存目录在其他位置");
            logger.info("  2. 缓存被禁用");
            logger.info("  3. 配置尚未被缓存");
        }
        
        logger.info("");
    }
    
    /**
     * 阶段3：测试禁用缓存的方法
     */
    private static void testDisableCache() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段3：测试禁用缓存的方法");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        logger.info("🛠️ 测试方法1：设置enableRemoteSyncConfig=false");
        testMethod1DisableRemoteSync();
        
        logger.info("🛠️ 测试方法2：设置configLongPollTimeout=0");
        testMethod2DisableLongPoll();
        
        logger.info("🛠️ 测试方法3：设置enableLocalCache=false");
        testMethod3DisableLocalCache();
        
        logger.info("🛠️ 测试方法4：自定义缓存目录到临时位置");
        testMethod4CustomCacheDir();
        
        logger.info("");
    }
    
    private static void testMethod1DisableRemoteSync() {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🛠️ 方法1：禁用远程同步 (enableRemoteSyncConfig=false)");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            properties.put("enableRemoteSyncConfig", "false");
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ 禁用远程同步的ConfigService创建成功");
            
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            logger.info("📊 读取结果: {}", content != null ? "成功" : "失败");
            if (content != null) {
                logger.info("  - 内容: {}", content);
            }
            
        } catch (Exception e) {
            logger.error("❌ 方法1测试失败: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    private static void testMethod2DisableLongPoll() {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🛠️ 方法2：禁用长轮询 (configLongPollTimeout=0)");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            properties.put("configLongPollTimeout", "0");
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ 禁用长轮询的ConfigService创建成功");
            
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            logger.info("📊 读取结果: {}", content != null ? "成功" : "失败");
            if (content != null) {
                logger.info("  - 内容: {}", content);
            }
            
        } catch (Exception e) {
            logger.error("❌ 方法2测试失败: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    private static void testMethod3DisableLocalCache() {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🛠️ 方法3：尝试禁用本地缓存 (enableLocalCache=false)");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            properties.put("enableLocalCache", "false");
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ 尝试禁用本地缓存的ConfigService创建成功");
            
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            logger.info("📊 读取结果: {}", content != null ? "成功" : "失败");
            if (content != null) {
                logger.info("  - 内容: {}", content);
            }
            
        } catch (Exception e) {
            logger.error("❌ 方法3测试失败: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    private static void testMethod4CustomCacheDir() {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🛠️ 方法4：自定义缓存目录到临时位置");
        
        try {
            String tempCacheDir = System.getProperty("java.io.tmpdir") + "/nacos-test-cache-" + System.currentTimeMillis();
            
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            properties.put("JM.SNAPSHOT.PATH", tempCacheDir);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ 自定义缓存目录的ConfigService创建成功");
            logger.info("  - 缓存目录: {}", tempCacheDir);
            
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            logger.info("📊 读取结果: {}", content != null ? "成功" : "失败");
            if (content != null) {
                logger.info("  - 内容: {}", content);
            }
            
            // 检查自定义缓存目录
            Path customCachePath = Paths.get(tempCacheDir);
            if (Files.exists(customCachePath)) {
                logger.info("✅ 自定义缓存目录已创建: {}", customCachePath.toAbsolutePath());
            }
            
        } catch (Exception e) {
            logger.error("❌ 方法4测试失败: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    /**
     * 递归列出目录内容
     */
    private static void listDirectoryContents(Path dir, int depth) throws IOException {
        String indent = "  ".repeat(depth);
        
        try (Stream<Path> paths = Files.list(dir)) {
            paths.forEach(path -> {
                try {
                    if (Files.isDirectory(path)) {
                        logger.info("{}📁 {}/", indent, path.getFileName());
                        if (depth < 3) { // 限制递归深度
                            listDirectoryContents(path, depth + 1);
                        }
                    } else {
                        long size = Files.size(path);
                        logger.info("{}📄 {} ({} bytes)", indent, path.getFileName(), size);
                    }
                } catch (IOException e) {
                    logger.warn("{}❌ 无法读取: {}", indent, path.getFileName());
                }
            });
        }
    }
    
    /**
     * 查找测试配置的缓存文件
     */
    private static void findTestConfigCache(Path cacheDir) {
        logger.info("🔍 查找测试配置的缓存文件...");
        
        try {
            Files.walk(cacheDir)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString();
                    return fileName.contains(TEST_DATA_ID) || 
                           fileName.contains(TARGET_NAMESPACE) ||
                           fileName.contains(TEST_GROUP);
                })
                .forEach(path -> {
                    logger.info("🎯 找到相关缓存文件: {}", path.toAbsolutePath());
                    try {
                        String content = Files.readString(path);
                        logger.info("  - 文件大小: {} bytes", Files.size(path));
                        logger.info("  - 文件内容: {}", content.length() > 100 ? 
                            content.substring(0, 100) + "..." : content);
                    } catch (IOException e) {
                        logger.warn("  - 无法读取文件内容: {}", e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            logger.error("❌ 搜索缓存文件失败: {}", e.getMessage());
        }
    }
}
