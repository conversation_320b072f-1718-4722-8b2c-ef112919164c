package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;

/**
 * Nacos权限测试运行器
 * 基于成功的Java SDK登录测试方式，实现完整的客户端权限验证测试套件
 *
 * 测试背景：
 * - 用户：cf
 * - 权限：cf用户对"cf"命名空间有完整读写权限，对"cf-n12s"命名空间没有任何权限
 */
public class NacosPermissionTestRunner {

    private static final Logger logger = LoggerFactory.getLogger(NacosPermissionTestRunner.class);

    // 测试配置 - 使用与成功登录测试相同的配置方式
    private static final String SERVER_ADDR = TestConfig.SERVER_ADDR;
    private static final String USERNAME = TestConfig.CF_USER_NAME;
    private static final String PASSWORD = TestConfig.CF_USER_TOKEN;

    // 测试命名空间
    private static final String ALLOWED_NAMESPACE = TestConfig.AUTHORIZED_NAMESPACE;      // cf用户有权限的命名空间
    private static final String FORBIDDEN_NAMESPACE = TestConfig.UNAUTHORIZED_NAMESPACE; // cf用户没有权限的命名空间

    // 测试服务配置
    private static final String TEST_SERVICE_NAME = "permission-test-service";
    private static final String TEST_GROUP_NAME = "DEFAULT_GROUP";
    private static final String TEST_INSTANCE_IP = "127.0.0.1";
    private static final int TEST_INSTANCE_PORT = 8080;

    // 测试结果统计
    private int totalTests = 0;
    private int passedTests = 0;
    private int failedTests = 0;

    public static void main(String[] args) {
        NacosPermissionTestRunner runner = new NacosPermissionTestRunner();

        printTestHeader();

        try {
            // 测试1: 正向测试 - cf命名空间（应该成功）
            runner.testAllowedNamespaceOperations();

            printSeparator();

            // 测试2: 负向测试 - cf-n12s命名空间（应该失败）
            runner.testForbiddenNamespaceOperations();

            printSeparator();

            // 测试3: 权限验证总结
            runner.printPermissionTestSummary();

        } catch (Exception e) {
            logger.error("权限测试执行失败", e);
        }

        printTestFooter();
    }

    /**
     * 测试cf命名空间的操作（正向测试 - 应该成功）
     */
    private void testAllowedNamespaceOperations() {
        printSectionHeader("正向测试：cf命名空间操作（应该成功）");

        NamingService cfNamingService = null;

        try {
            // 创建cf命名空间的NamingService
            logger.info("🔧 创建cf命名空间的NamingService...");
            Properties cfProperties = createNamingServiceProperties(ALLOWED_NAMESPACE);
            cfNamingService = NacosFactory.createNamingService(cfProperties);
            logger.info("✅ cf命名空间NamingService创建成功");

            // 测试1: 服务实例注册
            testServiceRegistration(cfNamingService, ALLOWED_NAMESPACE, true);

            // 测试2: 服务实例查询
            testServiceQuery(cfNamingService, ALLOWED_NAMESPACE, true);

            // 测试3: 服务列表获取
            testServiceListQuery(cfNamingService, ALLOWED_NAMESPACE, true);

            // 测试4: 服务实例删除
            testServiceDeregistration(cfNamingService, ALLOWED_NAMESPACE, true);

        } catch (Exception e) {
            logger.error("❌ cf命名空间操作测试失败", e);
            failedTests++;
        } finally {
            if (cfNamingService != null) {
                try {
                    cfNamingService.shutDown();
                    logger.info("🔒 cf命名空间NamingService已关闭");
                } catch (Exception e) {
                    logger.warn("关闭cf命名空间NamingService时出现警告", e);
                }
            }
        }
    }

    /**
     * 测试cf-n12s命名空间的操作（负向测试 - 应该失败）
     */
    private void testForbiddenNamespaceOperations() {
        printSectionHeader("负向测试：cf-n12s命名空间操作（应该失败）");

        NamingService forbiddenNamingService = null;

        try {
            // 创建cf-n12s命名空间的NamingService
            logger.info("🔧 创建cf-n12s命名空间的NamingService...");
            Properties forbiddenProperties = createNamingServiceProperties(FORBIDDEN_NAMESPACE);
            forbiddenNamingService = NacosFactory.createNamingService(forbiddenProperties);
            logger.info("✅ cf-n12s命名空间NamingService创建成功");

            // 测试1: 服务实例注册（应该失败）
            testServiceRegistration(forbiddenNamingService, FORBIDDEN_NAMESPACE, false);

            // 测试2: 服务实例查询（应该失败）
            testServiceQuery(forbiddenNamingService, FORBIDDEN_NAMESPACE, false);

            // 测试3: 服务列表获取（应该失败）
            testServiceListQuery(forbiddenNamingService, FORBIDDEN_NAMESPACE, false);

            // 测试4: 服务实例删除（应该失败）
            testServiceDeregistration(forbiddenNamingService, FORBIDDEN_NAMESPACE, false);

        } catch (Exception e) {
            logger.error("❌ cf-n12s命名空间操作测试失败", e);
            failedTests++;
        } finally {
            if (forbiddenNamingService != null) {
                try {
                    forbiddenNamingService.shutDown();
                    logger.info("🔒 cf-n12s命名空间NamingService已关闭");
                } catch (Exception e) {
                    logger.warn("关闭cf-n12s命名空间NamingService时出现警告", e);
                }
            }
        }
    }
    
    /**
     * 创建NamingService的Properties配置
     */
    private Properties createNamingServiceProperties(String namespace) {
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", namespace);

        logger.info("📋 NamingService配置:");
        logger.info("  serverAddr: {}", SERVER_ADDR);
        logger.info("  username: {}", USERNAME);
        logger.info("  password: {}...", PASSWORD.substring(0, Math.min(20, PASSWORD.length())));
        logger.info("  namespace: {}", namespace);

        return properties;
    }

    /**
     * 测试服务实例注册
     */
    private void testServiceRegistration(NamingService namingService, String namespace, boolean shouldSucceed) {
        totalTests++;
        String testName = String.format("服务实例注册 [%s命名空间]", namespace);

        try {
            logger.info("🔍 测试: {} (预期: {})", testName, shouldSucceed ? "成功" : "失败");

            Instance instance = new Instance();
            instance.setIp(TEST_INSTANCE_IP);
            instance.setPort(TEST_INSTANCE_PORT);
            instance.setServiceName(TEST_SERVICE_NAME);

            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 注册时间: {}", timestamp);
            logger.info("📝 注册实例: {}:{}@{}/{}", TEST_INSTANCE_IP, TEST_INSTANCE_PORT, TEST_SERVICE_NAME, TEST_GROUP_NAME);

            namingService.registerInstance(TEST_SERVICE_NAME, TEST_GROUP_NAME, instance);

            // 等待注册生效
            Thread.sleep(2000);

            if (shouldSucceed) {
                logger.info("✅ {} - 成功 (符合预期)", testName);
                passedTests++;
            } else {
                logger.error("❌ {} - 权限绕过漏洞！应该失败但却成功了", testName);
                failedTests++;
            }

        } catch (NacosException e) {
            if (shouldSucceed) {
                logger.error("❌ {} - 意外失败: {} - {}", testName, e.getErrCode(), e.getErrMsg());
                logger.error("异常详情:", e);
                failedTests++;
            } else {
                logger.info("✅ {} - 正确被拒绝 (符合预期): {} - {}", testName, e.getErrCode(), e.getErrMsg());
                passedTests++;
            }
        } catch (Exception e) {
            logger.error("❌ {} - 系统异常: {}", testName, e.getMessage());
            logger.error("异常详情:", e);
            failedTests++;
        }
    }

    /**
     * 测试服务实例查询
     */
    private void testServiceQuery(NamingService namingService, String namespace, boolean shouldSucceed) {
        totalTests++;
        String testName = String.format("服务实例查询 [%s命名空间]", namespace);

        try {
            logger.info("🔍 测试: {} (预期: {})", testName, shouldSucceed ? "成功" : "失败");

            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 查询时间: {}", timestamp);
            logger.info("🔎 查询服务: {}/{}", TEST_SERVICE_NAME, TEST_GROUP_NAME);
            logger.info("📋 请求参数: serviceName={}, groupName={}, namespace={}", TEST_SERVICE_NAME, TEST_GROUP_NAME, namespace);

            List<Instance> instances = namingService.getAllInstances(TEST_SERVICE_NAME, TEST_GROUP_NAME);

            // 详细输出实例信息
            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());
            for (int i = 0; i < instances.size(); i++) {
                Instance instance = instances.get(i);
                logger.info("  实例[{}]: IP={}, Port={}, Weight={}, Healthy={}, Enabled={}, Metadata={}",
                    i + 1, instance.getIp(), instance.getPort(), instance.getWeight(),
                    instance.isHealthy(), instance.isEnabled(), instance.getMetadata());
            }

            if (shouldSucceed) {
                logger.info("✅ {} - 成功 (符合预期), 实例数量: {}", testName, instances.size());
                passedTests++;
            } else {
                logger.error("❌ {} - 权限绕过漏洞！应该失败但却成功了, 实例数量: {}", testName, instances.size());
                failedTests++;
            }

        } catch (NacosException e) {
            logger.error("🔍 NacosException详情: 错误码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
            if (e.getCause() != null) {
                logger.error("🔍 根本原因: 类型={}, 消息={}", e.getCause().getClass().getName(), e.getCause().getMessage());
            }

            if (shouldSucceed) {
                logger.error("❌ {} - 意外失败: {} - {}", testName, e.getErrCode(), e.getErrMsg());
                logger.error("异常详情:", e);
                failedTests++;
            } else {
                logger.info("✅ {} - 正确被拒绝 (符合预期): {} - {}", testName, e.getErrCode(), e.getErrMsg());
                passedTests++;
            }
        } catch (Exception e) {
            logger.error("🔍 Exception详情: 类型={}, 消息={}", e.getClass().getName(), e.getMessage());
            if (e.getCause() != null) {
                logger.error("🔍 根本原因: 类型={}, 消息={}", e.getCause().getClass().getName(), e.getCause().getMessage());
            }
            logger.error("❌ {} - 系统异常: {}", testName, e.getMessage());
            logger.error("异常详情:", e);
            failedTests++;
        }
    }

    /**
     * 测试服务列表获取
     */
    private void testServiceListQuery(NamingService namingService, String namespace, boolean shouldSucceed) {
        totalTests++;
        String testName = String.format("服务列表获取 [%s命名空间]", namespace);

        try {
            logger.info("🔍 测试: {} (预期: {})", testName, shouldSucceed ? "成功" : "失败");

            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 查询时间: {}", timestamp);
            logger.info("📋 获取服务列表...");
            logger.info("📋 请求参数: pageNo=1, pageSize=10, namespace={}", namespace);

            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);

            // 详细输出服务列表信息
            logger.info("📊 查询结果: 总服务数量={}, 当前页服务数量={}", serviceList.getCount(), serviceList.getData().size());
            logger.info("📋 服务名称列表:");
            for (int i = 0; i < serviceList.getData().size(); i++) {
                logger.info("  服务[{}]: {}", i + 1, serviceList.getData().get(i));
            }

            if (shouldSucceed) {
                logger.info("✅ {} - 成功 (符合预期), 服务数量: {}", testName, serviceList.getCount());
                passedTests++;
            } else {
                logger.error("❌ {} - 权限绕过漏洞！应该失败但却成功了, 服务数量: {}", testName, serviceList.getCount());
                failedTests++;
            }

        } catch (NacosException e) {
            logger.error("🔍 NacosException详情: 错误码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
            if (e.getCause() != null) {
                logger.error("🔍 根本原因: 类型={}, 消息={}", e.getCause().getClass().getName(), e.getCause().getMessage());
            }

            if (shouldSucceed) {
                logger.error("❌ {} - 意外失败: {} - {}", testName, e.getErrCode(), e.getErrMsg());
                logger.error("异常详情:", e);
                failedTests++;
            } else {
                logger.info("✅ {} - 正确被拒绝 (符合预期): {} - {}", testName, e.getErrCode(), e.getErrMsg());
                passedTests++;
            }
        } catch (Exception e) {
            logger.error("🔍 Exception详情: 类型={}, 消息={}", e.getClass().getName(), e.getMessage());
            if (e.getCause() != null) {
                logger.error("🔍 根本原因: 类型={}, 消息={}", e.getCause().getClass().getName(), e.getCause().getMessage());
            }
            logger.error("❌ {} - 系统异常: {}", testName, e.getMessage());
            logger.error("异常详情:", e);
            failedTests++;
        }
    }

    /**
     * 测试服务实例删除
     */
    private void testServiceDeregistration(NamingService namingService, String namespace, boolean shouldSucceed) {
        totalTests++;
        String testName = String.format("服务实例删除 [%s命名空间]", namespace);

        try {
            logger.info("🔍 测试: {} (预期: {})", testName, shouldSucceed ? "成功" : "失败");

            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 删除时间: {}", timestamp);
            logger.info("🗑️ 删除实例: {}:{}@{}/{}", TEST_INSTANCE_IP, TEST_INSTANCE_PORT, TEST_SERVICE_NAME, TEST_GROUP_NAME);

            namingService.deregisterInstance(TEST_SERVICE_NAME, TEST_GROUP_NAME, TEST_INSTANCE_IP, TEST_INSTANCE_PORT);

            // 等待删除生效
            Thread.sleep(2000);

            if (shouldSucceed) {
                logger.info("✅ {} - 成功 (符合预期)", testName);
                passedTests++;
            } else {
                logger.error("❌ {} - 权限绕过漏洞！应该失败但却成功了", testName);
                failedTests++;
            }

        } catch (NacosException e) {
            if (shouldSucceed) {
                logger.error("❌ {} - 意外失败: {} - {}", testName, e.getErrCode(), e.getErrMsg());
                logger.error("异常详情:", e);
                failedTests++;
            } else {
                logger.info("✅ {} - 正确被拒绝 (符合预期): {} - {}", testName, e.getErrCode(), e.getErrMsg());
                passedTests++;
            }
        } catch (Exception e) {
            logger.error("❌ {} - 系统异常: {}", testName, e.getMessage());
            logger.error("异常详情:", e);
            failedTests++;
        }
    }

    /**
     * 打印权限测试总结
     */
    private void printPermissionTestSummary() {
        printSectionHeader("权限验证测试总结");

        logger.info("📊 测试统计:");
        logger.info("总测试数: {}", totalTests);
        logger.info("通过测试: {}", passedTests);
        logger.info("失败测试: {}", failedTests);

        if (totalTests > 0) {
            double passRate = (double) passedTests / totalTests * 100;
            logger.info("通过率: {:.1f}%", passRate);
        }

        logger.info("");
        logger.info("🎯 权限控制验证结果:");

        if (failedTests == 0) {
            logger.info("✅ 所有测试都通过了！");
            logger.info("✅ 权限控制系统工作正常");
            logger.info("✅ 权限绕过漏洞已被完全修复");
            logger.info("✅ cf用户只能访问cf命名空间，无法访问cf-n12s命名空间");
        } else {
            logger.error("❌ 有 {} 个测试失败", failedTests);
            logger.error("⚠️ 权限控制系统可能存在问题");

            if (hasPermissionBypassIssue()) {
                logger.error("🚨 发现权限绕过漏洞！需要进一步修复");
            }
        }

        logger.info("");
        logger.info("📋 测试覆盖范围:");
        logger.info("✅ 服务实例注册权限验证");
        logger.info("✅ 服务实例查询权限验证");
        logger.info("✅ 服务列表获取权限验证");
        logger.info("✅ 服务实例删除权限验证");
        logger.info("✅ 跨命名空间权限隔离验证");
    }
    
    /**
     * 分析测试失败原因
     */
    private static void analyzeFailures(List<PermissionTestCase.TestResult> results) {
        System.out.println("\n=== 失败分析 ===");
        
        boolean hasPermissionBypass = false;
        boolean hasUnexpectedFailure = false;
        
        for (PermissionTestCase.TestResult result : results) {
            if (!result.isSuccess()) {
                String message = result.getMessage().toLowerCase();
                
                if (message.contains("权限绕过") || message.contains("成功执行")) {
                    hasPermissionBypass = true;
                    System.out.println("🚨 发现权限绕过问题: " + result.getTestName());
                } else {
                    hasUnexpectedFailure = true;
                    System.out.println("❌ 意外失败: " + result.getTestName());
                }
            }
        }
        
        if (hasPermissionBypass) {
            System.out.println("\n⚠️  权限绕过漏洞仍然存在！需要进一步修复。");
        }
        
        if (hasUnexpectedFailure) {
            System.out.println("\n⚠️  存在意外的测试失败，可能是环境或配置问题。");
        }
        
        System.out.println("===============");
    }
    
    /**
     * 休眠指定毫秒数
     */
    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("休眠被中断", e);
        }
    }

    /**
     * 检查是否存在权限绕过问题
     */
    private boolean hasPermissionBypassIssue() {
        // 如果负向测试（应该失败的测试）意外成功了，说明存在权限绕过问题
        return failedTests > 0;
    }

    // 工具方法
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  Nacos 权限验证测试套件                        ║");
        logger.info("║              Permission Validation Test Suite               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证权限控制系统的有效性                              ║");
        logger.info("║ 测试用户: {}                                                 ║", USERNAME);
        logger.info("║ 有权限命名空间: {}                                            ║", ALLOWED_NAMESPACE);
        logger.info("║ 无权限命名空间: {}                                          ║", FORBIDDEN_NAMESPACE);
        logger.info("║ 服务器地址: {}                                    ║", SERVER_ADDR);
        logger.info("║ 测试时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }

    private static void printTestFooter() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    权限验证测试完成                            ║");
        logger.info("║              Permission Test Completed                      ║");
        logger.info("║ 完成时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }

    private static void printSectionHeader(String title) {
        logger.info("");
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ {}                                                    │", String.format("%-59s", title));
        logger.info("└─────────────────────────────────────────────────────────────┘");
    }

    private static void printSeparator() {
        logger.info("");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("");
    }

    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
