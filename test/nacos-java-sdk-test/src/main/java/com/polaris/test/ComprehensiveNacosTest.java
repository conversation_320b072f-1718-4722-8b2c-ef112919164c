package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 综合Nacos测试用例
 * 
 * 功能：
 * - 同时测试注册中心和配置中心的所有核心操作
 * - 验证不同命名空间的权限控制
 * - 生成详细的测试报告
 */
public class ComprehensiveNacosTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveNacosTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "**************:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间配置
    private static final String[] NAMESPACES = {"cf", "cf-ns", "cf-n12s"};
    private static final String[] NAMESPACE_DESCRIPTIONS = {
        "cf命名空间（读写权限）",
        "cf-ns命名空间（无权限）", 
        "cf-n12s命名空间（只读权限）"
    };
    
    // 权限预期结果：[注册中心读, 注册中心写, 配置中心读, 配置中心写]
    private static final boolean[][] EXPECTED_PERMISSIONS = {
        {true, true, true, true},   // cf: 读写权限
        {false, false, false, false}, // cf-ns: 无权限
        {true, false, true, false}   // cf-n12s: 只读权限
    };
    
    // 测试数据
    private static final String SERVICE_NAME = "comprehensive-test-service";
    private static final String CONFIG_DATA_ID = "comprehensive-test-config";
    private static final String CONFIG_GROUP = "COMPREHENSIVE_GROUP";
    
    // 测试统计
    private static final AtomicInteger totalTests = new AtomicInteger(0);
    private static final AtomicInteger passedTests = new AtomicInteger(0);
    private static final AtomicInteger failedTests = new AtomicInteger(0);
    
    public static void main(String[] args) {
        try {
            printTestHeader();
            runComprehensiveTest();
            printTestSummary();
        } catch (Exception e) {
            logger.error("综合测试执行失败", e);
        }
    }
    
    /**
     * 打印测试头部信息
     */
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    综合Nacos测试用例                          ║");
        logger.info("║            Comprehensive Nacos Test Suite               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证注册中心和配置中心的权限控制                   ║");
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("║                                                              ║");
        logger.info("║ 测试配置:                                                    ║");
        logger.info("║   - 服务器地址: {}", SERVER_ADDR);
        logger.info("║   - 用户名: {}", USERNAME);
        logger.info("║   - 测试命名空间: {}", String.join(", ", NAMESPACES));
        logger.info("║                                                              ║");
        logger.info("║ 权限配置:                                                    ║");
        logger.info("║   - cf: 读写权限（注册中心+配置中心）                        ║");
        logger.info("║   - cf-ns: 无权限（注册中心+配置中心）                       ║");
        logger.info("║   - cf-n12s: 只读权限（注册中心+配置中心）                   ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 运行综合测试
     */
    private static void runComprehensiveTest() {
        for (int i = 0; i < NAMESPACES.length; i++) {
            String namespace = NAMESPACES[i];
            String description = NAMESPACE_DESCRIPTIONS[i];
            boolean[] expectedPermissions = EXPECTED_PERMISSIONS[i];
            
            logger.info("┌─────────────────────────────────────────────────────────────┐");
            logger.info("│ 测试命名空间: {} - {}", namespace, description);
            logger.info("└─────────────────────────────────────────────────────────────┘");
            
            try {
                testNamespace(namespace, expectedPermissions);
            } catch (Exception e) {
                logger.error("命名空间 {} 测试失败", namespace, e);
            }
            
            logger.info("");
        }
    }
    
    /**
     * 测试单个命名空间
     */
    private static void testNamespace(String namespace, boolean[] expectedPermissions) throws Exception {
        // 创建服务实例
        NamingService namingService = createNamingService(namespace);
        ConfigService configService = createConfigService(namespace);
        
        logger.info("🔧 服务实例创建成功 - namespace: {}", namespace);
        
        // 测试注册中心操作
        testNamingService(namingService, namespace, expectedPermissions[0], expectedPermissions[1]);
        
        // 测试配置中心操作
        testConfigService(configService, namespace, expectedPermissions[2], expectedPermissions[3]);
    }
    
    /**
     * 创建NamingService
     */
    private static NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", namespace);
        
        return NacosFactory.createNamingService(properties);
    }
    
    /**
     * 创建ConfigService
     */
    private static ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", namespace);
        
        return NacosFactory.createConfigService(properties);
    }
    
    /**
     * 测试注册中心服务
     */
    private static void testNamingService(NamingService namingService, String namespace, 
                                        boolean expectedRead, boolean expectedWrite) {
        logger.info("📋 开始测试注册中心操作 - namespace: {}", namespace);
        
        // 测试服务注册（写操作）
        testServiceRegister(namingService, namespace, expectedWrite);
        
        // 测试服务发现（读操作）
        testServiceDiscovery(namingService, namespace, expectedRead);
        
        // 测试服务注销（写操作）
        testServiceDeregister(namingService, namespace, expectedWrite);
        
        // 测试健康检查状态验证（读操作）
        testHealthCheck(namingService, namespace, expectedRead);
    }
    
    /**
     * 测试配置中心服务
     */
    private static void testConfigService(ConfigService configService, String namespace,
                                        boolean expectedRead, boolean expectedWrite) {
        logger.info("📋 开始测试配置中心操作 - namespace: {}", namespace);
        
        // 测试配置发布（写操作）
        testConfigPublish(configService, namespace, expectedWrite);
        
        // 测试配置获取（读操作）
        testConfigGet(configService, namespace, expectedRead);
        
        // 测试配置更新（写操作）
        testConfigUpdate(configService, namespace, expectedWrite);
        
        // 测试配置监听器（读操作）
        testConfigListener(configService, namespace, expectedRead);
        
        // 测试配置删除（写操作）
        testConfigDelete(configService, namespace, expectedWrite);
    }
    
    /**
     * 测试服务注册
     */
    private static void testServiceRegister(NamingService namingService, String namespace, boolean expected) {
        String testName = "服务注册";
        totalTests.incrementAndGet();
        
        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");
            
            Instance instance = new Instance();
            instance.setIp("127.0.0.1");
            instance.setPort(8080);
            instance.setWeight(1.0);
            instance.setHealthy(true);
            
            namingService.registerInstance(SERVICE_NAME, instance);
            
            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            }
            
        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }
    
    /**
     * 测试服务发现
     */
    private static void testServiceDiscovery(NamingService namingService, String namespace, boolean expected) {
        String testName = "服务发现";
        totalTests.incrementAndGet();
        
        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");
            
            List<Instance> instances = namingService.getAllInstances(SERVICE_NAME);
            
            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                logger.info("🔍 发现服务实例数量: {}", instances.size());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                logger.info("🔍 发现服务实例数量: {}", instances.size());
                failedTests.incrementAndGet();
            }
            
        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }
    
    /**
     * 测试服务注销
     */
    private static void testServiceDeregister(NamingService namingService, String namespace, boolean expected) {
        String testName = "服务注销";
        totalTests.incrementAndGet();
        
        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");
            
            namingService.deregisterInstance(SERVICE_NAME, "127.0.0.1", 8080);
            
            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            }
            
        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }
    
    /**
     * 测试健康检查状态验证
     */
    private static void testHealthCheck(NamingService namingService, String namespace, boolean expected) {
        String testName = "健康检查状态验证";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            List<Instance> healthyInstances = namingService.selectInstances(SERVICE_NAME, true);

            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                logger.info("🔍 健康实例数量: {}", healthyInstances.size());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                logger.info("🔍 健康实例数量: {}", healthyInstances.size());
                failedTests.incrementAndGet();
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 测试配置发布
     */
    private static void testConfigPublish(ConfigService configService, String namespace, boolean expected) {
        String testName = "配置发布";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            String configContent = generateConfigContent("初始配置");
            boolean result = configService.publishConfig(CONFIG_DATA_ID, CONFIG_GROUP, configContent);

            // 操作成功
            if (result && expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                logger.info("🔍 配置内容长度: {} 字符", configContent.length());
                passedTests.incrementAndGet();
            } else if (!result && !expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                passedTests.incrementAndGet();
            } else if (result && !expected) {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                failedTests.incrementAndGet();
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 测试配置获取
     */
    private static void testConfigGet(ConfigService configService, String namespace, boolean expected) {
        String testName = "配置获取";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            String content = configService.getConfig(CONFIG_DATA_ID, CONFIG_GROUP, 5000);

            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                if (content != null) {
                    logger.info("🔍 获取配置内容长度: {} 字符", content.length());
                } else {
                    logger.info("🔍 获取配置内容: null（配置不存在）");
                }
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 测试配置更新
     */
    private static void testConfigUpdate(ConfigService configService, String namespace, boolean expected) {
        String testName = "配置更新";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            String updatedContent = generateConfigContent("更新配置");
            boolean result = configService.publishConfig(CONFIG_DATA_ID, CONFIG_GROUP, updatedContent);

            // 操作成功
            if (result && expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                logger.info("🔍 更新配置内容长度: {} 字符", updatedContent.length());
                passedTests.incrementAndGet();
            } else if (!result && !expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                passedTests.incrementAndGet();
            } else if (result && !expected) {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                failedTests.incrementAndGet();
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 测试配置监听器
     */
    private static void testConfigListener(ConfigService configService, String namespace, boolean expected) {
        String testName = "配置监听器";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            Listener listener = new Listener() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    logger.info("🔔 配置监听器收到配置变更: 内容长度={} 字符",
                              configInfo != null ? configInfo.length() : 0);
                }

                @Override
                public Executor getExecutor() {
                    return null;
                }
            };

            configService.addListener(CONFIG_DATA_ID, CONFIG_GROUP, listener);

            // 操作成功
            if (expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                logger.info("🔍 监听器添加成功");
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            }

            // 移除监听器
            try {
                configService.removeListener(CONFIG_DATA_ID, CONFIG_GROUP, listener);
            } catch (Exception e) {
                // 忽略移除监听器的异常
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 测试配置删除
     */
    private static void testConfigDelete(ConfigService configService, String namespace, boolean expected) {
        String testName = "配置删除";
        totalTests.incrementAndGet();

        try {
            logger.info("🚀 正在执行: {} (namespace: {})", testName, namespace);
            logger.info("🎯 预期结果: {}", expected ? "成功" : "失败");

            boolean result = configService.removeConfig(CONFIG_DATA_ID, CONFIG_GROUP);

            // 操作成功
            if (result && expected) {
                logger.info("✅ {} 测试通过: 预期成功, 实际成功", testName);
                passedTests.incrementAndGet();
            } else if (!result && !expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                passedTests.incrementAndGet();
            } else if (result && !expected) {
                logger.info("❌ {} 测试失败: 预期失败, 实际成功", testName);
                failedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                failedTests.incrementAndGet();
            }

        } catch (NacosException e) {
            // 操作失败
            if (!expected) {
                logger.info("✅ {} 测试通过: 预期失败, 实际失败", testName);
                logger.info("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                passedTests.incrementAndGet();
            } else {
                logger.info("❌ {} 测试失败: 预期成功, 实际失败", testName);
                logger.error("🔍 失败详情: 错误代码={}, 错误消息={}", e.getErrCode(), e.getErrMsg());
                failedTests.incrementAndGet();
            }
        }
    }

    /**
     * 生成配置内容
     */
    private static String generateConfigContent(String type) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 综合Nacos测试 - ").append(type).append("\n");
        sb.append("# 生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        sb.append("# 时间戳: ").append(System.currentTimeMillis()).append("\n");
        sb.append("\n");
        sb.append("# 应用配置\n");
        sb.append("app.name=comprehensive-nacos-test\n");
        sb.append("app.version=1.0.0\n");
        sb.append("app.environment=test\n");
        sb.append("\n");
        sb.append("# 数据库配置\n");
        sb.append("database.url=*************************************");
        sb.append("database.username=test_user\n");
        sb.append("database.pool.size=10\n");
        sb.append("\n");
        sb.append("# 缓存配置\n");
        sb.append("cache.enabled=true\n");
        sb.append("cache.ttl=3600\n");

        return sb.toString();
    }

    /**
     * 打印测试总结
     */
    private static void printTestSummary() {
        int total = totalTests.get();
        int passed = passedTests.get();
        int failed = failedTests.get();
        double successRate = total > 0 ? (double) passed / total * 100 : 0;

        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                      综合测试报告                            ║");
        logger.info("║               Comprehensive Test Report              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试完成时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("║                                                              ║");
        logger.info("║ 测试统计:                                                    ║");
        logger.info("║   - 总测试数: {} 个", total);
        logger.info("║   - 通过测试: {} 个", passed);
        logger.info("║   - 失败测试: {} 个", failed);
        logger.info("║   - 成功率: {:.1f}%", successRate);
        logger.info("║                                                              ║");
        logger.info("║ 权限验证结果:                                                ║");

        for (int i = 0; i < NAMESPACES.length; i++) {
            String namespace = NAMESPACES[i];
            String description = NAMESPACE_DESCRIPTIONS[i];
            boolean[] expected = EXPECTED_PERMISSIONS[i];

            logger.info("║   - {}: {}", namespace, description);
            logger.info("║     注册中心读权限: {} | 注册中心写权限: {}",
                      expected[0] ? "✅" : "❌", expected[1] ? "✅" : "❌");
            logger.info("║     配置中心读权限: {} | 配置中心写权限: {}",
                      expected[2] ? "✅" : "❌", expected[3] ? "✅" : "❌");
        }

        logger.info("║                                                              ║");
        if (successRate >= 90) {
            logger.info("║ 🎉 测试结果: 优秀 (成功率 >= 90%)                            ║");
        } else if (successRate >= 80) {
            logger.info("║ ✅ 测试结果: 良好 (成功率 >= 80%)                            ║");
        } else if (successRate >= 70) {
            logger.info("║ ⚠️  测试结果: 一般 (成功率 >= 70%)                            ║");
        } else {
            logger.info("║ ❌ 测试结果: 需要改进 (成功率 < 70%)                         ║");
        }
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("感谢您使用综合Nacos测试用例！");
    }
}
