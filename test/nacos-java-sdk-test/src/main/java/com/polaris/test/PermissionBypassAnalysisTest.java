package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 权限绕过分析测试
 * 
 * 目标：分析cf用户读取cf命名空间配置时权限绕过的原因
 * 场景：
 * - cf命名空间中存在配置
 * - cf用户已被取消cf命名空间的读权限
 * - cf用户被赋予cf-n12s命名空间的读写权限
 * - 测试cf用户是否能绕过权限读取cf命名空间的配置
 */
public class PermissionBypassAnalysisTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionBypassAnalysisTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试配置信息（从之前的测试中获取）
    private static final String TEST_DATA_ID = "permission-verify-config-1757338541716";
    private static final String TEST_GROUP = "PERMISSION_VERIFY_GROUP";
    
    // 测试命名空间
    private static final String CF_NAMESPACE = "cf";        // 应该无读权限
    private static final String CF_N12S_NAMESPACE = "cf-n12s"; // 应该有读写权限
    
    public static void main(String[] args) {
        PermissionBypassAnalysisTest test = new PermissionBypassAnalysisTest();
        test.runPermissionBypassAnalysis();
    }
    
    public void runPermissionBypassAnalysis() {
        printTestHeader();
        
        // 测试1：验证cf命名空间的权限状态
        testCfNamespacePermissions();
        
        logger.info("");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("");
        
        // 测试2：验证cf-n12s命名空间的权限状态
        testCfN12sNamespacePermissions();
        
        logger.info("");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("");
        
        // 测试3：详细分析权限绕过行为
        analyzePermissionBypassBehavior();
        
        printTestSummary();
    }
    
    /**
     * 测试cf命名空间的权限状态
     */
    private void testCfNamespacePermissions() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              测试1：cf命名空间权限状态验证                     ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("🎯 预期：cf用户对cf命名空间应该无读权限");
        logger.info("📋 测试配置：dataId={}, group={}", TEST_DATA_ID, TEST_GROUP);
        logger.info("");
        
        ConfigService configService = null;
        
        try {
            // 创建cf命名空间的ConfigService
            configService = createConfigService(CF_NAMESPACE);
            logger.info("✅ cf命名空间ConfigService创建成功");
            
            // 测试读取已知存在的配置
            logger.info("🔍 测试读取已知存在的配置...");
            testConfigRead(configService, CF_NAMESPACE, TEST_DATA_ID, TEST_GROUP, false);
            
            logger.info("");
            
            // 测试读取不存在的配置
            logger.info("🔍 测试读取不存在的配置...");
            String nonExistentDataId = "non-existent-config-" + System.currentTimeMillis();
            testConfigRead(configService, CF_NAMESPACE, nonExistentDataId, "NON_EXISTENT_GROUP", false);
            
            logger.info("");
            
            // 测试配置发布（写权限）
            logger.info("🔍 测试配置发布（写权限）...");
            testConfigPublish(configService, CF_NAMESPACE);
            
        } catch (Exception e) {
            logger.error("❌ cf命名空间权限测试异常", e);
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 测试cf-n12s命名空间的权限状态
     */
    private void testCfN12sNamespacePermissions() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            测试2：cf-n12s命名空间权限状态验证                  ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("🎯 预期：cf用户对cf-n12s命名空间应该有读写权限");
        logger.info("");
        
        ConfigService configService = null;
        
        try {
            // 创建cf-n12s命名空间的ConfigService
            configService = createConfigService(CF_N12S_NAMESPACE);
            logger.info("✅ cf-n12s命名空间ConfigService创建成功");
            
            // 测试配置发布（写权限）
            logger.info("🔍 测试配置发布（写权限）...");
            boolean publishSuccess = testConfigPublish(configService, CF_N12S_NAMESPACE);
            
            if (publishSuccess) {
                logger.info("");
                
                // 测试读取刚发布的配置
                logger.info("🔍 测试读取刚发布的配置...");
                String testDataId = "bypass-analysis-config-" + System.currentTimeMillis();
                testConfigRead(configService, CF_N12S_NAMESPACE, testDataId, "BYPASS_ANALYSIS_GROUP", true);
                
                logger.info("");
                
                // 清理测试配置
                logger.info("🧹 清理测试配置...");
                try {
                    configService.removeConfig(testDataId, "BYPASS_ANALYSIS_GROUP");
                    logger.info("✅ 测试配置清理成功");
                } catch (Exception e) {
                    logger.warn("⚠️ 清理测试配置失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ cf-n12s命名空间权限测试异常", e);
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 详细分析权限绕过行为
     */
    private void analyzePermissionBypassBehavior() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              测试3：权限绕过行为详细分析                       ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("🔍 分析权限绕过的可能原因：");
        logger.info("");
        
        // 分析1：权限缓存问题
        logger.info("📋 分析1：权限缓存问题");
        logger.info("  - Nacos可能存在权限缓存机制");
        logger.info("  - 权限变更后缓存未及时刷新");
        logger.info("  - 建议：等待更长时间或重启Nacos服务");
        logger.info("");
        
        // 分析2：权限继承问题
        logger.info("📋 分析2：权限继承问题");
        logger.info("  - cf用户可能从其他角色继承了权限");
        logger.info("  - 检查用户的角色分配和角色权限");
        logger.info("  - 建议：检查用户管理中的角色配置");
        logger.info("");
        
        // 分析3：权限检查逻辑问题
        logger.info("📋 分析3：权限检查逻辑问题");
        logger.info("  - Nacos的权限检查可能存在逻辑缺陷");
        logger.info("  - 某些操作可能绕过了权限验证");
        logger.info("  - 建议：升级Nacos版本或报告bug");
        logger.info("");
        
        // 分析4：配置同步问题
        logger.info("📋 分析4：配置同步问题");
        logger.info("  - 权限配置可能未同步到所有节点");
        logger.info("  - 集群环境下的一致性问题");
        logger.info("  - 建议：检查集群状态和配置同步");
        logger.info("");
        
        // 进行实时权限测试
        performRealTimePermissionTest();
    }
    
    /**
     * 进行实时权限测试
     */
    private void performRealTimePermissionTest() {
        logger.info("🧪 进行实时权限测试（多次尝试）：");
        logger.info("");
        
        for (int i = 1; i <= 3; i++) {
            logger.info("📋 第{}次测试：", i);
            
            ConfigService configService = null;
            try {
                configService = createConfigService(CF_NAMESPACE);
                
                // 测试读取已知配置
                String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 3000);
                
                if (content != null) {
                    logger.error("❌ 权限绕过确认：成功读取到配置内容");
                    logger.error("  - 内容长度: {} 字符", content.length());
                    logger.error("  - 这确实是权限控制的问题");
                } else {
                    logger.info("✅ 权限控制正常：配置内容为null");
                }
                
            } catch (NacosException e) {
                logger.info("✅ 权限控制正常：操作被拒绝");
                logger.info("  - 错误代码: {}", e.getErrCode());
                logger.info("  - 错误消息: {}", e.getErrMsg());
            } catch (Exception e) {
                logger.warn("⚠️ 测试异常: {}", e.getMessage());
            } finally {
                if (configService != null) {
                    try {
                        configService.shutDown();
                    } catch (Exception e) {
                        // 忽略关闭异常
                    }
                }
            }
            
            if (i < 3) {
                try {
                    Thread.sleep(2000); // 等待2秒再进行下一次测试
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }
    
    /**
     * 测试配置读取
     */
    private void testConfigRead(ConfigService configService, String namespace, String dataId, String group, boolean expectedSuccess) {
        try {
            logger.info("🔍 读取配置: namespace={}, dataId={}, group={}", namespace, dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
            
            String content = configService.getConfig(dataId, group, 5000);
            
            if (content != null) {
                if (expectedSuccess) {
                    logger.info("✅ 配置读取成功 - 符合预期");
                    logger.info("📋 内容长度: {} 字符", content.length());
                } else {
                    logger.error("❌ 配置读取意外成功 - 权限绕过！");
                    logger.error("📋 内容长度: {} 字符", content.length());
                    logger.error("🚨 这是一个严重的权限控制问题！");
                }
            } else {
                if (expectedSuccess) {
                    logger.warn("⚠️ 配置读取成功但内容为null");
                } else {
                    logger.info("✅ 配置读取返回null - 可能是权限控制或配置不存在");
                }
            }
            
        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ 配置读取正确被拒绝 - 符合预期");
                logger.info("🔒 权限错误: 代码={}, 消息={}", e.getErrCode(), e.getErrMsg());
            } else {
                logger.error("❌ 配置读取意外失败 - 不符合预期");
                logger.error("🔍 错误详情: 代码={}, 消息={}", e.getErrCode(), e.getErrMsg());
            }
        }
    }
    
    /**
     * 测试配置发布
     */
    private boolean testConfigPublish(ConfigService configService, String namespace) {
        try {
            String testDataId = "bypass-analysis-config-" + System.currentTimeMillis();
            String testContent = generateTestContent(namespace);
            
            logger.info("🔍 发布配置: namespace={}, dataId={}", namespace, testDataId);
            
            boolean result = configService.publishConfig(testDataId, "BYPASS_ANALYSIS_GROUP", testContent);
            
            if (result) {
                logger.info("✅ 配置发布成功");
                return true;
            } else {
                logger.warn("⚠️ 配置发布失败");
                return false;
            }
            
        } catch (NacosException e) {
            logger.info("❌ 配置发布被拒绝");
            logger.info("🔒 权限错误: 代码={}, 消息={}", e.getErrCode(), e.getErrMsg());
            return false;
        }
    }
    
    // ==================== 辅助方法 ====================
    
    private ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createConfigService(properties);
    }
    
    private String generateTestContent(String namespace) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 权限绕过分析测试配置\n");
        sb.append("# 命名空间: ").append(namespace).append("\n");
        sb.append("# 生成时间: ").append(getCurrentTime()).append("\n");
        sb.append("# 用途: 分析权限绕过问题\n");
        sb.append("\n");
        sb.append("test.purpose=permission-bypass-analysis\n");
        sb.append("test.namespace=").append(namespace).append("\n");
        sb.append("test.timestamp=").append(System.currentTimeMillis()).append("\n");
        
        return sb.toString();
    }
    
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  权限绕过分析测试用例                          ║");
        logger.info("║            Permission Bypass Analysis Test Suite           ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分析cf用户读取cf命名空间配置时的权限绕过问题            ║");
        logger.info("║ 场景: cf用户已被取消cf命名空间读权限，但仍能读取配置          ║");
        logger.info("║ 测试时间: {}", getCurrentTime());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private void printTestSummary() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    权限绕过分析报告                           ║");
        logger.info("║            Permission Bypass Analysis Report              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 分析完成时间: {}", getCurrentTime());
        logger.info("║                                                              ║");
        logger.info("║ 关键发现:                                                    ║");
        logger.info("║   请查看上述详细分析结果                                      ║");
        logger.info("║   重点关注权限绕过的具体表现和可能原因                        ║");
        logger.info("║                                                              ║");
        logger.info("║ 修复建议:                                                    ║");
        logger.info("║   1. 检查权限缓存和同步机制                                  ║");
        logger.info("║   2. 验证用户角色和权限继承                                  ║");
        logger.info("║   3. 考虑升级Nacos版本                                       ║");
        logger.info("║   4. 在应用层增加额外的权限检查                              ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("感谢您使用权限绕过分析测试用例！");
    }
}
