package com.polaris.test;

/**
 * 测试配置类
 * 包含所有测试环境的配置信息
 */
public class TestConfig {
    
    // 服务端配置
    public static final String SERVER_ADDR = "180.76.109.137:8848";
    
    // cf 用户配置（有权限用户）
    // 注意：根据Polaris官方文档，PASSWORD字段应该使用北极星用户/用户组的资源访问凭据Token
    // USERNAME字段可以是任意值，PASSWORD字段是实际的访问凭据Token
    // 使用原始token（不进行URL编码），因为Java SDK会自动处理编码
    public static final String CF_USER_TOKEN = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    public static final String CF_USER_NAME = "cf";

    // 命名空间配置
    public static final String AUTHORIZED_NAMESPACE = "cf";      // cf 用户有权限的命名空间
    public static final String UNAUTHORIZED_NAMESPACE = "cf-n12s"; // cf 用户无权限的命名空间（与curl测试保持一致）
    
    // 测试服务配置
    public static final String TEST_SERVICE_PREFIX = "java-sdk-test-service";
    public static final String TEST_GROUP = "DEFAULT_GROUP";
    public static final String TEST_CLUSTER = "DEFAULT";
    
    // 测试实例配置
    public static final String TEST_IP = "*************";
    public static final int TEST_PORT_BASE = 9000;
    
    // 测试超时配置
    public static final int OPERATION_TIMEOUT_MS = 5000;
    public static final int RETRY_COUNT = 3;
    public static final int RETRY_DELAY_MS = 1000;
    
    /**
     * 获取测试服务名
     */
    public static String getTestServiceName(String suffix) {
        return TEST_SERVICE_PREFIX + "-" + suffix + "-" + System.currentTimeMillis();
    }
    
    /**
     * 获取测试端口
     */
    public static int getTestPort(int offset) {
        return TEST_PORT_BASE + offset;
    }
    
    /**
     * 打印测试配置信息
     */
    public static void printConfig() {
        System.out.println("=== Nacos Java SDK 测试配置 ===");
        System.out.println("服务端地址: " + SERVER_ADDR);
        System.out.println("测试用户: " + CF_USER_NAME);
        System.out.println("有权限命名空间: " + AUTHORIZED_NAMESPACE);
        System.out.println("无权限命名空间: " + UNAUTHORIZED_NAMESPACE);
        System.out.println("测试服务前缀: " + TEST_SERVICE_PREFIX);
        System.out.println("测试IP: " + TEST_IP);
        System.out.println("测试端口范围: " + TEST_PORT_BASE + "+");
        System.out.println("操作超时: " + OPERATION_TIMEOUT_MS + "ms");
        System.out.println("===============================");
    }
}
