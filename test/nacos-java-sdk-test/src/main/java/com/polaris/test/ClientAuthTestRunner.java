package com.polaris.test;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户端鉴权测试运行器
 * 用于测试clientOpen开关对权限控制的影响
 */
public class ClientAuthTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientAuthTestRunner.class);
    
    // 测试结果统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    private static final List<String> testResults = new ArrayList<>();
    
    public static void main(String[] args) {
        printTestHeader();
        
        try {
            // 打印测试配置
            TestUserConfig.printConfig();
            
            // 验证三个命名空间的权限设置
            verifyNamespacePermissions();
            
            // 输出测试总结
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("❌ 客户端鉴权测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 验证三个命名空间的权限设置
     */
    private static void verifyNamespacePermissions() {
        logger.info("🔍 开始验证命名空间权限设置...");
        logger.info("");
        
        String[] namespaces = TestUserConfig.getAllNamespaces();
        
        for (String namespace : namespaces) {
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("🔍 测试命名空间: {} ({})", namespace, TestUserConfig.getNamespacePermission(namespace));
            logger.info("═══════════════════════════════════════════════════════════════");
            
            // 测试注册中心权限
            testNamingServicePermissions(namespace);
            
            // 测试配置中心权限
            testConfigServicePermissions(namespace);
            
            logger.info("");
        }
    }
    
    /**
     * 测试注册中心权限
     */
    private static void testNamingServicePermissions(String namespace) {
        logger.info("🚀 测试注册中心权限...");
        
        NamingService namingService = null;
        try {
            // 创建NamingService
            namingService = ClientAuthTestUtil.createNamingService(namespace);
            logger.info("✅ NamingService创建成功");
            
            // 测试服务列表查询（读权限）
            testServiceListQuery(namingService, namespace);
            
            // 测试服务注册（写权限）
            testServiceRegistration(namingService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 注册中心权限测试失败: {}", e.getMessage());
            recordTestResult(namespace, "注册中心连接", false, e.getMessage());
        } finally {
            if (namingService != null) {
                ClientAuthTestUtil.shutdown(namingService);
            }
        }
    }
    
    /**
     * 测试配置中心权限
     */
    private static void testConfigServicePermissions(String namespace) {
        logger.info("📝 测试配置中心权限...");
        
        ConfigService configService = null;
        try {
            // 创建ConfigService
            configService = ClientAuthTestUtil.createConfigService(namespace);
            logger.info("✅ ConfigService创建成功");
            
            // 测试配置读取（读权限）
            testConfigRead(configService, namespace);
            
            // 测试配置发布（写权限）
            testConfigPublish(configService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 配置中心权限测试失败: {}", e.getMessage());
            recordTestResult(namespace, "配置中心连接", false, e.getMessage());
        } finally {
            if (configService != null) {
                ClientAuthTestUtil.shutdown(configService);
            }
        }
    }
    
    /**
     * 测试服务列表查询
     */
    private static void testServiceListQuery(NamingService namingService, String namespace) {
        String testName = "服务列表查询";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        try {
            ListView<String> services = ClientAuthTestUtil.getServicesOfServer(namingService, 1, 10, TestUserConfig.TEST_GROUP);
            
            if (services != null) {
                logger.info("✅ 服务列表查询成功，服务数量: {}", services.getCount());
                recordTestResult(namespace, testName, true, "查询成功，服务数量: " + services.getCount());
            } else {
                logger.error("❌ 服务列表查询失败");
                recordTestResult(namespace, testName, false, "查询返回null");
            }
            
        } catch (Exception e) {
            boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）: {}", testName, e.getMessage());
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无读权限）", testName);
                logger.info("🔒 权限错误详情: {}", e.getMessage());
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试服务注册
     */
    private static void testServiceRegistration(NamingService namingService, String namespace) {
        String testName = "服务注册";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String serviceName = TestUserConfig.getTestServiceName("verify");
        String ip = TestUserConfig.TEST_IP;
        int port = TestUserConfig.getTestPort(1);
        
        try {
            boolean result = ClientAuthTestUtil.registerInstance(namingService, serviceName, ip, port, TestUserConfig.TEST_GROUP);
            
            if (result) {
                logger.info("✅ 服务注册成功");
                recordTestResult(namespace, testName, true, "注册成功");
                
                // 清理：注销服务
                ClientAuthTestUtil.deregisterInstance(namingService, serviceName, ip, port, TestUserConfig.TEST_GROUP);
            } else {
                logger.error("❌ 服务注册失败");
                recordTestResult(namespace, testName, false, "注册失败");
            }
            
        } catch (Exception e) {
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）: {}", testName, e.getMessage());
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无写权限）", testName);
                logger.info("🔒 权限错误详情: {}", e.getMessage());
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试配置读取
     */
    private static void testConfigRead(ConfigService configService, String namespace) {
        String testName = "配置读取";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String dataId = "test-config";
        String group = TestUserConfig.TEST_CONFIG_GROUP;
        
        try {
            String content = ClientAuthTestUtil.getConfig(configService, dataId, group, 3000);
            
            logger.info("✅ 配置读取成功，内容: {}", content != null ? "有内容" : "无内容");
            recordTestResult(namespace, testName, true, "读取成功");
            
        } catch (Exception e) {
            boolean shouldAllow = TestUserConfig.shouldAllowRead(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）: {}", testName, e.getMessage());
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无读权限）", testName);
                logger.info("🔒 权限错误详情: {}", e.getMessage());
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试配置发布
     */
    private static void testConfigPublish(ConfigService configService, String namespace) {
        String testName = "配置发布";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String dataId = TestUserConfig.getTestConfigId("verify");
        String group = TestUserConfig.TEST_CONFIG_GROUP;
        String content = "test-content-" + System.currentTimeMillis();
        
        try {
            boolean result = ClientAuthTestUtil.publishConfig(configService, dataId, group, content);
            
            if (result) {
                logger.info("✅ 配置发布成功");
                recordTestResult(namespace, testName, true, "发布成功");
                
                // 清理：删除配置
                ClientAuthTestUtil.removeConfig(configService, dataId, group);
            } else {
                logger.error("❌ 配置发布失败");
                recordTestResult(namespace, testName, false, "发布失败");
            }
            
        } catch (Exception e) {
            boolean shouldAllow = TestUserConfig.shouldAllowWrite(namespace);
            if (shouldAllow) {
                logger.error("❌ {} 失败 - 不符合预期（应该成功）: {}", testName, e.getMessage());
                recordTestResult(namespace, testName, false, "权限验证异常: " + e.getMessage());
            } else {
                logger.info("✅ {} 正确被拒绝 - 符合预期（无写权限）", testName);
                logger.info("🔒 权限错误详情: {}", e.getMessage());
                recordTestResult(namespace, testName, true, "正确拒绝: " + e.getMessage());
            }
        }
    }
    
    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testName, boolean passed, String message) {
        totalTests++;
        if (passed) {
            passedTests++;
        } else {
            failedTests++;
        }
        
        String result = String.format("%s - %s - %s: %s", 
                                    namespace, testName, 
                                    passed ? "PASS" : "FAIL", 
                                    message);
        testResults.add(result);
    }
    
    /**
     * 打印测试头部信息
     */
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              客户端鉴权功能验证测试                            ║");
        logger.info("║           Client Authentication Test                     ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证三个命名空间的权限设置                           ║");
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 打印测试总结
     */
    private static void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    测试结果总结                              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}                                                ║", totalTests);
        logger.info("║ 通过测试: {}                                                ║", passedTests);
        logger.info("║ 失败测试: {}                                                ║", failedTests);
        logger.info("║ 成功率: {:.1f}%                                            ║", 
                   totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        logger.info("📋 详细测试结果:");
        for (String result : testResults) {
            logger.info("  - {}", result);
        }
        
        logger.info("");
        logger.info("🎯 测试完成时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
}
