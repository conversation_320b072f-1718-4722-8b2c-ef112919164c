package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;
import java.util.Scanner;

/**
 * 交互式配置中心测试程序
 * 
 * 功能：
 * - 分步执行配置CRUD操作
 * - 每个步骤后暂停等待用户验证
 * - 提供详细的操作信息和控制台验证指导
 */
public class InteractiveConfigCenterTest {
    
    private static final Logger logger = LoggerFactory.getLogger(InteractiveConfigCenterTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String NAMESPACE = "cf-n12s"; // 使用新赋予读写权限的命名空间
    
    // 测试数据配置 - 使用cf-n12s命名空间下的配置分组
    private static final String DATA_ID = "interactive-test-config";
    private static final String GROUP = "DEFAULT_GROUP"; // 使用默认配置分组
    
    private static ConfigService configService;
    private static Scanner scanner;
    private static String currentConfigContent;
    
    public static void main(String[] args) {
        scanner = new Scanner(System.in);
        
        try {
            printWelcomeMessage();
            initializeConfigService();
            runInteractiveTest();
        } catch (Exception e) {
            logger.error("交互式测试程序执行失败", e);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
    }
    
    /**
     * 打印欢迎信息
     */
    private static void printWelcomeMessage() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              交互式配置中心测试程序                           ║");
        logger.info("║         Interactive Config Center Test Program           ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分步执行配置CRUD操作并在控制台验证实际效果              ║");
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("║                                                              ║");
        logger.info("║ 测试配置信息:                                                ║");
        logger.info("║   - 服务器地址: {}", SERVER_ADDR);
        logger.info("║   - 用户名: {}", USERNAME);
        logger.info("║   - 命名空间: {} (新赋予读写权限)", NAMESPACE);
        logger.info("║   - 数据ID: {}", DATA_ID);
        logger.info("║   - 分组: {} (默认配置分组)", GROUP);
        logger.info("║                                                              ║");
        logger.info("║ 说明:                                                        ║");
        logger.info("║   - 测试cf-n12s命名空间的新读写权限                          ║");
        logger.info("║   - 验证权限变更后的配置CRUD操作                             ║");
        logger.info("║   - 删除配置后不会删除配置分组，这是正常行为                 ║");
        logger.info("║                                                              ║");
        logger.info("║ 控制台验证地址:                                              ║");
        logger.info("║   http://{}/#/configurationManagement", SERVER_ADDR);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 初始化ConfigService
     */
    private static void initializeConfigService() throws NacosException {
        logger.info("🔧 正在初始化ConfigService...");
        
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", NAMESPACE);
        
        configService = NacosFactory.createConfigService(properties);
        
        logger.info("✅ ConfigService初始化成功");
        logger.info("🔑 认证信息: username={}, namespace={}", USERNAME, NAMESPACE);
        logger.info("");
    }
    
    /**
     * 运行交互式测试
     */
    private static void runInteractiveTest() {
        try {
            // 第一阶段：创建和查询
            executeCreateAndQueryPhase();
            
            // 第二阶段：更新操作
            executeUpdatePhase();
            
            // 第三阶段：删除操作
            executeDeletePhase();
            
            // 测试完成
            printCompletionMessage();
            
        } catch (Exception e) {
            logger.error("交互式测试执行过程中发生错误", e);
        }
    }
    
    /**
     * 第一阶段：创建和查询
     */
    private static void executeCreateAndQueryPhase() throws Exception {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│                    第一阶段：创建和查询                      │");
        logger.info("└─────────────────────────────────────────────────────────────┘");
        logger.info("");
        
        // 生成初始配置内容
        String timestamp = String.valueOf(System.currentTimeMillis());
        currentConfigContent = generateInitialConfig(timestamp);
        
        logger.info("📝 即将执行配置创建操作");
        logger.info("📋 配置信息:");
        logger.info("  - 命名空间: {}", NAMESPACE);
        logger.info("  - 数据ID: {}", DATA_ID);
        logger.info("  - 分组: {}", GROUP);
        logger.info("  - 配置内容:");
        printConfigContent(currentConfigContent, "    ");
        logger.info("");
        
        waitForUserConfirmation("按回车键开始执行配置创建操作...");
        
        // 执行配置创建
        boolean createResult = executeConfigCreate(currentConfigContent);
        
        if (createResult) {
            logger.info("✅ 配置创建成功！");
            
            // 等待配置生效
            Thread.sleep(2000);
            
            // 验证配置创建结果
            executeConfigQuery("验证创建结果");
            
            logger.info("");
            logger.info("🔍 请在控制台验证配置是否已创建:");
            logger.info("  1. 打开控制台: http://{}/#/configurationManagement", SERVER_ADDR);
            logger.info("  2. 选择命名空间: {}", NAMESPACE);
            logger.info("  3. 查找配置: Data ID = {}, Group = {} (默认配置分组)", DATA_ID, GROUP);
            logger.info("  4. 确认配置内容是否与上面显示的一致");
            logger.info("");
            
            waitForUserConfirmation("确认在控制台看到配置后，按回车键继续...");
            
        } else {
            logger.error("❌ 配置创建失败，无法继续后续测试");
            return;
        }
    }
    
    /**
     * 第二阶段：更新操作
     */
    private static void executeUpdatePhase() throws Exception {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│                    第二阶段：更新操作                        │");
        logger.info("└─────────────────────────────────────────────────────────────┘");
        logger.info("");
        
        logger.info("📝 准备执行配置更新操作");
        logger.info("请输入 'update' 或 '更新' 来执行更新操作:");
        
        String userInput = waitForSpecificInput(new String[]{"update", "更新", "执行更新"});
        
        // 生成更新后的配置内容
        String timestamp = String.valueOf(System.currentTimeMillis());
        String updatedContent = generateUpdatedConfig(timestamp);
        
        logger.info("📋 配置更新信息:");
        logger.info("  - 命名空间: {}", NAMESPACE);
        logger.info("  - 数据ID: {}", DATA_ID);
        logger.info("  - 分组: {}", GROUP);
        logger.info("");
        logger.info("📄 更新前配置内容:");
        printConfigContent(currentConfigContent, "    ");
        logger.info("");
        logger.info("📄 更新后配置内容:");
        printConfigContent(updatedContent, "    ");
        logger.info("");
        
        waitForUserConfirmation("按回车键开始执行配置更新操作...");
        
        // 执行配置更新
        boolean updateResult = executeConfigUpdate(updatedContent);
        
        if (updateResult) {
            logger.info("✅ 配置更新成功！");
            currentConfigContent = updatedContent;
            
            // 等待配置生效
            Thread.sleep(2000);
            
            // 验证配置更新结果
            executeConfigQuery("验证更新结果");
            
            logger.info("");
            logger.info("🔍 请在控制台验证配置是否已更新:");
            logger.info("  1. 刷新控制台页面");
            logger.info("  2. 查看配置: Data ID = {}, Group = {} (默认配置分组)", DATA_ID, GROUP);
            logger.info("  3. 确认配置内容已更新为新内容");
            logger.info("  4. 注意版本号应该已增加");
            logger.info("");
            
            waitForUserConfirmation("确认在控制台看到配置更新后，按回车键继续...");
            
        } else {
            logger.error("❌ 配置更新失败");
        }
    }
    
    /**
     * 生成初始配置内容
     */
    private static String generateInitialConfig(String timestamp) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 交互式配置中心测试 - 初始配置\n");
        sb.append("# 创建时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        sb.append("# 时间戳: ").append(timestamp).append("\n");
        sb.append("\n");
        sb.append("# 应用配置\n");
        sb.append("app.name=interactive-config-test\n");
        sb.append("app.version=1.0.0\n");
        sb.append("app.environment=test\n");
        sb.append("\n");
        sb.append("# 数据库配置\n");
        sb.append("database.url=*************************************");
        sb.append("database.username=test_user\n");
        sb.append("database.pool.size=10\n");
        sb.append("\n");
        sb.append("# 缓存配置\n");
        sb.append("cache.enabled=true\n");
        sb.append("cache.ttl=3600\n");
        
        return sb.toString();
    }
    
    /**
     * 生成更新后的配置内容
     */
    private static String generateUpdatedConfig(String timestamp) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 交互式配置中心测试 - 更新配置\n");
        sb.append("# 更新时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        sb.append("# 时间戳: ").append(timestamp).append("\n");
        sb.append("\n");
        sb.append("# 应用配置 (已更新)\n");
        sb.append("app.name=interactive-config-test\n");
        sb.append("app.version=2.0.0\n");  // 版本号更新
        sb.append("app.environment=production\n");  // 环境更新
        sb.append("app.debug=false\n");  // 新增配置
        sb.append("\n");
        sb.append("# 数据库配置 (已更新)\n");
        sb.append("database.url=***************************************");  // URL更新
        sb.append("database.username=prod_user\n");  // 用户名更新
        sb.append("database.pool.size=20\n");  // 连接池大小更新
        sb.append("database.timeout=30000\n");  // 新增超时配置
        sb.append("\n");
        sb.append("# 缓存配置 (已更新)\n");
        sb.append("cache.enabled=true\n");
        sb.append("cache.ttl=7200\n");  // TTL更新
        sb.append("cache.max.size=1000\n");  // 新增最大大小配置
        
        return sb.toString();
    }
    
    /**
     * 打印配置内容
     */
    private static void printConfigContent(String content, String prefix) {
        String[] lines = content.split("\n");
        for (String line : lines) {
            logger.info("{}{}", prefix, line);
        }
    }
    
    /**
     * 等待用户确认
     */
    private static void waitForUserConfirmation(String message) {
        logger.info("⏸️  {}", message);
        scanner.nextLine();
    }
    
    /**
     * 等待特定输入
     */
    private static String waitForSpecificInput(String[] validInputs) {
        while (true) {
            String input = scanner.nextLine().trim();
            for (String validInput : validInputs) {
                if (validInput.equalsIgnoreCase(input)) {
                    return input;
                }
            }
            logger.info("❌ 无效输入，请输入以下选项之一: {}", String.join(", ", validInputs));
        }
    }

    /**
     * 第三阶段：删除操作
     */
    private static void executeDeletePhase() throws Exception {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│                    第三阶段：删除操作                        │");
        logger.info("└─────────────────────────────────────────────────────────────┘");
        logger.info("");

        logger.info("🗑️ 准备执行配置删除操作");
        logger.info("请输入 'delete' 或 '删除' 来执行删除操作:");

        String userInput = waitForSpecificInput(new String[]{"delete", "删除", "执行删除"});

        logger.info("📋 配置删除信息:");
        logger.info("  - 命名空间: {}", NAMESPACE);
        logger.info("  - 数据ID: {}", DATA_ID);
        logger.info("  - 分组: {}", GROUP);
        logger.info("");
        logger.info("⚠️  警告: 此操作将永久删除配置，无法恢复！");
        logger.info("");

        waitForUserConfirmation("确认要删除配置，按回车键开始执行删除操作...");

        // 执行配置删除
        boolean deleteResult = executeConfigDelete();

        if (deleteResult) {
            logger.info("✅ 配置删除成功！");

            // 等待配置生效
            Thread.sleep(2000);

            // 验证配置删除结果
            executeConfigQuery("验证删除结果");

            logger.info("");
            logger.info("🔍 请在控制台验证配置是否已删除:");
            logger.info("  1. 刷新控制台页面");
            logger.info("  2. 查找配置: Data ID = {}, Group = {} (默认配置分组)", DATA_ID, GROUP);
            logger.info("  3. 确认配置已不存在（应该找不到该配置）");
            logger.info("  4. 注意：配置分组 {} 仍然存在，这是正常行为", GROUP);
            logger.info("");

            waitForUserConfirmation("确认在控制台看到配置已删除后，按回车键继续...");

        } else {
            logger.error("❌ 配置删除失败");
        }
    }

    /**
     * 执行配置创建
     */
    private static boolean executeConfigCreate(String content) {
        try {
            logger.info("🚀 正在执行配置创建...");
            logger.info("⏰ 操作时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            boolean result = configService.publishConfig(DATA_ID, GROUP, content);

            if (result) {
                logger.info("📊 创建结果: ✅ 成功");
                logger.info("🔍 操作详情:");
                logger.info("  - 操作类型: 配置创建 (publishConfig)");
                logger.info("  - 配置路径: namespace={}, dataId={}, group={}", NAMESPACE, DATA_ID, GROUP);
                logger.info("  - 内容长度: {} 字符", content.length());
            } else {
                logger.info("📊 创建结果: ❌ 失败");
                logger.info("🔍 失败详情:");
                logger.info("  - 操作类型: 配置创建 (publishConfig)");
                logger.info("  - 失败原因: 操作返回false，可能是权限不足或其他业务限制");
            }

            return result;

        } catch (NacosException e) {
            logger.error("📊 创建结果: ❌ 异常");
            logger.error("🔍 异常详情:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 错误代码: {}", e.getErrCode());
            logger.error("  - 错误消息: {}", e.getErrMsg());
            return false;
        }
    }

    /**
     * 执行配置更新
     */
    private static boolean executeConfigUpdate(String content) {
        try {
            logger.info("🚀 正在执行配置更新...");
            logger.info("⏰ 操作时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            boolean result = configService.publishConfig(DATA_ID, GROUP, content);

            if (result) {
                logger.info("📊 更新结果: ✅ 成功");
                logger.info("🔍 操作详情:");
                logger.info("  - 操作类型: 配置更新 (publishConfig)");
                logger.info("  - 配置路径: namespace={}, dataId={}, group={}", NAMESPACE, DATA_ID, GROUP);
                logger.info("  - 内容长度: {} 字符", content.length());
            } else {
                logger.info("📊 更新结果: ❌ 失败");
                logger.info("🔍 失败详情:");
                logger.info("  - 操作类型: 配置更新 (publishConfig)");
                logger.info("  - 失败原因: 操作返回false，可能是权限不足或其他业务限制");
            }

            return result;

        } catch (NacosException e) {
            logger.error("📊 更新结果: ❌ 异常");
            logger.error("🔍 异常详情:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 错误代码: {}", e.getErrCode());
            logger.error("  - 错误消息: {}", e.getErrMsg());
            return false;
        }
    }

    /**
     * 执行配置删除
     */
    private static boolean executeConfigDelete() {
        try {
            logger.info("🚀 正在执行配置删除...");
            logger.info("⏰ 操作时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            boolean result = configService.removeConfig(DATA_ID, GROUP);

            if (result) {
                logger.info("📊 删除结果: ✅ 成功");
                logger.info("🔍 操作详情:");
                logger.info("  - 操作类型: 配置删除 (removeConfig)");
                logger.info("  - 配置路径: namespace={}, dataId={}, group={}", NAMESPACE, DATA_ID, GROUP);
            } else {
                logger.info("📊 删除结果: ❌ 失败");
                logger.info("🔍 失败详情:");
                logger.info("  - 操作类型: 配置删除 (removeConfig)");
                logger.info("  - 失败原因: 操作返回false，可能是权限不足或配置不存在");
            }

            return result;

        } catch (NacosException e) {
            logger.error("📊 删除结果: ❌ 异常");
            logger.error("🔍 异常详情:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 错误代码: {}", e.getErrCode());
            logger.error("  - 错误消息: {}", e.getErrMsg());
            return false;
        }
    }

    /**
     * 执行配置查询
     */
    private static void executeConfigQuery(String purpose) {
        try {
            logger.info("🔍 正在执行配置查询 ({})", purpose);
            logger.info("⏰ 查询时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            String content = configService.getConfig(DATA_ID, GROUP, 5000);

            if (content != null) {
                logger.info("📊 查询结果: ✅ 配置存在");
                logger.info("🔍 配置详情:");
                logger.info("  - 配置路径: namespace={}, dataId={}, group={}", NAMESPACE, DATA_ID, GROUP);
                logger.info("  - 内容长度: {} 字符", content.length());
                logger.info("  - 配置内容:");
                printConfigContent(content, "    ");
            } else {
                logger.info("📊 查询结果: ❌ 配置不存在");
                logger.info("🔍 查询详情:");
                logger.info("  - 配置路径: namespace={}, dataId={}, group={}", NAMESPACE, DATA_ID, GROUP);
                logger.info("  - 结果: 配置不存在或已被删除");
            }

        } catch (NacosException e) {
            logger.error("📊 查询结果: ❌ 异常");
            logger.error("🔍 异常详情:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 错误代码: {}", e.getErrCode());
            logger.error("  - 错误消息: {}", e.getErrMsg());
        }
    }

    /**
     * 打印测试完成信息
     */
    private static void printCompletionMessage() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              交互式配置中心测试完成                           ║");
        logger.info("║         Interactive Config Center Test Completed         ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 🎉 恭喜！您已成功完成所有配置CRUD操作测试                     ║");
        logger.info("║                                                              ║");
        logger.info("║ 测试总结:                                                    ║");
        logger.info("║   ✅ 第一阶段: 配置创建和查询 - 完成                         ║");
        logger.info("║   ✅ 第二阶段: 配置更新操作 - 完成                           ║");
        logger.info("║   ✅ 第三阶段: 配置删除操作 - 完成                           ║");
        logger.info("║                                                              ║");
        logger.info("║ 验证要点:                                                    ║");
        logger.info("║   - SDK操作与控制台显示一致性 ✅                             ║");
        logger.info("║   - 配置内容的准确性 ✅                                      ║");
        logger.info("║   - 操作时间的实时性 ✅                                      ║");
        logger.info("║   - 权限验证的有效性 ✅                                      ║");
        logger.info("║                                                              ║");
        logger.info("║ 测试完成时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("感谢您使用交互式配置中心测试程序！");
    }
}
