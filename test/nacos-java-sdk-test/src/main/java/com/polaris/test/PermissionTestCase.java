package com.polaris.test;

import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 权限测试用例类
 * 包含各种权限控制场景的测试方法
 */
public class PermissionTestCase {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionTestCase.class);
    
    /**
     * 测试结果类
     */
    public static class TestResult {
        private final String testName;
        private final boolean success;
        private final String message;
        private final Exception exception;
        
        public TestResult(String testName, boolean success, String message, Exception exception) {
            this.testName = testName;
            this.success = success;
            this.message = message;
            this.exception = exception;
        }
        
        public String getTestName() { return testName; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Exception getException() { return exception; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s: %s", 
                               success ? "PASS" : "FAIL", testName, message);
        }
    }
    
    /**
     * 测试有权限用户的服务注册操作（应该成功）
     */
    public static TestResult testAuthorizedServiceRegistration() {
        String testName = "有权限用户服务注册";
        NamingService namingService = null;
        long startTime = System.currentTimeMillis();

        logger.info("=== 开始执行测试：{} ===", testName);
        logger.info("测试开始时间：{}", new java.util.Date(startTime));

        try {
            // 记录测试参数
            logger.info("测试参数配置：");
            logger.info("  - 目标命名空间：{} (有权限)", TestConfig.AUTHORIZED_NAMESPACE);
            logger.info("  - 用户Token：{}...", TestConfig.CF_USER_TOKEN.substring(0, 20));
            logger.info("  - 服务端地址：{}", TestConfig.SERVER_ADDR);

            // 创建有权限的客户端
            logger.info("步骤1：创建 Nacos 客户端连接");
            logger.debug("正在建立到服务端的连接...");
            namingService = NacosClientUtil.createNamingService(
                TestConfig.AUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);
            logger.info("✓ Nacos 客户端连接建立成功");

            String serviceName = TestConfig.getTestServiceName("authorized-register");
            int port = TestConfig.getTestPort(1);

            logger.info("步骤2：准备服务注册");
            logger.info("  - 服务名称：{}", serviceName);
            logger.info("  - 服务IP：{}", TestConfig.TEST_IP);
            logger.info("  - 服务端口：{}", port);
            logger.info("  - 服务分组：{}", TestConfig.TEST_GROUP);
            logger.info("  - 服务集群：{}", TestConfig.TEST_CLUSTER);

            // 尝试注册服务
            logger.info("步骤3：执行服务注册操作");
            logger.debug("向服务端发送注册请求...");
            boolean result = NacosClientUtil.registerInstance(
                namingService, serviceName, TestConfig.TEST_IP, port,
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

            if (result) {
                logger.info("✓ 服务注册操作返回成功");

                // 验证服务是否真的注册成功
                logger.info("步骤4：验证服务注册结果");
                logger.debug("查询已注册的服务实例...");
                List<Instance> instances = NacosClientUtil.getAllInstances(
                    namingService, serviceName, TestConfig.TEST_GROUP);

                if (instances != null && !instances.isEmpty()) {
                    logger.info("✓ 服务注册验证成功，发现 {} 个实例", instances.size());
                    for (int i = 0; i < instances.size(); i++) {
                        Instance instance = instances.get(i);
                        logger.debug("  实例{}：{}:{}, 健康状态：{}, 权重：{}",
                                   i+1, instance.getIp(), instance.getPort(),
                                   instance.isHealthy(), instance.getWeight());
                    }

                    long endTime = System.currentTimeMillis();
                    logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                    logger.info("=== 测试结果：{} - 成功 ===", testName);

                    return new TestResult(testName, true,
                        "服务注册成功，实例数量: " + instances.size(), null);
                } else {
                    logger.error("✗ 服务注册验证失败：注册操作返回成功但查询不到实例");
                    logger.error("可能的原因：1) 注册延迟 2) 权限问题 3) 服务端异常");

                    long endTime = System.currentTimeMillis();
                    logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                    logger.info("=== 测试结果：{} - 失败 ===", testName);

                    return new TestResult(testName, false,
                        "服务注册返回成功但查询不到实例", null);
                }
            } else {
                logger.error("✗ 服务注册操作失败");
                logger.error("这是意外的结果，有权限用户的注册操作应该成功");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 失败 ===", testName);

                return new TestResult(testName, false, "服务注册失败", null);
            }

        } catch (Exception e) {
            logger.error("✗ 测试执行过程中发生异常：{}", e.getMessage());
            logger.error("异常类型：{}", e.getClass().getSimpleName());
            logger.debug("异常堆栈：", e);

            long endTime = System.currentTimeMillis();
            logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
            logger.info("=== 测试结果：{} - 异常 ===", testName);

            return new TestResult(testName, false,
                "测试执行异常: " + e.getMessage(), e);
        } finally {
            logger.debug("清理资源：关闭 Nacos 客户端连接");
            NacosClientUtil.shutdown(namingService);
            logger.debug("资源清理完成");
        }
    }
    
    /**
     * 测试无权限用户的服务注册操作（应该被拒绝）
     */
    public static TestResult testUnauthorizedServiceRegistration() {
        String testName = "无权限用户服务注册";
        NamingService namingService = null;
        long startTime = System.currentTimeMillis();

        logger.info("=== 开始执行测试：{} ===", testName);
        logger.info("测试开始时间：{}", new java.util.Date(startTime));
        logger.info("🎯 测试目标：验证权限绕过漏洞是否已修复");

        try {
            // 记录测试参数
            logger.info("测试参数配置：");
            logger.info("  - 目标命名空间：{} (无权限)", TestConfig.UNAUTHORIZED_NAMESPACE);
            logger.info("  - 用户Token：{}...", TestConfig.CF_USER_TOKEN.substring(0, 20));
            logger.info("  - 服务端地址：{}", TestConfig.SERVER_ADDR);
            logger.warn("⚠️  注意：此用户对目标命名空间无权限，操作应被拒绝");

            // 创建无权限的客户端（使用无权限的命名空间）
            logger.info("步骤1：创建 Nacos 客户端连接");
            logger.debug("正在建立到服务端的连接...");
            namingService = NacosClientUtil.createNamingService(
                TestConfig.UNAUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);
            logger.info("✓ Nacos 客户端连接建立成功");

            String serviceName = TestConfig.getTestServiceName("unauthorized-register");
            int port = TestConfig.getTestPort(2);

            logger.info("步骤2：准备服务注册（预期被拒绝）");
            logger.info("  - 服务名称：{}", serviceName);
            logger.info("  - 服务IP：{}", TestConfig.TEST_IP);
            logger.info("  - 服务端口：{}", port);
            logger.info("  - 服务分组：{}", TestConfig.TEST_GROUP);
            logger.info("  - 服务集群：{}", TestConfig.TEST_CLUSTER);

            // 尝试注册服务
            logger.info("步骤3：执行服务注册操作（权限验证测试）");
            logger.debug("向服务端发送注册请求，期望被权限控制拒绝...");
            boolean result = NacosClientUtil.registerInstance(
                namingService, serviceName, TestConfig.TEST_IP, port,
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

            if (result) {
                logger.error("🚨 权限绕过漏洞！无权限用户成功注册了服务");
                logger.error("这表明权限控制系统存在严重安全漏洞");
                logger.error("服务注册不应该成功，但实际返回了成功状态");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 发现安全漏洞 ===", testName);

                return new TestResult(testName, false,
                    "权限绕过！无权限用户成功注册了服务", null);
            } else {
                logger.info("✓ 权限控制正常：无权限用户的服务注册被正确拒绝");
                logger.info("服务注册操作返回失败，符合预期");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 成功 ===", testName);

                return new TestResult(testName, true,
                    "无权限用户的服务注册被正确拒绝", null);
            }

        } catch (Exception e) {
            logger.info("捕获到异常：{}", e.getMessage());
            logger.debug("异常类型：{}", e.getClass().getSimpleName());

            // 权限被拒绝时会抛出异常，这是预期的行为
            if (e.getMessage().contains("no permission") ||
                e.getMessage().contains("access is not approved") ||
                e.getMessage().contains("权限") ||
                e.getMessage().contains("permission")) {

                logger.info("✓ 权限控制正常：捕获到权限拒绝异常");
                logger.info("异常信息：{}", e.getMessage());
                logger.info("这是预期的行为，表明权限控制系统正常工作");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 成功 ===", testName);

                return new TestResult(testName, true,
                    "无权限用户的服务注册被正确拒绝: " + e.getMessage(), null);
            } else {
                logger.error("✗ 测试执行过程中发生意外异常：{}", e.getMessage());
                logger.error("这不是预期的权限拒绝异常，可能是其他问题");
                logger.debug("异常堆栈：", e);

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 异常 ===", testName);

                return new TestResult(testName, false,
                    "测试执行异常: " + e.getMessage(), e);
            }
        } finally {
            logger.debug("清理资源：关闭 Nacos 客户端连接");
            NacosClientUtil.shutdown(namingService);
            logger.debug("资源清理完成");
        }
    }
    
    /**
     * 测试有权限用户的服务注销操作（应该成功）
     */
    public static TestResult testAuthorizedServiceDeregistration() {
        String testName = "有权限用户服务注销";
        NamingService namingService = null;
        
        try {
            // 创建有权限的客户端
            namingService = NacosClientUtil.createNamingService(
                TestConfig.AUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);
            
            String serviceName = TestConfig.getTestServiceName("authorized-deregister");
            int port = TestConfig.getTestPort(3);
            
            // 先注册一个服务
            boolean registerResult = NacosClientUtil.registerInstance(
                namingService, serviceName, TestConfig.TEST_IP, port, 
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);
            
            if (!registerResult) {
                return new TestResult(testName, false, "前置服务注册失败", null);
            }
            
            // 再注销这个服务
            boolean deregisterResult = NacosClientUtil.deregisterInstance(
                namingService, serviceName, TestConfig.TEST_IP, port, 
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);
            
            if (deregisterResult) {
                return new TestResult(testName, true, "服务注销成功", null);
            } else {
                return new TestResult(testName, false, "服务注销失败", null);
            }
            
        } catch (Exception e) {
            return new TestResult(testName, false, 
                "测试执行异常: " + e.getMessage(), e);
        } finally {
            NacosClientUtil.shutdown(namingService);
        }
    }
    
    /**
     * 测试无权限用户的服务注销操作（应该被拒绝）
     */
    public static TestResult testUnauthorizedServiceDeregistration() {
        String testName = "无权限用户服务注销";
        NamingService namingService = null;
        long startTime = System.currentTimeMillis();

        logger.info("=== 开始执行测试：{} ===", testName);
        logger.info("测试开始时间：{}", new java.util.Date(startTime));
        logger.info("🎯 测试目标：验证权限绕过漏洞修复（关键测试）");
        logger.warn("⚠️  这是权限绕过漏洞的核心测试场景");

        try {
            // 记录测试参数
            logger.info("测试参数配置：");
            logger.info("  - 目标命名空间：{} (无权限)", TestConfig.UNAUTHORIZED_NAMESPACE);
            logger.info("  - 用户Token：{}...", TestConfig.CF_USER_TOKEN.substring(0, 20));
            logger.info("  - 服务端地址：{}", TestConfig.SERVER_ADDR);
            logger.warn("⚠️  注意：此用户对目标命名空间无权限，注销操作应被拒绝");

            // 创建无权限的客户端
            logger.info("步骤1：创建 Nacos 客户端连接");
            logger.debug("正在建立到服务端的连接...");
            namingService = NacosClientUtil.createNamingService(
                TestConfig.UNAUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);
            logger.info("✓ Nacos 客户端连接建立成功");

            String serviceName = TestConfig.getTestServiceName("unauthorized-deregister");
            int port = TestConfig.getTestPort(4);

            logger.info("步骤2：准备服务注销（模拟攻击场景）");
            logger.info("  - 目标服务名称：{}", serviceName);
            logger.info("  - 目标服务IP：{}", TestConfig.TEST_IP);
            logger.info("  - 目标服务端口：{}", port);
            logger.info("  - 服务分组：{}", TestConfig.TEST_GROUP);
            logger.info("  - 服务集群：{}", TestConfig.TEST_CLUSTER);
            logger.warn("🔍 模拟攻击场景：尝试删除其他命名空间的服务实例");

            // 尝试注销一个可能存在的服务（模拟攻击场景）
            logger.info("步骤3：执行服务注销操作（权限验证测试）");
            logger.debug("向服务端发送注销请求，期望被权限控制拒绝...");
            boolean result = NacosClientUtil.deregisterInstance(
                namingService, serviceName, TestConfig.TEST_IP, port,
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

            if (result) {
                logger.error("🚨 严重安全漏洞！无权限用户成功注销了服务");
                logger.error("这表明权限绕过漏洞仍然存在！");
                logger.error("攻击者可以删除其他命名空间的服务实例");
                logger.error("服务注销不应该成功，但实际返回了成功状态");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 发现严重安全漏洞 ===", testName);

                return new TestResult(testName, false,
                    "权限绕过！无权限用户成功注销了服务", null);
            } else {
                logger.info("✓ 权限控制正常：无权限用户的服务注销被正确拒绝");
                logger.info("服务注销操作返回失败，符合预期");
                logger.info("权限绕过漏洞修复生效");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 成功 ===", testName);

                return new TestResult(testName, true,
                    "无权限用户的服务注销被正确拒绝", null);
            }

        } catch (Exception e) {
            logger.info("捕获到异常：{}", e.getMessage());
            logger.debug("异常类型：{}", e.getClass().getSimpleName());

            // 权限被拒绝时会抛出异常，这是预期的行为
            if (e.getMessage().contains("no permission") ||
                e.getMessage().contains("access is not approved") ||
                e.getMessage().contains("权限") ||
                e.getMessage().contains("permission")) {

                logger.info("✓ 权限控制正常：捕获到权限拒绝异常");
                logger.info("异常信息：{}", e.getMessage());
                logger.info("这是预期的行为，表明权限绕过漏洞已修复");

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 成功 ===", testName);

                return new TestResult(testName, true,
                    "无权限用户的服务注销被正确拒绝: " + e.getMessage(), null);
            } else {
                logger.error("✗ 测试执行过程中发生意外异常：{}", e.getMessage());
                logger.error("这不是预期的权限拒绝异常，可能是其他问题");
                logger.debug("异常堆栈：", e);

                long endTime = System.currentTimeMillis();
                logger.info("测试执行完成，耗时：{}ms", endTime - startTime);
                logger.info("=== 测试结果：{} - 异常 ===", testName);

                return new TestResult(testName, false,
                    "测试执行异常: " + e.getMessage(), e);
            }
        } finally {
            logger.debug("清理资源：关闭 Nacos 客户端连接");
            NacosClientUtil.shutdown(namingService);
            logger.debug("资源清理完成");
        }
    }

    /**
     * 测试服务发现功能（应该正常工作）
     */
    public static TestResult testServiceDiscovery() {
        String testName = "服务发现功能";
        NamingService namingService = null;

        try {
            // 创建有权限的客户端
            namingService = NacosClientUtil.createNamingService(
                TestConfig.AUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);

            String serviceName = TestConfig.getTestServiceName("discovery-test");
            int port = TestConfig.getTestPort(5);

            // 先注册一个服务
            boolean registerResult = NacosClientUtil.registerInstance(
                namingService, serviceName, TestConfig.TEST_IP, port,
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

            if (!registerResult) {
                return new TestResult(testName, false, "前置服务注册失败", null);
            }

            // 测试服务发现
            List<Instance> instances = NacosClientUtil.getAllInstances(
                namingService, serviceName, TestConfig.TEST_GROUP);

            if (instances != null && !instances.isEmpty()) {
                Instance instance = instances.get(0);
                if (TestConfig.TEST_IP.equals(instance.getIp()) && port == instance.getPort()) {
                    return new TestResult(testName, true,
                        "服务发现成功，找到匹配的实例", null);
                } else {
                    return new TestResult(testName, false,
                        "服务发现返回了错误的实例信息", null);
                }
            } else {
                return new TestResult(testName, false,
                    "服务发现失败，未找到任何实例", null);
            }

        } catch (Exception e) {
            return new TestResult(testName, false,
                "测试执行异常: " + e.getMessage(), e);
        } finally {
            NacosClientUtil.shutdown(namingService);
        }
    }

    /**
     * 测试跨命名空间的服务访问（边界情况）
     */
    public static TestResult testCrossNamespaceAccess() {
        String testName = "跨命名空间服务访问";
        NamingService authorizedService = null;
        NamingService unauthorizedService = null;

        try {
            // 创建有权限的客户端
            authorizedService = NacosClientUtil.createNamingService(
                TestConfig.AUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);

            // 创建无权限的客户端
            unauthorizedService = NacosClientUtil.createNamingService(
                TestConfig.UNAUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);

            String serviceName = TestConfig.getTestServiceName("cross-namespace");
            int port = TestConfig.getTestPort(6);

            // 在有权限的命名空间中注册服务
            boolean registerResult = NacosClientUtil.registerInstance(
                authorizedService, serviceName, TestConfig.TEST_IP, port,
                TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

            if (!registerResult) {
                return new TestResult(testName, false, "在有权限命名空间注册服务失败", null);
            }

            // 尝试从无权限的命名空间访问服务
            List<Instance> instances = NacosClientUtil.getAllInstances(
                unauthorizedService, serviceName, TestConfig.TEST_GROUP);

            // 跨命名空间访问应该返回空结果（不同命名空间的服务是隔离的）
            if (instances == null || instances.isEmpty()) {
                return new TestResult(testName, true,
                    "跨命名空间访问正确返回空结果，命名空间隔离正常", null);
            } else {
                return new TestResult(testName, false,
                    "跨命名空间访问返回了不应该访问到的服务实例", null);
            }

        } catch (Exception e) {
            return new TestResult(testName, false,
                "测试执行异常: " + e.getMessage(), e);
        } finally {
            NacosClientUtil.shutdown(authorizedService);
            NacosClientUtil.shutdown(unauthorizedService);
        }
    }

    /**
     * 测试批量操作的权限控制
     */
    public static TestResult testBatchOperationPermission() {
        String testName = "批量操作权限控制";
        NamingService namingService = null;

        try {
            // 创建无权限的客户端
            namingService = NacosClientUtil.createNamingService(
                TestConfig.UNAUTHORIZED_NAMESPACE, TestConfig.CF_USER_TOKEN);

            // 尝试批量注册多个服务实例
            int successCount = 0;
            int totalCount = 3;

            for (int i = 0; i < totalCount; i++) {
                String serviceName = TestConfig.getTestServiceName("batch-" + i);
                int port = TestConfig.getTestPort(10 + i);

                try {
                    boolean result = NacosClientUtil.registerInstance(
                        namingService, serviceName, TestConfig.TEST_IP, port,
                        TestConfig.TEST_GROUP, TestConfig.TEST_CLUSTER);

                    if (result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    // 权限被拒绝是预期的
                    logger.debug("批量操作中的单个操作被拒绝: {}", e.getMessage());
                }
            }

            if (successCount == 0) {
                return new TestResult(testName, true,
                    "批量操作权限控制正常，所有无权限操作都被拒绝", null);
            } else {
                return new TestResult(testName, false,
                    String.format("权限绕过！%d/%d 个无权限操作成功执行", successCount, totalCount), null);
            }

        } catch (Exception e) {
            return new TestResult(testName, false,
                "测试执行异常: " + e.getMessage(), e);
        } finally {
            NacosClientUtil.shutdown(namingService);
        }
    }
}
