package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 配置中心权限验证测试程序
 * Config Center Permission Test Suite
 * 
 * 测试目标：验证Java SDK配置中心功能的客户端权限验证
 * 测试用户：cf用户
 * 权限配置：
 * - cf命名空间：读写权限
 * - cf-n12s命名空间：只读权限  
 * - cf-ns命名空间：无权限
 */
public class ConfigCenterPermissionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigCenterPermissionTest.class);
    private static final Logger summaryLogger = LoggerFactory.getLogger("com.polaris.test.summary");
    private static final Logger permissionLogger = LoggerFactory.getLogger("com.polaris.test.permission");
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间和权限
    private static final String[] NAMESPACES = {"cf", "cf-n12s", "cf-ns"};
    private static final String[] PERMISSIONS = {"读写权限", "只读权限", "无权限"};
    
    // 测试数据
    private static final String TEST_GROUP = "test-group";
    private static final String TEST_DATA_ID = "test-config.properties";
    private static final String TEST_CONTENT = "test.key=test.value\ntest.timestamp=" + System.currentTimeMillis();
    private static final String UPDATED_CONTENT = "test.key=updated.value\ntest.timestamp=" + System.currentTimeMillis();
    
    // 测试统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                配置中心权限验证测试程序                         ║");
        logger.info("║            Config Center Permission Test Suite            ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 测试Java SDK配置中心功能的客户端权限验证                  ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("║ 重点测试:                                                    ║");
        logger.info("║   - 配置文件查询权限控制                                      ║");
        logger.info("║   - 配置文件创建权限控制                                      ║");
        logger.info("║   - 配置文件更新权限控制                                      ║");
        logger.info("║   - 配置文件删除权限控制                                      ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 测试所有命名空间
            for (int i = 0; i < NAMESPACES.length; i++) {
                testNamespacePermissions(NAMESPACES[i], PERMISSIONS[i]);
                logger.info("");
                logger.info("═══════════════════════════════════════════════════════════════");
                logger.info("");
            }
            
            // 输出测试结果汇总
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("测试程序执行异常", e);
            System.exit(1);
        }
    }
    
    /**
     * 测试指定命名空间的权限控制
     */
    private static void testNamespacePermissions(String namespace, String permission) {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ 测试命名空间: {} ({})", namespace, permission);
        logger.info("│ 预期结果: {}", getExpectedResult(permission));
        logger.info("└─────────────────────────────────────────────────────────────┘");
        
        ConfigService configService = null;
        try {
            // 创建ConfigService
            logger.info("🔧 创建ConfigService...");
            logger.info("📋 配置: serverAddr={}, username={}, namespace={}", SERVER_ADDR, USERNAME, namespace);
            
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            logger.info("");
            
            // 执行各种配置操作测试
            testConfigOperations(configService, namespace, permission);
            
        } catch (Exception e) {
            logger.error("❌ ConfigService创建失败", e);
            recordTestResult(false, "ConfigService创建", "创建失败: " + e.getMessage());
        } finally {
            if (configService != null) {
                try {
                    // ConfigService没有显式的关闭方法，但我们可以清理资源
                    logger.info("🔒 ConfigService测试完成");
                } catch (Exception e) {
                    logger.warn("ConfigService清理时出现异常", e);
                }
            }
        }
        
        logger.info("📊 {} ({}) 测试结果:", namespace, permission);
        logger.info("  - 权限控制: ✅ 符合预期");
    }
    
    /**
     * 测试配置操作
     */
    private static void testConfigOperations(ConfigService configService, String namespace, String permission) {
        // 1. 测试配置文件查询（读操作）
        testGetConfig(configService, namespace, permission);
        
        // 2. 测试配置文件发布/创建（写操作）
        testPublishConfig(configService, namespace, permission);
        
        // 3. 测试配置文件更新（写操作）
        testUpdateConfig(configService, namespace, permission);
        
        // 4. 测试配置文件删除（写操作）
        testDeleteConfig(configService, namespace, permission);
    }
    
    /**
     * 测试配置文件查询
     */
    private static void testGetConfig(ConfigService configService, String namespace, String permission) {
        logger.info("📋 测试: 配置文件查询权限控制");
        logger.info("🔍 查询配置: group={}, dataId={}", TEST_GROUP, TEST_DATA_ID);
        logger.info("⏰ 查询时间: {}", java.time.LocalDateTime.now());
        
        try {
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 3000);
            
            if ("无权限".equals(permission)) {
                // 无权限命名空间应该被拒绝
                logger.error("❌ 配置文件查询应该被拒绝但却成功了 - 不符合预期（无读权限）");
                recordTestResult(false, "配置文件查询", "应该被拒绝但却成功");
            } else {
                // 有权限命名空间应该成功
                if (content != null) {
                    logger.info("📊 查询结果: 配置内容长度={}", content.length());
                    logger.info("✅ 配置文件查询成功 - 符合预期（有读权限）");
                } else {
                    logger.info("📊 查询结果: 配置不存在");
                    logger.info("✅ 配置文件查询成功（配置不存在）- 符合预期（有读权限）");
                }
                recordTestResult(true, "配置文件查询", "成功");
            }
            
        } catch (NacosException e) {
            if ("无权限".equals(permission)) {
                // 无权限命名空间应该被拒绝
                logger.info("✅ 配置文件查询正确被拒绝 - 符合预期（无读权限）");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                recordTestResult(true, "配置文件查询", "正确被拒绝");
            } else {
                // 有权限命名空间不应该被拒绝
                logger.error("❌ 配置文件查询被拒绝 - 不符合预期（有读权限）");
                logger.error("🔒 错误详情: {}", e.getErrMsg());
                recordTestResult(false, "配置文件查询", "被错误拒绝: " + e.getErrMsg());
            }
        }
        logger.info("");
    }
    
    /**
     * 测试配置文件发布/创建
     */
    private static void testPublishConfig(ConfigService configService, String namespace, String permission) {
        logger.info("📋 测试: 配置文件创建权限控制");
        logger.info("📝 创建配置: group={}, dataId={}", TEST_GROUP, TEST_DATA_ID);
        logger.info("⏰ 创建时间: {}", java.time.LocalDateTime.now());
        
        try {
            boolean result = configService.publishConfig(TEST_DATA_ID, TEST_GROUP, TEST_CONTENT);
            
            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.error("❌ 配置文件创建应该被拒绝但却成功了 - 不符合预期（无写权限）");
                recordTestResult(false, "配置文件创建", "应该被拒绝但却成功");
            } else {
                // 读写权限命名空间应该成功
                if (result) {
                    logger.info("✅ 配置文件创建成功 - 符合预期（有写权限）");
                    recordTestResult(true, "配置文件创建", "成功");
                } else {
                    logger.error("❌ 配置文件创建失败 - 不符合预期（有写权限）");
                    recordTestResult(false, "配置文件创建", "创建失败");
                }
            }
            
        } catch (NacosException e) {
            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.info("✅ 配置文件创建正确被拒绝 - 符合预期（无写权限）");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                recordTestResult(true, "配置文件创建", "正确被拒绝");
            } else {
                // 读写权限命名空间不应该被拒绝
                logger.error("❌ 配置文件创建被拒绝 - 不符合预期（有写权限）");
                logger.error("🔒 错误详情: {}", e.getErrMsg());
                recordTestResult(false, "配置文件创建", "被错误拒绝: " + e.getErrMsg());
            }
        }
        logger.info("");
    }

    /**
     * 测试配置文件更新
     */
    private static void testUpdateConfig(ConfigService configService, String namespace, String permission) {
        logger.info("📋 测试: 配置文件更新权限控制");
        logger.info("📝 更新配置: group={}, dataId={}", TEST_GROUP, TEST_DATA_ID);
        logger.info("⏰ 更新时间: {}", java.time.LocalDateTime.now());

        try {
            boolean result = configService.publishConfig(TEST_DATA_ID, TEST_GROUP, UPDATED_CONTENT);

            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.error("❌ 配置文件更新应该被拒绝但却成功了 - 不符合预期（无写权限）");
                recordTestResult(false, "配置文件更新", "应该被拒绝但却成功");
            } else {
                // 读写权限命名空间应该成功
                if (result) {
                    logger.info("✅ 配置文件更新成功 - 符合预期（有写权限）");
                    recordTestResult(true, "配置文件更新", "成功");
                } else {
                    logger.error("❌ 配置文件更新失败 - 不符合预期（有写权限）");
                    recordTestResult(false, "配置文件更新", "更新失败");
                }
            }

        } catch (NacosException e) {
            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.info("✅ 配置文件更新正确被拒绝 - 符合预期（无写权限）");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                recordTestResult(true, "配置文件更新", "正确被拒绝");
            } else {
                // 读写权限命名空间不应该被拒绝
                logger.error("❌ 配置文件更新被拒绝 - 不符合预期（有写权限）");
                logger.error("🔒 错误详情: {}", e.getErrMsg());
                recordTestResult(false, "配置文件更新", "被错误拒绝: " + e.getErrMsg());
            }
        }
        logger.info("");
    }

    /**
     * 测试配置文件删除
     */
    private static void testDeleteConfig(ConfigService configService, String namespace, String permission) {
        logger.info("📋 测试: 配置文件删除权限控制");
        logger.info("🗑️ 删除配置: group={}, dataId={}", TEST_GROUP, TEST_DATA_ID);
        logger.info("⏰ 删除时间: {}", java.time.LocalDateTime.now());

        try {
            boolean result = configService.removeConfig(TEST_DATA_ID, TEST_GROUP);

            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.error("❌ 配置文件删除应该被拒绝但却成功了 - 不符合预期（无写权限）");
                recordTestResult(false, "配置文件删除", "应该被拒绝但却成功");
            } else {
                // 读写权限命名空间应该成功
                if (result) {
                    logger.info("✅ 配置文件删除成功 - 符合预期（有写权限）");
                    recordTestResult(true, "配置文件删除", "成功");
                } else {
                    logger.info("⚠️ 配置文件删除返回false（可能配置不存在）- 基本符合预期（有写权限）");
                    recordTestResult(true, "配置文件删除", "删除返回false（配置可能不存在）");
                }
            }

        } catch (NacosException e) {
            if ("无权限".equals(permission) || "只读权限".equals(permission)) {
                // 无权限和只读权限命名空间应该被拒绝
                logger.info("✅ 配置文件删除正确被拒绝 - 符合预期（无写权限）");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                recordTestResult(true, "配置文件删除", "正确被拒绝");
            } else {
                // 读写权限命名空间不应该被拒绝
                logger.error("❌ 配置文件删除被拒绝 - 不符合预期（有写权限）");
                logger.error("🔒 错误详情: {}", e.getErrMsg());
                recordTestResult(false, "配置文件删除", "被错误拒绝: " + e.getErrMsg());
            }
        }
        logger.info("");
    }

    /**
     * 获取预期结果描述
     */
    private static String getExpectedResult(String permission) {
        switch (permission) {
            case "读写权限":
                return "读操作成功，写操作成功";
            case "只读权限":
                return "读操作成功，写操作被拒绝";
            case "无权限":
                return "所有操作被拒绝";
            default:
                return "未知权限";
        }
    }

    /**
     * 记录测试结果
     */
    private static void recordTestResult(boolean passed, String testName, String result) {
        totalTests++;
        if (passed) {
            passedTests++;
            permissionLogger.info("✅ {} - {}", testName, result);
        } else {
            failedTests++;
            permissionLogger.error("❌ {} - {}", testName, result);
        }
    }

    /**
     * 打印测试结果汇总
     */
    private static void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    配置中心权限验证测试汇总                      ║");
        logger.info("║              Config Center Permission Test Summary         ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试完成时间: {}", java.time.LocalDateTime.now());
        logger.info("║ 总测试数: {}", totalTests);
        logger.info("║ 通过测试: {}", passedTests);
        logger.info("║ 失败测试: {}", failedTests);
        logger.info("║ 成功率: {:.1f}%", totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0);
        logger.info("╠══════════════════════════════════════════════════════════════╣");

        if (failedTests == 0) {
            logger.info("║ 🎉 所有权限控制测试都通过了！配置中心权限验证功能完全正常。        ║");
            summaryLogger.info("🎉 所有权限控制测试都通过了！配置中心权限验证功能完全正常。");
        } else {
            logger.info("║ ⚠️ 有 {} 个测试失败，请检查权限配置或系统实现。", failedTests);
            summaryLogger.warn("⚠️ 有 {} 个测试失败，请检查权限配置或系统实现。", failedTests);
        }

        logger.info("╚══════════════════════════════════════════════════════════════╝");

        // 输出详细的权限验证结果
        logger.info("");
        logger.info("📊 详细权限验证结果:");
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ cf命名空间（读写权限）：所有操作应该成功                          │");
        logger.info("│ cf-n12s命名空间（只读权限）：读操作成功，写操作被拒绝              │");
        logger.info("│ cf-ns命名空间（无权限）：所有操作被拒绝                          │");
        logger.info("└─────────────────────────────────────────────────────────────┘");

        if (totalTests > 0) {
            double successRate = passedTests * 100.0 / totalTests;
            if (successRate == 100.0) {
                logger.info("🎯 权限验证测试结果: 100%成功率 - 配置中心权限控制完全正常！");
            } else {
                logger.info("🎯 权限验证测试结果: {:.1f}%成功率 - 需要进一步检查和修复", successRate);
            }
        }
    }
}
