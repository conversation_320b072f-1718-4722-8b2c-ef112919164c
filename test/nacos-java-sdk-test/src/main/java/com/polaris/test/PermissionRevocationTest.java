package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 权限取消后的访问控制验证测试
 * 
 * 测试目标：验证cf用户对cf-n12s命名空间权限取消后，访问控制是否生效
 * 
 * 测试场景：
 * 1. cf用户已被取消cf-n12s命名空间的所有权限（读权限和写权限）
 * 2. cf-n12s命名空间中存在5个已创建的测试配置
 * 3. 验证读取和写入操作都被正确拒绝
 */
public class PermissionRevocationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionRevocationTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String TARGET_NAMESPACE = "cf-n12s";
    
    // 已存在的测试配置
    private static final TestConfig[] EXISTING_CONFIGS = {
        new TestConfig("permission-test-database-config", "DEFAULT_GROUP"),
        new TestConfig("permission-test-redis-config", "CACHE_GROUP"),
        new TestConfig("permission-test-application-config", "APP_GROUP"),
        new TestConfig("permission-test-security-config", "SECURITY_GROUP"),
        new TestConfig("permission-test-monitoring-config", "MONITOR_GROUP")
    };
    
    // 新测试配置（用于测试写入权限）
    private static final TestConfig[] NEW_TEST_CONFIGS = {
        new TestConfig("revocation-test-config-1", "TEST_GROUP", 
            "# Permission Revocation Test Config 1\ntest.timestamp=" + System.currentTimeMillis()),
        new TestConfig("revocation-test-config-2", "DEFAULT_GROUP", 
            "# Permission Revocation Test Config 2\ntest.timestamp=" + System.currentTimeMillis()),
        new TestConfig("revocation-test-config-3", "SECURITY_GROUP", 
            "# Permission Revocation Test Config 3\ntest.timestamp=" + System.currentTimeMillis())
    };
    
    static class TestConfig {
        final String dataId;
        final String group;
        final String content;
        
        TestConfig(String dataId, String group) {
            this.dataId = dataId;
            this.group = group;
            this.content = null;
        }
        
        TestConfig(String dataId, String group, String content) {
            this.dataId = dataId;
            this.group = group;
            this.content = content;
        }
    }
    
    public static void main(String[] args) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String testStartTime = LocalDateTime.now().format(formatter);
        
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              权限取消后的访问控制验证测试                        ║");
        logger.info("║        Permission Revocation Access Control Test        ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证权限取消后访问控制是否生效                        ║");
        logger.info("║ 测试用户: cf                                                 ║");
        logger.info("║ 目标命名空间: cf-n12s (权限已被取消)                          ║");
        logger.info("║ 测试时间: {}", testStartTime);
        logger.info("║ 预期结果: 所有操作都应该被拒绝                                ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 阶段1：测试读取权限验证
            logger.info("🔍 阶段1：测试读取权限验证");
            testReadPermissions();
            
            // 阶段2：测试写入权限验证
            logger.info("🔍 阶段2：测试写入权限验证");
            testWritePermissions();
            
            // 阶段3：生成测试报告
            logger.info("🔍 阶段3：生成测试报告");
            generateTestReport(testStartTime);
            
        } catch (Exception e) {
            logger.error("权限取消验证测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 阶段1：测试读取权限验证
     */
    private static void testReadPermissions() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段1：测试读取权限验证");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("📋 测试目标：验证cf用户无法读取cf-n12s命名空间中的已存在配置");
        logger.info("🎯 预期结果：所有读取操作都应该失败，返回权限拒绝错误");
        logger.info("");
        
        ConfigService configService = null;
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        
        try {
            // 创建ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            logger.info("");
            
            // 测试读取每个已存在的配置
            for (int i = 0; i < EXISTING_CONFIGS.length; i++) {
                TestConfig config = EXISTING_CONFIGS[i];
                totalTests++;
                
                logger.info("📖 测试读取配置 {}/{}: {}", i + 1, EXISTING_CONFIGS.length, config.dataId);
                logger.info("  - 分组: {}", config.group);
                logger.info("  - 命名空间: {}", TARGET_NAMESPACE);
                logger.info("  - 预期结果: 权限拒绝");
                
                try {
                    long startTime = System.currentTimeMillis();
                    String content = configService.getConfig(config.dataId, config.group, 5000);
                    long endTime = System.currentTimeMillis();
                    
                    if (content != null) {
                        logger.error("  ❌ 权限绕过问题：读取成功，但应该被拒绝！");
                        logger.error("    - 响应时间: {}ms", endTime - startTime);
                        logger.error("    - 内容长度: {} 字符", content.length());
                        logger.error("    - 内容预览: {}", getContentPreview(content));
                        logger.error("    🚨 这是一个严重的权限绕过漏洞！");
                        failedTests++;
                    } else {
                        logger.info("  ✅ 权限验证通过：配置不存在或读取失败（符合预期）");
                        logger.info("    - 响应时间: {}ms", endTime - startTime);
                        passedTests++;
                    }
                    
                } catch (NacosException e) {
                    logger.info("  ✅ 权限验证通过：读取被拒绝（符合预期）");
                    logger.info("    - 错误代码: {}", e.getErrCode());
                    logger.info("    - 错误信息: {}", e.getErrMsg());
                    passedTests++;
                }
                
                logger.info("");
            }
            
        } catch (Exception e) {
            logger.error("❌ 读取权限测试过程中出现异常: {}", e.getMessage());
            failedTests = totalTests - passedTests;
        } finally {
            if (configService != null) {
                configService = null;
            }
        }
        
        // 输出阶段1测试结果
        logger.info("📊 阶段1测试结果汇总:");
        logger.info("  - 总测试数: {}", totalTests);
        logger.info("  - 通过测试: {}", passedTests);
        logger.info("  - 失败测试: {}", failedTests);
        logger.info("  - 成功率: {:.1f}%", totalTests > 0 ? (double) passedTests / totalTests * 100 : 0);
        logger.info("");
        
        if (failedTests > 0) {
            logger.error("🚨 发现权限绕过问题！需要立即修复！");
        } else {
            logger.info("✅ 读取权限验证完全正确！");
        }
        
        logger.info("");
    }
    
    /**
     * 阶段2：测试写入权限验证
     */
    private static void testWritePermissions() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段2：测试写入权限验证");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("📋 测试目标：验证cf用户无法在cf-n12s命名空间中创建新配置");
        logger.info("🎯 预期结果：所有创建操作都应该失败，返回权限拒绝错误");
        logger.info("");
        
        ConfigService configService = null;
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        
        try {
            // 创建ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            logger.info("");
            
            // 测试创建每个新配置
            for (int i = 0; i < NEW_TEST_CONFIGS.length; i++) {
                TestConfig config = NEW_TEST_CONFIGS[i];
                totalTests++;
                
                logger.info("📝 测试创建配置 {}/{}: {}", i + 1, NEW_TEST_CONFIGS.length, config.dataId);
                logger.info("  - 分组: {}", config.group);
                logger.info("  - 命名空间: {}", TARGET_NAMESPACE);
                logger.info("  - 内容长度: {} 字符", config.content.length());
                logger.info("  - 预期结果: 权限拒绝");
                
                try {
                    long startTime = System.currentTimeMillis();
                    boolean result = configService.publishConfig(config.dataId, config.group, config.content);
                    long endTime = System.currentTimeMillis();
                    
                    if (result) {
                        logger.error("  ❌ 权限绕过问题：创建成功，但应该被拒绝！");
                        logger.error("    - 响应时间: {}ms", endTime - startTime);
                        logger.error("    🚨 这是一个严重的权限绕过漏洞！");
                        
                        // 尝试清理意外创建的配置
                        try {
                            configService.removeConfig(config.dataId, config.group);
                            logger.info("    🧹 已清理意外创建的配置");
                        } catch (Exception cleanupException) {
                            logger.warn("    ⚠️ 清理配置时出现异常: {}", cleanupException.getMessage());
                        }
                        
                        failedTests++;
                    } else {
                        logger.info("  ✅ 权限验证通过：创建失败（符合预期）");
                        logger.info("    - 响应时间: {}ms", endTime - startTime);
                        passedTests++;
                    }
                    
                } catch (NacosException e) {
                    logger.info("  ✅ 权限验证通过：创建被拒绝（符合预期）");
                    logger.info("    - 错误代码: {}", e.getErrCode());
                    logger.info("    - 错误信息: {}", e.getErrMsg());
                    passedTests++;
                }
                
                logger.info("");
            }
            
        } catch (Exception e) {
            logger.error("❌ 写入权限测试过程中出现异常: {}", e.getMessage());
            failedTests = totalTests - passedTests;
        } finally {
            if (configService != null) {
                configService = null;
            }
        }
        
        // 输出阶段2测试结果
        logger.info("📊 阶段2测试结果汇总:");
        logger.info("  - 总测试数: {}", totalTests);
        logger.info("  - 通过测试: {}", passedTests);
        logger.info("  - 失败测试: {}", failedTests);
        logger.info("  - 成功率: {:.1f}%", totalTests > 0 ? (double) passedTests / totalTests * 100 : 0);
        logger.info("");
        
        if (failedTests > 0) {
            logger.error("🚨 发现权限绕过问题！需要立即修复！");
        } else {
            logger.info("✅ 写入权限验证完全正确！");
        }
        
        logger.info("");
    }
    
    /**
     * 阶段3：生成测试报告
     */
    private static void generateTestReport(String testStartTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String testEndTime = LocalDateTime.now().format(formatter);
        
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("📋 权限取消验证测试报告");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🕐 测试开始时间: {}", testStartTime);
        logger.info("🕐 测试结束时间: {}", testEndTime);
        logger.info("👤 测试用户: {}", USERNAME);
        logger.info("🏷️ 目标命名空间: {} (权限已取消)", TARGET_NAMESPACE);
        logger.info("");
        
        logger.info("📊 测试覆盖范围:");
        logger.info("  - 读取权限验证: {} 个已存在配置", EXISTING_CONFIGS.length);
        logger.info("  - 写入权限验证: {} 个新配置创建", NEW_TEST_CONFIGS.length);
        logger.info("  - 总测试用例: {} 个", EXISTING_CONFIGS.length + NEW_TEST_CONFIGS.length);
        logger.info("");
        
        logger.info("🎯 测试结论:");
        logger.info("  - 如果所有测试都通过，说明权限取消生效，访问控制正常");
        logger.info("  - 如果有测试失败，说明存在权限绕过问题，需要立即修复");
        logger.info("");
        
        logger.info("📋 建议后续操作:");
        logger.info("  1. 查看服务端日志，确认权限验证逻辑执行情况");
        logger.info("  2. 如发现权限绕过问题，分析具体原因并制定修复方案");
        logger.info("  3. 验证其他命名空间和用户的权限控制是否正常");
        logger.info("");
    }
    
    /**
     * 获取内容预览
     */
    private static String getContentPreview(String content) {
        if (content == null) {
            return "null";
        }
        if (content.length() <= 100) {
            return content.replace("\n", "\\n");
        }
        return content.substring(0, 100).replace("\n", "\\n") + "...";
    }
}
