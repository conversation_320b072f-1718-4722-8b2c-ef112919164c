package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 测试读取新创建的配置文件
 * 
 * 目标：验证客户端缓存权限绕过问题
 * 
 * 测试逻辑：
 * - 如果是客户端缓存问题，新创建的配置应该无法读取（因为从未缓存过）
 * - 如果不是缓存问题，新配置也能被读取，说明存在其他权限绕过问题
 */
public class NewConfigReadTest {
    
    private static final Logger logger = LoggerFactory.getLogger(NewConfigReadTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String TARGET_NAMESPACE = "cf-n12s";
    
    // 新创建的测试配置
    private static final String NEW_CONFIG_DATA_ID = "test-read";
    private static final String NEW_CONFIG_GROUP = "DEFAULT_GROUP";
    
    public static void main(String[] args) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String testTime = LocalDateTime.now().format(formatter);
        
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                新配置读取测试                                  ║");
        logger.info("║            New Config Read Test                          ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证客户端缓存权限绕过问题                            ║");
        logger.info("║ 测试用户: cf (已被取消cf-n12s命名空间权限)                     ║");
        logger.info("║ 目标配置: test-read (DEFAULT_GROUP)                         ║");
        logger.info("║ 测试时间: {}", testTime);
        logger.info("║ 测试逻辑:                                                    ║");
        logger.info("║   - 如果是缓存问题：新配置应该无法读取                          ║");
        logger.info("║   - 如果不是缓存问题：新配置也能读取                            ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            testNewConfigRead();
        } catch (Exception e) {
            logger.error("新配置读取测试失败", e);
            System.exit(1);
        }
    }
    
    private static void testNewConfigRead() {
        logger.info("🔍 开始新配置读取测试");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        ConfigService configService = null;
        
        try {
            // 创建ConfigService
            logger.info("🔧 正在连接到Nacos服务器...");
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            logger.info("📋 连接信息:");
            logger.info("  - 服务器地址: {}", SERVER_ADDR);
            logger.info("  - 用户名: {}", USERNAME);
            logger.info("  - 命名空间: {}", TARGET_NAMESPACE);
            logger.info("");
            
            // 测试读取新创建的配置
            logger.info("📖 尝试读取新创建的配置");
            logger.info("  - 配置ID: {}", NEW_CONFIG_DATA_ID);
            logger.info("  - 分组: {}", NEW_CONFIG_GROUP);
            logger.info("  - 命名空间: {}", TARGET_NAMESPACE);
            logger.info("  - 预期结果: 权限拒绝（如果是缓存问题）");
            logger.info("");
            
            long startTime = System.currentTimeMillis();
            String content = null;
            NacosException exception = null;
            
            try {
                content = configService.getConfig(NEW_CONFIG_DATA_ID, NEW_CONFIG_GROUP, 5000);
            } catch (NacosException e) {
                exception = e;
            }
            
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            // 分析结果
            logger.info("📊 测试结果分析:");
            logger.info("  - 响应时间: {}ms", responseTime);
            
            if (exception != null) {
                logger.info("  - 操作结果: ❌ 读取失败");
                logger.info("  - 错误代码: {}", exception.getErrCode());
                logger.info("  - 错误信息: {}", exception.getErrMsg());
                logger.info("");
                
                logger.info("✅ 权限验证正确：新配置读取被拒绝");
                logger.info("🎯 结论: 这证实了客户端缓存权限绕过问题的假设");
                logger.info("  - 服务端权限验证正常工作");
                logger.info("  - 之前能读取配置是因为客户端缓存");
                logger.info("  - 新配置没有缓存，所以被正确拒绝");
                
            } else if (content != null) {
                logger.error("  - 操作结果: ✅ 读取成功");
                logger.error("  - 内容长度: {} 字符", content.length());
                logger.error("  - 配置内容:");
                logger.error("═══════════════════════════════════════════════════════════════");
                logger.error("{}", content);
                logger.error("═══════════════════════════════════════════════════════════════");
                logger.error("");
                
                logger.error("❌ 严重权限绕过问题：新配置也能被读取！");
                logger.error("🚨 这不是简单的客户端缓存问题！");
                logger.error("🚨 存在更严重的权限验证漏洞！");
                logger.error("  - 服务端权限验证可能存在问题");
                logger.error("  - 或者存在其他权限绕过路径");
                
            } else {
                logger.info("  - 操作结果: ❌ 配置不存在");
                logger.info("");
                
                logger.info("⚠️ 配置可能不存在或创建失败");
                logger.info("📋 请确认:");
                logger.info("  1. 配置是否已在控制台成功创建");
                logger.info("  2. 配置名称和分组是否正确");
                logger.info("  3. 命名空间是否正确");
            }
            
        } catch (Exception e) {
            logger.error("❌ 测试过程中出现异常: {}", e.getMessage(), e);
        } finally {
            if (configService != null) {
                configService = null;
            }
        }
        
        logger.info("");
        logger.info("🎯 测试完成");
        
        // 提供后续建议
        logger.info("");
        logger.info("📋 后续建议:");
        logger.info("1. 如果新配置读取被拒绝：");
        logger.info("   - 确认是客户端缓存问题");
        logger.info("   - 实施缓存清理或重启方案");
        logger.info("2. 如果新配置也能读取：");
        logger.info("   - 深入检查权限验证逻辑");
        logger.info("   - 可能存在更严重的权限漏洞");
        logger.info("3. 如果配置不存在：");
        logger.info("   - 检查配置创建是否成功");
        logger.info("   - 重新创建配置后再次测试");
    }
}
