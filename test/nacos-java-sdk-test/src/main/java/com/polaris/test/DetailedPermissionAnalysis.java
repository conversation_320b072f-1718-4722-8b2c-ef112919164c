package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 详细的权限分析测试
 * 专门分析配置写操作的权限验证问题
 */
public class DetailedPermissionAnalysis {
    
    private static final Logger logger = LoggerFactory.getLogger(DetailedPermissionAnalysis.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                详细的权限分析测试                              ║");
        logger.info("║           Detailed Permission Analysis Test              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分析配置写操作权限验证问题                               ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 测试cf-n12s命名空间（只读权限）
            logger.info("🔍 测试cf-n12s命名空间（只读权限）");
            testNamespaceDetailedPermission("cf-n12s", "只读权限");
            
            logger.info("");
            
            // 测试cf-ns命名空间（无权限）
            logger.info("🔍 测试cf-ns命名空间（无权限）");
            testNamespaceDetailedPermission("cf-ns", "无权限");
            
        } catch (Exception e) {
            logger.error("❌ 详细权限分析测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 详细测试指定命名空间的权限
     */
    private static void testNamespaceDetailedPermission(String namespace, String expectedPermission) {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 命名空间: {} (期望权限: {})", namespace, expectedPermission);
        logger.info("═══════════════════════════════════════════════════════════════");
        
        try {
            // 创建ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            
            // 详细测试配置创建
            testDetailedConfigCreate(configService, namespace, expectedPermission);
            
            // 详细测试配置更新
            testDetailedConfigUpdate(configService, namespace, expectedPermission);
            
            // 详细测试配置删除
            testDetailedConfigDelete(configService, namespace, expectedPermission);
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 测试失败: {}", namespace, e.getMessage());
        }
    }
    
    /**
     * 详细测试配置创建
     */
    private static void testDetailedConfigCreate(ConfigService configService, String namespace, String expectedPermission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 详细测试配置创建 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        String dataId = "detailed-test-create-" + System.currentTimeMillis();
        String content = "详细测试配置创建 - " + namespace + " - " + java.time.LocalDateTime.now();
        
        logger.info("🔍 测试参数:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: TEST_GROUP");
        logger.info("  - 内容长度: {} 字符", content.length());
        logger.info("  - 期望权限: {}", expectedPermission);
        
        try {
            logger.info("📤 发送配置创建请求...");
            long startTime = System.currentTimeMillis();
            
            boolean result = configService.publishConfig(dataId, "TEST_GROUP", content);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            logger.info("📊 请求结果:");
            logger.info("  - 响应时间: {}ms", duration);
            logger.info("  - 返回结果: {}", result);
            
            if (result) {
                logger.error("❌ 配置创建成功，但根据权限设置应该失败！");
                logger.error("  - 命名空间: {}", namespace);
                logger.error("  - 期望权限: {}", expectedPermission);
                logger.error("  - 实际结果: 创建成功");
                
                // 验证配置是否真的被创建
                logger.info("🔍 验证配置是否真的被创建...");
                try {
                    String readContent = configService.getConfig(dataId, "TEST_GROUP", 3000);
                    if (readContent != null) {
                        logger.error("❌ 配置确实被创建了！内容: {}", readContent.substring(0, Math.min(50, readContent.length())));
                    } else {
                        logger.info("ℹ️ 配置读取为空，可能创建失败");
                    }
                } catch (Exception e) {
                    logger.info("ℹ️ 配置读取异常: {}", e.getMessage());
                }
                
            } else {
                logger.info("✅ 配置创建失败，符合权限预期");
            }
            
        } catch (NacosException e) {
            logger.info("📊 捕获到异常:");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            if (e.getErrCode() == 401001) {
                logger.info("✅ 正确返回权限拒绝错误 (401001)");
            } else {
                logger.warn("⚠️ 错误代码不是预期的401001: {}", e.getErrCode());
            }
        } catch (Exception e) {
            logger.error("❌ 其他异常: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    /**
     * 详细测试配置更新
     */
    private static void testDetailedConfigUpdate(ConfigService configService, String namespace, String expectedPermission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 详细测试配置更新 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        String dataId = "test-config-01";
        String newContent = "详细测试配置更新 - " + namespace + " - " + java.time.LocalDateTime.now();
        
        logger.info("🔍 测试参数:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: DEFAULT_GROUP");
        logger.info("  - 新内容长度: {} 字符", newContent.length());
        logger.info("  - 期望权限: {}", expectedPermission);
        
        try {
            // 先读取原始内容
            logger.info("📖 读取原始配置内容...");
            String originalContent = null;
            try {
                originalContent = configService.getConfig(dataId, "DEFAULT_GROUP", 3000);
                if (originalContent != null) {
                    logger.info("  - 原始内容长度: {} 字符", originalContent.length());
                    logger.info("  - 原始内容预览: {}", originalContent.substring(0, Math.min(50, originalContent.length())));
                } else {
                    logger.info("  - 原始配置不存在或为空");
                }
            } catch (Exception e) {
                logger.info("  - 读取原始配置失败: {}", e.getMessage());
            }
            
            logger.info("📤 发送配置更新请求...");
            long startTime = System.currentTimeMillis();
            
            boolean result = configService.publishConfig(dataId, "DEFAULT_GROUP", newContent);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            logger.info("📊 请求结果:");
            logger.info("  - 响应时间: {}ms", duration);
            logger.info("  - 返回结果: {}", result);
            
            if (result) {
                logger.error("❌ 配置更新成功，但根据权限设置应该失败！");
                
                // 验证配置是否真的被更新
                logger.info("🔍 验证配置是否真的被更新...");
                try {
                    String updatedContent = configService.getConfig(dataId, "DEFAULT_GROUP", 3000);
                    if (updatedContent != null && updatedContent.equals(newContent)) {
                        logger.error("❌ 配置确实被更新了！");
                    } else if (updatedContent != null && updatedContent.equals(originalContent)) {
                        logger.info("ℹ️ 配置内容未变化，可能更新失败");
                    } else {
                        logger.info("ℹ️ 配置内容状态不明确");
                    }
                } catch (Exception e) {
                    logger.info("ℹ️ 配置读取异常: {}", e.getMessage());
                }
                
            } else {
                logger.info("✅ 配置更新失败，符合权限预期");
            }
            
        } catch (NacosException e) {
            logger.info("📊 捕获到异常:");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            if (e.getErrCode() == 401001) {
                logger.info("✅ 正确返回权限拒绝错误 (401001)");
            } else {
                logger.warn("⚠️ 错误代码不是预期的401001: {}", e.getErrCode());
            }
        } catch (Exception e) {
            logger.error("❌ 其他异常: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    /**
     * 详细测试配置删除
     */
    private static void testDetailedConfigDelete(ConfigService configService, String namespace, String expectedPermission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 详细测试配置删除 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        String dataId = "test-config-09"; // 选择一个存在的配置
        String group = "MIDDLEWARE_GROUP";
        
        logger.info("🔍 测试参数:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: {}", group);
        logger.info("  - 期望权限: {}", expectedPermission);
        
        try {
            // 先检查配置是否存在
            logger.info("📖 检查配置是否存在...");
            String existingContent = null;
            try {
                existingContent = configService.getConfig(dataId, group, 3000);
                if (existingContent != null) {
                    logger.info("  - 配置存在，内容长度: {} 字符", existingContent.length());
                } else {
                    logger.info("  - 配置不存在或为空");
                }
            } catch (Exception e) {
                logger.info("  - 检查配置存在性失败: {}", e.getMessage());
            }
            
            logger.info("📤 发送配置删除请求...");
            long startTime = System.currentTimeMillis();
            
            boolean result = configService.removeConfig(dataId, group);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            logger.info("📊 请求结果:");
            logger.info("  - 响应时间: {}ms", duration);
            logger.info("  - 返回结果: {}", result);
            
            if (result) {
                logger.error("❌ 配置删除成功，但根据权限设置应该失败！");
                
                // 验证配置是否真的被删除
                logger.info("🔍 验证配置是否真的被删除...");
                try {
                    String deletedContent = configService.getConfig(dataId, group, 3000);
                    if (deletedContent == null) {
                        logger.error("❌ 配置确实被删除了！");
                    } else {
                        logger.info("ℹ️ 配置仍然存在，可能删除失败");
                    }
                } catch (Exception e) {
                    logger.info("ℹ️ 配置读取异常: {}", e.getMessage());
                }
                
            } else {
                logger.info("✅ 配置删除失败，符合权限预期");
            }
            
        } catch (NacosException e) {
            logger.info("📊 捕获到异常:");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            if (e.getErrCode() == 401001) {
                logger.info("✅ 正确返回权限拒绝错误 (401001)");
            } else {
                logger.warn("⚠️ 错误代码不是预期的401001: {}", e.getErrCode());
            }
        } catch (Exception e) {
            logger.error("❌ 其他异常: {}", e.getMessage());
        }
        
        logger.info("");
    }
}
