package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Comparator;
import java.util.Properties;

/**
 * 缓存清理测试
 * 验证手动清理缓存后的权限验证效果
 */
public class CacheClearTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheClearTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String TARGET_NAMESPACE = "cf-n12s";
    
    // 测试配置
    private static final String TEST_DATA_ID = "test-read";
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    
    // 缓存目录
    private static final String CACHE_BASE_DIR = System.getProperty("user.home") + "/nacos/config";
    private static final String NAMESPACE_CACHE_DIR = CACHE_BASE_DIR + "/fixed-cf-n12s-180.76.109.137_8848_nacos";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    缓存清理测试                                ║");
        logger.info("║                Cache Clear Test                          ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 验证手动清理缓存后的权限验证效果                         ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 步骤1：清理前测试（应该能读取到缓存）
            logger.info("🔍 步骤1：清理前测试（验证缓存存在）");
            testBeforeCacheClear();
            
            // 步骤2：清理缓存
            logger.info("🔍 步骤2：清理缓存");
            clearCache();
            
            // 步骤3：清理后测试（应该无法读取，如果权限确实被取消）
            logger.info("🔍 步骤3：清理后测试（验证权限验证生效）");
            testAfterCacheClear();
            
        } catch (Exception e) {
            logger.error("缓存清理测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 步骤1：清理前测试
     */
    private static void testBeforeCacheClear() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 步骤1：清理前测试（验证缓存存在）");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        // 检查缓存文件是否存在
        Path cacheFile = Paths.get(NAMESPACE_CACHE_DIR + "/snapshot-tenant/" + TARGET_NAMESPACE + "/" + TEST_GROUP + "/" + TEST_DATA_ID);
        logger.info("🔍 检查缓存文件: {}", cacheFile.toAbsolutePath());
        
        if (Files.exists(cacheFile)) {
            logger.info("✅ 缓存文件存在");
            try {
                String cachedContent = Files.readString(cacheFile);
                logger.info("  - 缓存内容: {}", cachedContent);
                logger.info("  - 文件大小: {} bytes", Files.size(cacheFile));
            } catch (IOException e) {
                logger.warn("  - 无法读取缓存文件: {}", e.getMessage());
            }
        } else {
            logger.warn("⚠️ 缓存文件不存在");
        }
        
        // 测试读取配置
        logger.info("");
        logger.info("📖 测试读取配置（应该成功，可能来自缓存）");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            
            long startTime = System.currentTimeMillis();
            String content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            long endTime = System.currentTimeMillis();
            
            if (content != null) {
                logger.info("✅ 读取成功");
                logger.info("  - 内容: {}", content);
                logger.info("  - 响应时间: {}ms", endTime - startTime);
                logger.info("  - 可能来源: 服务端或本地缓存");
            } else {
                logger.info("❌ 读取失败或配置不存在");
            }
            
        } catch (NacosException e) {
            logger.info("❌ 读取失败");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
        }
        
        logger.info("");
    }
    
    /**
     * 步骤2：清理缓存
     */
    private static void clearCache() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 步骤2：清理缓存");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        Path cacheDir = Paths.get(NAMESPACE_CACHE_DIR);
        logger.info("🗑️ 清理缓存目录: {}", cacheDir.toAbsolutePath());
        
        if (Files.exists(cacheDir)) {
            try {
                // 递归删除缓存目录
                Files.walk(cacheDir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            logger.debug("删除: {}", path);
                        } catch (IOException e) {
                            logger.warn("无法删除: {} - {}", path, e.getMessage());
                        }
                    });
                
                logger.info("✅ 缓存目录清理完成");
                
                // 验证清理结果
                if (!Files.exists(cacheDir)) {
                    logger.info("✅ 缓存目录已完全删除");
                } else {
                    logger.warn("⚠️ 缓存目录仍然存在，可能清理不完全");
                }
                
            } catch (IOException e) {
                logger.error("❌ 清理缓存失败: {}", e.getMessage());
            }
        } else {
            logger.info("ℹ️ 缓存目录不存在，无需清理");
        }
        
        logger.info("");
    }
    
    /**
     * 步骤3：清理后测试
     */
    private static void testAfterCacheClear() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 步骤3：清理后测试（验证权限验证生效）");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        logger.info("📖 测试读取配置（缓存已清理，应该直接访问服务端）");
        logger.info("🎯 如果cf用户权限已被取消，此次读取应该失败");
        logger.info("");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", TARGET_NAMESPACE);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            
            long startTime = System.currentTimeMillis();
            String content = null;
            NacosException exception = null;
            
            try {
                content = configService.getConfig(TEST_DATA_ID, TEST_GROUP, 5000);
            } catch (NacosException e) {
                exception = e;
            }
            
            long endTime = System.currentTimeMillis();
            
            logger.info("📊 测试结果:");
            logger.info("  - 响应时间: {}ms", endTime - startTime);
            
            if (exception != null) {
                logger.info("  - 操作结果: ❌ 读取失败");
                logger.info("  - 错误代码: {}", exception.getErrCode());
                logger.info("  - 错误信息: {}", exception.getErrMsg());
                logger.info("");
                
                if (exception.getErrCode() == 401001) {
                    logger.info("✅ 权限验证正确：权限被拒绝（401001）");
                    logger.info("🎯 结论: 缓存清理成功，权限验证正常工作");
                } else {
                    logger.warn("⚠️ 其他错误，可能不是权限问题");
                }
                
            } else if (content != null) {
                logger.info("  - 操作结果: ✅ 读取成功");
                logger.info("  - 内容: {}", content);
                logger.info("");
                
                logger.info("ℹ️ 读取成功，可能的原因:");
                logger.info("  1. cf用户权限已恢复");
                logger.info("  2. 配置来自服务端（非缓存）");
                logger.info("  3. 存在其他缓存机制");
                
            } else {
                logger.info("  - 操作结果: ❌ 配置不存在");
                logger.info("");
                
                logger.info("ℹ️ 配置不存在，可能的原因:");
                logger.info("  1. 配置已被删除");
                logger.info("  2. 权限被拒绝但返回null而非异常");
            }
            
        } catch (Exception e) {
            logger.error("❌ 测试过程中出现异常: {}", e.getMessage());
        }
        
        logger.info("");
        logger.info("🎯 缓存清理测试完成");
        
        // 提供后续建议
        logger.info("");
        logger.info("📋 后续建议:");
        logger.info("1. 如果清理后仍能读取配置：");
        logger.info("   - 确认用户权限是否真的被取消");
        logger.info("   - 检查是否存在其他缓存机制");
        logger.info("2. 如果清理后无法读取配置：");
        logger.info("   - 证明缓存清理方案有效");
        logger.info("   - 可以作为权限变更后的标准操作");
        logger.info("3. 生产环境建议：");
        logger.info("   - 权限变更后自动清理相关缓存");
        logger.info("   - 或实现权限变更通知机制");
    }
}
