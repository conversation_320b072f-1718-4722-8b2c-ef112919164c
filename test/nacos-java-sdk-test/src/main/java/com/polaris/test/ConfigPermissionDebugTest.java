package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * 配置权限调试测试
 * 
 * 专门用于调试cf-ns命名空间配置查询意外成功的问题
 */
public class ConfigPermissionDebugTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigPermissionDebugTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "**************:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间
    private static final String[] TEST_NAMESPACES = {"cf", "cf-n12s", "cf-ns"};
    private static final String[] NAMESPACE_DESCRIPTIONS = {
        "cf命名空间（读写权限）",
        "cf-n12s命名空间（只读权限）", 
        "cf-ns命名空间（无权限）"
    };
    
    // 权限预期结果：[读权限, 写权限]
    private static final boolean[][] EXPECTED_PERMISSIONS = {
        {true, true},   // cf: 读写权限
        {true, false},  // cf-n12s: 只读权限
        {false, false}  // cf-ns: 无权限
    };
    
    public static void main(String[] args) {
        ConfigPermissionDebugTest test = new ConfigPermissionDebugTest();
        test.runConfigPermissionDebugTest();
    }
    
    public void runConfigPermissionDebugTest() {
        printTestHeader();
        
        // 对每个命名空间进行详细的配置权限测试
        for (int i = 0; i < TEST_NAMESPACES.length; i++) {
            String namespace = TEST_NAMESPACES[i];
            String description = NAMESPACE_DESCRIPTIONS[i];
            boolean[] permissions = EXPECTED_PERMISSIONS[i];
            
            logger.info("┌─────────────────────────────────────────────────────────────┐");
            logger.info("│ 测试命名空间: {}", description);
            logger.info("│ 预期权限: 读权限={}, 写权限={}", permissions[0], permissions[1]);
            logger.info("└─────────────────────────────────────────────────────────────┘");
            
            testNamespaceConfigPermissions(namespace, permissions[0], permissions[1]);
            
            logger.info("");
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("");
        }
        
        printTestSummary();
    }
    
    /**
     * 测试单个命名空间的配置权限
     */
    private void testNamespaceConfigPermissions(String namespace, boolean expectedRead, boolean expectedWrite) {
        logger.info("🚀 开始配置权限详细测试");
        logger.info("⏰ 测试时间: {}", getCurrentTime());
        logger.info("🔧 命名空间: {}", namespace);
        logger.info("🎯 预期读权限: {}, 预期写权限: {}", expectedRead, expectedWrite);
        
        ConfigService configService = null;
        
        try {
            // 创建ConfigService
            configService = createConfigService(namespace);
            logger.info("✅ ConfigService创建成功");
            
            // 生成唯一的测试标识符
            long timestamp = System.currentTimeMillis();
            String testDataId = "debug-config-" + namespace + "-" + timestamp;
            String testGroup = "DEBUG_GROUP";
            
            logger.info("🔧 测试配置标识符:");
            logger.info("  - DataId: {}", testDataId);
            logger.info("  - Group: {}", testGroup);
            logger.info("");
            
            // 1. 测试配置发布（写权限）
            testConfigPublish(configService, namespace, testDataId, testGroup, expectedWrite);
            
            // 2. 测试配置查询（读权限）- 查询不存在的配置
            testConfigGetNonExistent(configService, namespace, testDataId, testGroup, expectedRead);
            
            // 3. 测试配置查询（读权限）- 查询已知存在的配置
            testConfigGetExisting(configService, namespace, expectedRead);
            
            // 4. 如果发布成功，再次测试查询已发布的配置
            if (expectedWrite) {
                logger.info("📋 测试查询刚发布的配置");
                testConfigGetPublished(configService, namespace, testDataId, testGroup, expectedRead);
                
                // 5. 测试配置删除（写权限）
                testConfigDelete(configService, namespace, testDataId, testGroup, expectedWrite);
            }
            
        } catch (Exception e) {
            logger.error("❌ 配置权限测试异常: {}", e.getMessage(), e);
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                    logger.info("🔒 ConfigService已关闭");
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 测试配置发布
     */
    private void testConfigPublish(ConfigService configService, String namespace, String dataId, String group,
                                 boolean expectedSuccess) {
        logger.info("📋 步骤1: 测试配置发布 (publishConfig)");
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        try {
            String configContent = generateConfigContent(namespace);
            logger.info("📋 配置内容长度: {} 字符", configContent.length());
            logger.info("📋 配置内容预览: {}", configContent.substring(0, Math.min(100, configContent.length())) + "...");
            
            boolean result = configService.publishConfig(dataId, group, configContent);
            
            if (result && expectedSuccess) {
                logger.info("✅ 配置发布成功 - 符合预期");
            } else if (!result && !expectedSuccess) {
                logger.info("✅ 配置发布失败 - 符合预期");
            } else if (result && !expectedSuccess) {
                logger.error("❌ 配置发布意外成功 - 不符合预期");
            } else {
                logger.error("❌ 配置发布意外失败 - 不符合预期");
            }
            
        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ 配置发布正确被拒绝 - 符合预期");
                logger.info("🔒 权限错误详情:");
                logger.info("  - 错误代码: {}", e.getErrCode());
                logger.info("  - 错误消息: {}", e.getErrMsg());
                logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            } else {
                logger.error("❌ 配置发布意外失败 - 不符合预期");
                logger.error("🔍 失败详情:");
                logger.error("  - 错误代码: {}", e.getErrCode());
                logger.error("  - 错误消息: {}", e.getErrMsg());
                logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            }
        }
        
        logger.info("");
    }
    
    /**
     * 测试查询不存在的配置
     */
    private void testConfigGetNonExistent(ConfigService configService, String namespace, String dataId, String group,
                                        boolean expectedSuccess) {
        logger.info("📋 步骤2: 测试查询不存在的配置 (getConfig)");
        logger.info("🔍 查询配置: dataId={}, group={}", dataId, group);
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功（返回null或空）" : "失败（抛出异常）");
        
        try {
            String content = configService.getConfig(dataId, group, 5000);
            
            if (expectedSuccess) {
                logger.info("✅ 配置查询成功 - 符合预期");
                if (content == null) {
                    logger.info("📋 查询结果: null（配置不存在）");
                } else if (content.trim().isEmpty()) {
                    logger.info("📋 查询结果: 空字符串（配置不存在）");
                } else {
                    logger.info("📋 查询结果: 内容长度={} 字符", content.length());
                    logger.info("📋 内容预览: {}", content.substring(0, Math.min(100, content.length())) + "...");
                }
            } else {
                logger.error("❌ 配置查询意外成功 - 不符合预期");
                logger.error("🔍 意外成功详情:");
                if (content == null) {
                    logger.error("  - 查询结果: null");
                } else {
                    logger.error("  - 查询结果: 内容长度={} 字符", content.length());
                    logger.error("  - 内容预览: {}", content.substring(0, Math.min(100, content.length())) + "...");
                }
            }
            
        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ 配置查询正确被拒绝 - 符合预期");
                logger.info("🔒 权限错误详情:");
                logger.info("  - 错误代码: {}", e.getErrCode());
                logger.info("  - 错误消息: {}", e.getErrMsg());
                logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            } else {
                logger.error("❌ 配置查询意外失败 - 不符合预期");
                logger.error("🔍 失败详情:");
                logger.error("  - 错误代码: {}", e.getErrCode());
                logger.error("  - 错误消息: {}", e.getErrMsg());
                logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            }
        }
        
        logger.info("");
    }
    
    /**
     * 测试查询已知存在的配置
     */
    private void testConfigGetExisting(ConfigService configService, String namespace, boolean expectedSuccess) {
        logger.info("📋 步骤3: 测试查询已知存在的配置");
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        // 使用一些可能存在的配置进行测试
        String[] knownConfigs = {
            "application.properties:DEFAULT_GROUP",
            "bootstrap.properties:DEFAULT_GROUP",
            "config.properties:DEFAULT_GROUP"
        };
        
        for (String configKey : knownConfigs) {
            String[] parts = configKey.split(":");
            String dataId = parts[0];
            String group = parts[1];
            
            try {
                logger.info("🔍 尝试查询: dataId={}, group={}", dataId, group);
                String content = configService.getConfig(dataId, group, 3000);
                
                if (content != null && !content.trim().isEmpty()) {
                    if (expectedSuccess) {
                        logger.info("✅ 找到已知配置 - 符合预期");
                        logger.info("📋 配置内容长度: {} 字符", content.length());
                        logger.info("📋 内容预览: {}", content.substring(0, Math.min(50, content.length())) + "...");
                        break; // 找到一个就够了
                    } else {
                        logger.error("❌ 查询已知配置意外成功 - 不符合预期");
                        logger.error("📋 配置内容长度: {} 字符", content.length());
                        break;
                    }
                }
                
            } catch (NacosException e) {
                if (!expectedSuccess) {
                    logger.info("✅ 查询已知配置正确被拒绝 - 符合预期");
                    logger.info("🔒 权限错误: {}", e.getErrMsg());
                    break;
                } else {
                    logger.warn("⚠️ 查询已知配置失败: {}", e.getErrMsg());
                    // 继续尝试下一个配置
                }
            }
        }
        
        logger.info("");
    }
    
    /**
     * 测试查询刚发布的配置
     */
    private void testConfigGetPublished(ConfigService configService, String namespace, String dataId, String group,
                                      boolean expectedSuccess) {
        logger.info("📋 步骤4: 测试查询刚发布的配置");
        logger.info("🔍 查询配置: dataId={}, group={}", dataId, group);
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        try {
            // 等待一下让配置同步
            Thread.sleep(1000);
            
            String content = configService.getConfig(dataId, group, 5000);
            
            if (expectedSuccess) {
                logger.info("✅ 查询刚发布的配置成功 - 符合预期");
                if (content != null) {
                    logger.info("📋 配置内容长度: {} 字符", content.length());
                } else {
                    logger.warn("⚠️ 配置内容为null - 可能同步延迟");
                }
            } else {
                logger.error("❌ 查询刚发布的配置意外成功 - 不符合预期");
                if (content != null) {
                    logger.error("📋 配置内容长度: {} 字符", content.length());
                }
            }
            
        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ 查询刚发布的配置正确被拒绝 - 符合预期");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
            } else {
                logger.error("❌ 查询刚发布的配置意外失败 - 不符合预期");
                logger.error("🔍 失败详情: {}", e.getErrMsg());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("⚠️ 等待过程中被中断");
        }
        
        logger.info("");
    }
    
    /**
     * 测试配置删除
     */
    private void testConfigDelete(ConfigService configService, String namespace, String dataId, String group,
                                boolean expectedSuccess) {
        logger.info("📋 步骤5: 测试配置删除 (removeConfig)");
        logger.info("🔍 删除配置: dataId={}, group={}", dataId, group);
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        try {
            boolean result = configService.removeConfig(dataId, group);
            
            if (result && expectedSuccess) {
                logger.info("✅ 配置删除成功 - 符合预期");
            } else if (!result && !expectedSuccess) {
                logger.info("✅ 配置删除失败 - 符合预期");
            } else if (result && !expectedSuccess) {
                logger.error("❌ 配置删除意外成功 - 不符合预期");
            } else {
                logger.error("❌ 配置删除意外失败 - 不符合预期");
            }
            
        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ 配置删除正确被拒绝 - 符合预期");
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
            } else {
                logger.error("❌ 配置删除意外失败 - 不符合预期");
                logger.error("🔍 失败详情: {}", e.getErrMsg());
            }
        }
        
        logger.info("");
    }
    
    // ==================== 辅助方法 ====================
    
    private ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createConfigService(properties);
    }
    
    private String generateConfigContent(String namespace) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 配置权限调试测试\n");
        sb.append("# 命名空间: ").append(namespace).append("\n");
        sb.append("# 生成时间: ").append(getCurrentTime()).append("\n");
        sb.append("# 时间戳: ").append(System.currentTimeMillis()).append("\n");
        sb.append("\n");
        sb.append("app.name=config-permission-debug-test\n");
        sb.append("app.namespace=").append(namespace).append("\n");
        sb.append("test.purpose=debug-config-permission\n");
        
        return sb.toString();
    }
    
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                  配置权限调试测试用例                          ║");
        logger.info("║              Config Permission Debug Test Suite            ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 调试cf-ns命名空间配置查询意外成功的问题                  ║");
        logger.info("║ 策略: 详细测试各种配置操作，分析权限控制行为                   ║");
        logger.info("║ 测试时间: {}", getCurrentTime());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    配置权限调试报告                           ║");
        logger.info("║              Config Permission Debug Report               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试完成时间: {}", getCurrentTime());
        logger.info("║                                                              ║");
        logger.info("║ 关键发现:                                                    ║");
        logger.info("║   请查看上述详细日志分析权限控制行为                          ║");
        logger.info("║   特别关注cf-ns命名空间的配置查询操作                         ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        logger.info("感谢您使用配置权限调试测试用例！");
    }
}
