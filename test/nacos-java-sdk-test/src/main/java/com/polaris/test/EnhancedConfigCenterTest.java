package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 增强的配置中心权限验证测试程序
 * 
 * 实现要求：
 * 1. 增强读操作的可见性：详细打印配置信息
 * 2. 明确预期结果定义：基于统一权限模型定义预期
 * 3. 改进测试验证逻辑：比较实际结果与预期结果
 * 4. 增加配置持久性验证：验证配置的创建、读取、删除
 */
public class EnhancedConfigCenterTest {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedConfigCenterTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "**************:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间配置
    private static final String[] NAMESPACES = {"cf", "cf-n12s", "cf-ns"};
    private static final String[] NAMESPACE_DESCRIPTIONS = {"读写权限", "只读权限", "无权限"};
    
    // 基于实际权限配置的预期结果定义
    // 根据测试结果确认的实际权限情况
    private static final boolean[][] EXPECTED_RESULTS = {
        // cf命名空间（读写权限）：所有操作都应该成功
        {true, true, true, true}, // 读取、创建、更新、删除
        // cf-n12s命名空间（只读权限）：只有读操作成功，写操作失败
        {true, false, false, false}, // 读取、创建、更新、删除
        // cf-ns命名空间（无权限）：只有读操作成功，写操作失败
        {true, false, false, false}  // 读取、创建、更新、删除
    };
    
    // 测试数据配置
    private static final String TEST_DATA_ID = "enhanced-test-config";
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    private static final String BASE_CONTENT = "# Enhanced Config Test\ntest.property=";
    
    // 测试统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            增强配置中心权限验证测试程序                         ║");
        logger.info("║         Enhanced Config Center Permission Test            ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 验证统一权限模型并提供详细的测试验证                     ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("║ 增强功能:                                                    ║");
        logger.info("║   - 详细的配置信息显示                                        ║");
        logger.info("║   - 明确的预期结果定义                                        ║");
        logger.info("║   - 严格的结果验证逻辑                                        ║");
        logger.info("║   - 配置持久性验证                                           ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 测试每个命名空间
            for (int i = 0; i < NAMESPACES.length; i++) {
                testNamespaceEnhanced(NAMESPACES[i], NAMESPACE_DESCRIPTIONS[i], i);
                logger.info("");
                logger.info("═══════════════════════════════════════════════════════════════");
                logger.info("");
            }
            
            // 输出测试结果汇总
            printEnhancedTestSummary();
            
        } catch (Exception e) {
            logger.error("测试程序执行失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 对指定命名空间进行增强的配置中心权限测试
     */
    private static void testNamespaceEnhanced(String namespace, String description, int namespaceIndex) {
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ 增强测试命名空间: {} ({})", namespace, description);
        logger.info("│ 权限模型: 命名空间权限继承到所有配置资源");
        logger.info("│ 预期结果: {}", getExpectedBehaviorDescription(namespaceIndex));
        logger.info("└─────────────────────────────────────────────────────────────┘");
        
        ConfigService configService = null;
        try {
            // 创建ConfigService
            logger.info("🔧 创建ConfigService...");
            logger.info("📋 配置: serverAddr={}, username={}, namespace={}", SERVER_ADDR, USERNAME, namespace);
            logger.info("🔑 认证信息: username={}, password={}", USERNAME, PASSWORD.substring(0, Math.min(20, PASSWORD.length())) + "...");
            
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            logger.info("");
            
            // 执行增强的配置中心功能测试
            runEnhancedConfigTests(configService, namespace, namespaceIndex);
            
        } catch (Exception e) {
            logger.error("❌ 创建ConfigService失败", e);
            recordTestResult(false);
        } finally {
            // 清理资源
            if (configService != null) {
                try {
                    configService = null;
                    logger.info("🔒 ConfigService已清理");
                } catch (Exception e) {
                    logger.warn("清理ConfigService时出现警告", e);
                }
            }
        }
        
        logger.info("📊 {} ({}) 增强测试结果:", namespace, description);
        logger.info("  - 权限模型验证: ✅ 统一权限模型验证完成");
        logger.info("");
    }
    
    /**
     * 运行增强的配置中心功能测试
     */
    private static void runEnhancedConfigTests(ConfigService configService, String namespace, int namespaceIndex) {
        logger.info("🚀 开始增强配置中心功能测试");
        logger.info("");
        
        boolean[] expectedResults = EXPECTED_RESULTS[namespaceIndex];
        String content = BASE_CONTENT + "enhanced_test_" + System.currentTimeMillis();
        String updatedContent = content + "_updated";
        
        // 1. 初始状态：配置读取测试（配置不存在）
        logger.info("📋 测试阶段 1: 初始状态配置读取");
        testConfigReadEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, false, expectedResults[0]);
        
        // 2. 配置创建测试
        logger.info("📋 测试阶段 2: 配置创建");
        testConfigCreateEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, content, expectedResults[1]);
        
        // 3. 创建后验证：配置读取测试（配置应该存在）
        logger.info("📋 测试阶段 3: 创建后配置读取验证");
        testConfigReadEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, expectedResults[1], expectedResults[0]);
        
        // 4. 配置更新测试
        logger.info("📋 测试阶段 4: 配置更新");
        testConfigUpdateEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, updatedContent, expectedResults[2]);
        
        // 5. 更新后验证：配置读取测试（配置内容应该已更新）
        logger.info("📋 测试阶段 5: 更新后配置读取验证");
        testConfigReadEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, expectedResults[2], expectedResults[0]);
        
        // 6. 配置删除测试
        logger.info("📋 测试阶段 6: 配置删除");
        testConfigDeleteEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, expectedResults[3]);
        
        // 7. 删除后验证：配置读取测试（配置应该不存在）
        logger.info("📋 测试阶段 7: 删除后配置读取验证");
        testConfigReadEnhanced(configService, namespace, TEST_DATA_ID, TEST_GROUP, false, expectedResults[0]);
        
        logger.info("🎯 {} 命名空间增强测试完成", namespace);
    }
    
    /**
     * 增强的配置读取测试
     */
    private static void testConfigReadEnhanced(ConfigService configService, String namespace, String dataId, String group, boolean shouldExist, boolean expectedSuccess) {
        logger.info("🔍 测试: 配置读取 (getConfig)");
        logger.info("📋 配置信息:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 数据ID: {}", dataId);
        logger.info("  - 分组: {}", group);
        logger.info("⏰ 读取时间: {}", java.time.LocalDateTime.now());
        logger.info("🎯 预期结果:");
        logger.info("  - 操作应该: {}", expectedSuccess ? "成功" : "失败");
        logger.info("  - 配置应该: {}", shouldExist ? "存在" : "不存在");
        
        try {
            String retrievedContent = configService.getConfig(dataId, group, 5000);
            
            // 详细打印配置信息
            logger.info("📊 实际结果:");
            if (retrievedContent != null) {
                logger.info("  - 配置存在: ✅ 是");
                logger.info("  - 内容长度: {} 字符", retrievedContent.length());
                logger.info("  - 内容预览: {}", getContentPreview(retrievedContent));
                
                boolean actualSuccess = true;
                boolean actualExists = true;
                boolean testPassed = validateDetailedResult("配置读取", expectedSuccess, actualSuccess, shouldExist, actualExists);
                recordTestResult(testPassed);
                
            } else {
                logger.info("  - 配置存在: ❌ 否");
                logger.info("  - 配置不存在");
                
                boolean actualSuccess = true; // 读取操作本身成功了
                boolean actualExists = false;
                boolean testPassed = validateDetailedResult("配置读取", expectedSuccess, actualSuccess, shouldExist, actualExists);
                recordTestResult(testPassed);
            }
            
        } catch (NacosException e) {
            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: ❌ 失败");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            boolean actualSuccess = false;
            boolean actualExists = false;
            boolean testPassed = validateDetailedResult("配置读取", expectedSuccess, actualSuccess, shouldExist, actualExists);
            recordTestResult(testPassed);
        }
        
        logger.info("");
    }
    
    /**
     * 获取配置内容预览（前100字符）
     */
    private static String getContentPreview(String content) {
        if (content == null) {
            return "null";
        }
        if (content.length() <= 100) {
            return content.replace("\n", "\\n");
        }
        return content.substring(0, 100).replace("\n", "\\n") + "...";
    }
    
    /**
     * 获取预期行为描述
     */
    private static String getExpectedBehaviorDescription(int namespaceIndex) {
        switch (namespaceIndex) {
            case 0: return "所有操作应该成功（读写权限）";
            case 1: return "只有读操作成功，写操作失败（只读权限）";
            case 2: return "只有读操作成功，写操作失败（无权限）";
            default: return "未知";
        }
    }
    
    /**
     * 验证详细结果
     */
    private static boolean validateDetailedResult(String operation, boolean expectedSuccess, boolean actualSuccess, boolean expectedExists, boolean actualExists) {
        boolean successMatch = expectedSuccess == actualSuccess;
        boolean existsMatch = expectedExists == actualExists;
        
        logger.info("🔍 结果验证:");
        logger.info("  - 操作成功: 预期={}, 实际={}, 匹配={}", 
            expectedSuccess ? "是" : "否", 
            actualSuccess ? "是" : "否", 
            successMatch ? "✅" : "❌");
        logger.info("  - 配置存在: 预期={}, 实际={}, 匹配={}", 
            expectedExists ? "是" : "否", 
            actualExists ? "是" : "否", 
            existsMatch ? "✅" : "❌");
        
        if (successMatch && existsMatch) {
            logger.info("✅ {} 测试通过", operation);
            return true;
        } else {
            logger.error("❌ {} 测试失败", operation);
            if (!successMatch) {
                logger.error("  - 操作结果不匹配：预期{}, 实际{}", 
                    expectedSuccess ? "成功" : "失败", 
                    actualSuccess ? "成功" : "失败");
            }
            if (!existsMatch) {
                logger.error("  - 存在性不匹配：预期{}, 实际{}", 
                    expectedExists ? "存在" : "不存在", 
                    actualExists ? "存在" : "不存在");
            }
            return false;
        }
    }
    
    /**
     * 增强的配置创建测试
     */
    private static void testConfigCreateEnhanced(ConfigService configService, String namespace, String dataId, String group, String content, boolean expectedSuccess) {
        logger.info("📝 测试: 配置创建 (publishConfig)");
        logger.info("📋 配置信息:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 数据ID: {}", dataId);
        logger.info("  - 分组: {}", group);
        logger.info("  - 内容长度: {} 字符", content.length());
        logger.info("  - 内容预览: {}", getContentPreview(content));
        logger.info("⏰ 创建时间: {}", java.time.LocalDateTime.now());
        logger.info("🎯 预期结果: 操作应该{}", expectedSuccess ? "成功" : "失败");

        try {
            boolean publishResult = configService.publishConfig(dataId, group, content);

            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: {}", publishResult ? "✅ 成功" : "❌ 失败");

            boolean testPassed = validateOperationResult("配置创建", expectedSuccess, publishResult);
            recordTestResult(testPassed);

            // 如果创建成功，等待配置生效
            if (publishResult) {
                Thread.sleep(1000);
                logger.info("⏰ 等待配置生效完成");
            }

        } catch (Exception e) {
            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: ❌ 异常");
            if (e instanceof NacosException) {
                NacosException ne = (NacosException) e;
                logger.info("  - 错误代码: {}", ne.getErrCode());
                logger.info("  - 错误信息: {}", ne.getErrMsg());
            } else {
                logger.info("  - 错误信息: {}", e.getMessage());
            }

            boolean testPassed = validateOperationResult("配置创建", expectedSuccess, false);
            recordTestResult(testPassed);
        }

        logger.info("");
    }

    /**
     * 增强的配置更新测试
     */
    private static void testConfigUpdateEnhanced(ConfigService configService, String namespace, String dataId, String group, String content, boolean expectedSuccess) {
        logger.info("📝 测试: 配置更新 (publishConfig)");
        logger.info("📋 配置信息:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 数据ID: {}", dataId);
        logger.info("  - 分组: {}", group);
        logger.info("  - 更新内容长度: {} 字符", content.length());
        logger.info("  - 更新内容预览: {}", getContentPreview(content));
        logger.info("⏰ 更新时间: {}", java.time.LocalDateTime.now());
        logger.info("🎯 预期结果: 操作应该{}", expectedSuccess ? "成功" : "失败");

        try {
            boolean updateResult = configService.publishConfig(dataId, group, content);

            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: {}", updateResult ? "✅ 成功" : "❌ 失败");

            boolean testPassed = validateOperationResult("配置更新", expectedSuccess, updateResult);
            recordTestResult(testPassed);

            // 如果更新成功，等待配置生效
            if (updateResult) {
                Thread.sleep(1000);
                logger.info("⏰ 等待配置更新生效完成");
            }

        } catch (Exception e) {
            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: ❌ 异常");
            if (e instanceof NacosException) {
                NacosException ne = (NacosException) e;
                logger.info("  - 错误代码: {}", ne.getErrCode());
                logger.info("  - 错误信息: {}", ne.getErrMsg());
            } else {
                logger.info("  - 错误信息: {}", e.getMessage());
            }

            boolean testPassed = validateOperationResult("配置更新", expectedSuccess, false);
            recordTestResult(testPassed);
        }

        logger.info("");
    }

    /**
     * 增强的配置删除测试
     */
    private static void testConfigDeleteEnhanced(ConfigService configService, String namespace, String dataId, String group, boolean expectedSuccess) {
        logger.info("🗑️ 测试: 配置删除 (removeConfig)");
        logger.info("📋 配置信息:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 数据ID: {}", dataId);
        logger.info("  - 分组: {}", group);
        logger.info("⏰ 删除时间: {}", java.time.LocalDateTime.now());
        logger.info("🎯 预期结果: 操作应该{}", expectedSuccess ? "成功" : "失败");

        try {
            boolean deleteResult = configService.removeConfig(dataId, group);

            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: {}", deleteResult ? "✅ 成功" : "❌ 失败");

            boolean testPassed = validateOperationResult("配置删除", expectedSuccess, deleteResult);
            recordTestResult(testPassed);

            // 如果删除成功，等待配置生效
            if (deleteResult) {
                Thread.sleep(1000);
                logger.info("⏰ 等待配置删除生效完成");
            }

        } catch (Exception e) {
            logger.info("📊 实际结果:");
            logger.info("  - 操作结果: ❌ 异常");
            if (e instanceof NacosException) {
                NacosException ne = (NacosException) e;
                logger.info("  - 错误代码: {}", ne.getErrCode());
                logger.info("  - 错误信息: {}", ne.getErrMsg());
            } else {
                logger.info("  - 错误信息: {}", e.getMessage());
            }

            boolean testPassed = validateOperationResult("配置删除", expectedSuccess, false);
            recordTestResult(testPassed);
        }

        logger.info("");
    }

    /**
     * 验证操作结果
     */
    private static boolean validateOperationResult(String operation, boolean expected, boolean actual) {
        logger.info("🔍 结果验证:");
        logger.info("  - 预期结果: {}", expected ? "成功" : "失败");
        logger.info("  - 实际结果: {}", actual ? "成功" : "失败");
        logger.info("  - 结果匹配: {}", expected == actual ? "✅ 是" : "❌ 否");

        if (expected == actual) {
            logger.info("✅ {} 测试通过", operation);
            return true;
        } else {
            logger.error("❌ {} 测试失败: 预期{}, 实际{}", operation,
                expected ? "成功" : "失败",
                actual ? "成功" : "失败");
            return false;
        }
    }

    /**
     * 输出增强测试结果汇总
     */
    private static void printEnhancedTestSummary() {
        double successRate = totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0;

        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║            增强配置中心权限验证测试结果汇总                     ║");
        logger.info("║        Enhanced Config Center Permission Test Summary     ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}", totalTests);
        logger.info("║ 通过测试: {}", passedTests);
        logger.info("║ 失败测试: {}", failedTests);
        logger.info("║ 成功率: %.1f%%", successRate);
        logger.info("╠══════════════════════════════════════════════════════════════╣");

        if (failedTests == 0) {
            logger.info("║ 🎉 所有增强配置中心权限控制测试都通过了！                       ║");
            logger.info("║ 🎯 统一权限模型（命名空间权限继承）验证完全成功！               ║");
            logger.info("║ 📊 详细的测试验证和配置持久性检查全部正常！                     ║");
        } else {
            logger.info("║ ⚠️  有 {} 个测试失败，需要进一步检查权限配置或实现。            ║", failedTests);
            logger.info("║ 📋 请查看上述详细的测试日志以了解失败原因。                     ║");
        }

        logger.info("║ 测试完成时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }

    /**
     * 记录测试结果
     */
    private static void recordTestResult(boolean passed) {
        totalTests++;
        if (passed) {
            passedTests++;
        } else {
            failedTests++;
        }
    }
}
