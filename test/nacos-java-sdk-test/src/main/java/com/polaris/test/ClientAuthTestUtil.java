package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * 客户端鉴权测试工具类
 * 封装test-user用户的Nacos操作
 */
public class ClientAuthTestUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientAuthTestUtil.class);
    
    /**
     * 创建 NamingService 实例
     */
    public static NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.SERVER_ADDR, TestUserConfig.SERVER_ADDR);
        properties.put(PropertyKeyConst.NAMESPACE, namespace);
        properties.put("username", TestUserConfig.TEST_USER_NAME);
        properties.put("password", TestUserConfig.TEST_USER_TOKEN); // 使用Token作为password

        logger.info("创建 NamingService - 命名空间: {}, 用户: {}, token: {}...",
                   namespace, TestUserConfig.TEST_USER_NAME, 
                   TestUserConfig.TEST_USER_TOKEN.substring(0, 20));

        return NacosFactory.createNamingService(properties);
    }
    
    /**
     * 创建 ConfigService 实例
     */
    public static ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.SERVER_ADDR, TestUserConfig.SERVER_ADDR);
        properties.put(PropertyKeyConst.NAMESPACE, namespace);
        properties.put("username", TestUserConfig.TEST_USER_NAME);
        properties.put("password", TestUserConfig.TEST_USER_TOKEN); // 使用Token作为password

        logger.info("创建 ConfigService - 命名空间: {}, 用户: {}, token: {}...",
                   namespace, TestUserConfig.TEST_USER_NAME, 
                   TestUserConfig.TEST_USER_TOKEN.substring(0, 20));

        return NacosFactory.createConfigService(properties);
    }
    
    /**
     * 注册服务实例
     */
    public static boolean registerInstance(NamingService namingService, String serviceName,
                                         String ip, int port, String groupName) {
        try {
            Instance instance = new Instance();
            instance.setIp(ip);
            instance.setPort(port);
            instance.setHealthy(true);
            instance.setWeight(1.0);
            instance.setClusterName(TestUserConfig.TEST_CLUSTER);

            logger.info("📤 注册服务实例请求:");
            logger.info("  - 服务名: {}", serviceName);
            logger.info("  - 分组: {}", groupName);
            logger.info("  - IP: {}", ip);
            logger.info("  - 端口: {}", port);
            logger.info("  - 集群: {}", TestUserConfig.TEST_CLUSTER);
            logger.info("  - 权重: {}", instance.getWeight());
            logger.info("  - 健康状态: {}", instance.isHealthy());

            long startTime = System.currentTimeMillis();
            namingService.registerInstance(serviceName, groupName, instance);
            long duration = System.currentTimeMillis() - startTime;

            logger.info("📥 注册服务实例响应:");
            logger.info("  - 耗时: {}ms", duration);
            logger.info("  - 状态: 请求已发送");

            // 等待注册生效
            Thread.sleep(2000);

            // 验证注册是否真的成功
            List<Instance> instances = getAllInstances(namingService, serviceName, groupName);
            boolean verified = instances != null && instances.size() > 0;

            if (verified) {
                logger.info("✅ 服务实例注册成功并验证通过");
                logger.info("  - 验证结果: 找到 {} 个实例", instances.size());
            } else {
                logger.warn("⚠️ 服务实例注册状态未知 - 验证失败");
            }

            return true;

        } catch (Exception e) {
            logger.error("❌ 服务实例注册异常:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            logger.error("  - 完整异常: ", e);
            return false;
        }
    }
    
    /**
     * 查询服务实例列表
     */
    public static List<Instance> getAllInstances(NamingService namingService, String serviceName, String groupName) {
        try {
            logger.info("查询服务实例: 服务={}, 分组={}", serviceName, groupName);
            
            List<Instance> instances = namingService.getAllInstances(serviceName, groupName);
            
            logger.info("✅ 服务实例查询成功，实例数量: {}", instances.size());
            return instances;
            
        } catch (Exception e) {
            logger.error("❌ 服务实例查询失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取服务列表
     */
    public static ListView<String> getServicesOfServer(NamingService namingService, int pageNo, int pageSize, String groupName) {
        try {
            logger.info("获取服务列表: 页码={}, 页大小={}, 分组={}", pageNo, pageSize, groupName);
            
            ListView<String> services = namingService.getServicesOfServer(pageNo, pageSize, groupName);
            
            logger.info("✅ 服务列表获取成功，服务数量: {}", services.getCount());
            return services;
            
        } catch (Exception e) {
            logger.error("❌ 服务列表获取失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 注销服务实例
     */
    public static boolean deregisterInstance(NamingService namingService, String serviceName,
                                           String ip, int port, String groupName) {
        try {
            logger.info("注销服务实例: 服务={}, 分组={}, IP={}, 端口={}", 
                       serviceName, groupName, ip, port);

            namingService.deregisterInstance(serviceName, groupName, ip, port);
            
            // 等待注销生效
            Thread.sleep(2000);
            
            logger.info("✅ 服务实例注销成功");
            return true;
            
        } catch (Exception e) {
            logger.error("❌ 服务实例注销失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 发布配置
     */
    public static boolean publishConfig(ConfigService configService, String dataId, String group, String content) {
        try {
            logger.info("📤 发布配置请求:");
            logger.info("  - dataId: {}", dataId);
            logger.info("  - group: {}", group);
            logger.info("  - 内容: {}", content);
            logger.info("  - 内容长度: {} 字符", content.length());

            long startTime = System.currentTimeMillis();
            boolean result = configService.publishConfig(dataId, group, content);
            long duration = System.currentTimeMillis() - startTime;

            logger.info("📥 配置发布响应:");
            logger.info("  - 结果: {}", result ? "成功" : "失败");
            logger.info("  - 耗时: {}ms", duration);

            if (result) {
                logger.info("✅ 配置发布成功");

                // 立即验证发布是否真的成功
                Thread.sleep(1000); // 等待1秒确保配置生效
                String verifyContent = getConfig(configService, dataId, group, 3000);
                if (content.equals(verifyContent)) {
                    logger.info("✅ 配置发布验证成功 - 内容一致");
                } else {
                    logger.warn("⚠️ 配置发布验证异常 - 内容不一致");
                    logger.warn("  - 期望内容: {}", content);
                    logger.warn("  - 实际内容: {}", verifyContent);
                }
            } else {
                logger.error("❌ 配置发布失败");
            }

            return result;

        } catch (Exception e) {
            logger.error("❌ 配置发布异常:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            logger.error("  - 完整异常: ", e);
            return false;
        }
    }
    
    /**
     * 获取配置
     */
    public static String getConfig(ConfigService configService, String dataId, String group, long timeoutMs) {
        try {
            logger.info("📤 获取配置请求:");
            logger.info("  - dataId: {}", dataId);
            logger.info("  - group: {}", group);
            logger.info("  - 超时: {}ms", timeoutMs);

            long startTime = System.currentTimeMillis();
            String content = configService.getConfig(dataId, group, timeoutMs);
            long duration = System.currentTimeMillis() - startTime;

            logger.info("📥 获取配置响应:");
            logger.info("  - 耗时: {}ms", duration);

            if (content != null) {
                logger.info("  - 状态: 成功");
                logger.info("  - 内容长度: {} 字符", content.length());
                logger.info("  - 内容: {}", content);
                logger.info("✅ 配置获取成功");
            } else {
                logger.info("  - 状态: 配置不存在或为空");
                logger.info("⚠️ 配置不存在或为空");
            }

            return content;

        } catch (Exception e) {
            logger.error("❌ 配置获取异常:");
            logger.error("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.error("  - 异常信息: {}", e.getMessage());
            logger.error("  - 完整异常: ", e);
            return null;
        }
    }
    
    /**
     * 删除配置
     */
    public static boolean removeConfig(ConfigService configService, String dataId, String group) {
        try {
            logger.info("删除配置: dataId={}, group={}", dataId, group);
            
            boolean result = configService.removeConfig(dataId, group);
            
            if (result) {
                logger.info("✅ 配置删除成功");
            } else {
                logger.error("❌ 配置删除失败");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("❌ 配置删除失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭服务
     */
    public static void shutdown(NamingService namingService) {
        try {
            if (namingService != null) {
                namingService.shutDown();
                logger.info("NamingService 已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭 NamingService 失败: {}", e.getMessage());
        }
    }
    
    /**
     * 关闭服务
     */
    public static void shutdown(ConfigService configService) {
        try {
            if (configService != null) {
                configService.shutDown();
                logger.info("ConfigService 已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭 ConfigService 失败: {}", e.getMessage());
        }
    }
}
