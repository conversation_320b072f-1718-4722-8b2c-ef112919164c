package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * 服务数据分析测试
 * 专门分析服务查询失败的原因
 */
public class ServiceDataAnalysis {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceDataAnalysis.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                服务数据分析测试                                ║");
        logger.info("║            Service Data Analysis Test                   ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 分析服务查询失败的具体原因                               ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 测试所有命名空间的服务数据
            String[] namespaces = {"cf", "cf-ns", "cf-n12s"};
            
            for (String namespace : namespaces) {
                logger.info("═══════════════════════════════════════════════════════════════");
                logger.info("🔍 分析命名空间: {}", namespace);
                logger.info("═══════════════════════════════════════════════════════════════");
                analyzeNamespaceServices(namespace);
                logger.info("");
            }
            
        } catch (Exception e) {
            logger.error("❌ 服务数据分析测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 分析指定命名空间的服务数据
     */
    private static void analyzeNamespaceServices(String namespace) {
        try {
            // 创建NamingService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            NamingService namingService = NacosFactory.createNamingService(properties);
            logger.info("✅ NamingService创建成功");
            
            // 测试预期存在的服务
            String[] expectedServices = {
                "user-service", "order-service", "payment-service", 
                "inventory-service", "notification-service", "auth-service",
                "gateway-service", "config-service", "monitor-service", "log-service"
            };
            
            logger.info("📊 预期服务列表: {}", String.join(", ", expectedServices));
            logger.info("");
            
            int foundServices = 0;
            int totalInstances = 0;
            
            for (String serviceName : expectedServices) {
                logger.info("🔍 查询服务: {}", serviceName);
                
                try {
                    List<Instance> instances = namingService.getAllInstances(serviceName);
                    
                    if (instances != null && !instances.isEmpty()) {
                        foundServices++;
                        totalInstances += instances.size();
                        logger.info("  ✅ 找到 {} 个实例:", instances.size());
                        
                        for (int i = 0; i < instances.size(); i++) {
                            Instance instance = instances.get(i);
                            logger.info("    实例{}: {}:{} (健康: {}, 权重: {})", 
                                i + 1, instance.getIp(), instance.getPort(), 
                                instance.isHealthy(), instance.getWeight());
                        }
                    } else {
                        logger.warn("  ❌ 服务不存在或无实例");
                    }
                    
                } catch (NacosException e) {
                    logger.error("  ❌ 查询异常: {} - {}", e.getErrCode(), e.getErrMsg());
                } catch (Exception e) {
                    logger.error("  ❌ 其他异常: {}", e.getMessage());
                }
                
                logger.info("");
            }
            
            logger.info("📊 命名空间 {} 统计:", namespace);
            logger.info("  - 预期服务数: {}", expectedServices.length);
            logger.info("  - 实际找到服务数: {}", foundServices);
            logger.info("  - 总实例数: {}", totalInstances);
            logger.info("  - 服务存在率: {}%", foundServices * 100 / expectedServices.length);
            
            // 尝试获取所有服务列表
            logger.info("");
            logger.info("🔍 尝试获取所有服务列表...");
            try {
                // 注意：Nacos Java SDK可能没有直接获取所有服务的方法
                // 这里我们通过其他方式来验证
                logger.info("  ℹ️ Nacos Java SDK不支持直接获取所有服务列表");
                logger.info("  ℹ️ 建议通过控制台或HTTP API查看完整服务列表");
            } catch (Exception e) {
                logger.warn("  ⚠️ 获取服务列表异常: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 分析失败: {}", namespace, e.getMessage());
        }
    }
}
