package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * Nacos Java SDK 登录功能专项测试
 * 按照官方文档标准方式进行登录测试
 */
public class NacosLoginTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(NacosLoginTestRunner.class);
    
    // 测试配置 - 按照官方文档标准
    private static final String SERVER_ADDR = TestConfig.SERVER_ADDR;
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    public static void main(String[] args) {
        NacosLoginTestRunner runner = new NacosLoginTestRunner();
        
        printTestHeader();
        
        try {
            // 测试1：使用原生HTTP请求模拟官方文档的curl登录
            runner.testHttpLogin();
            
            printSeparator();
            
            // 测试2：使用Java SDK按照官方文档标准方式登录
            runner.testJavaSDKLogin();
            
            printSeparator();
            
            // 测试3：对比分析
            runner.printComparisonAnalysis();
            
        } catch (Exception e) {
            logger.error("测试执行失败", e);
        }
        
        printTestFooter();
    }
    
    /**
     * 测试1：使用原生HTTP请求模拟官方文档的curl登录
     */
    private void testHttpLogin() {
        printSectionHeader("测试1：原生HTTP请求登录（模拟官方文档curl）");
        
        try {
            String loginUrl = "http://" + SERVER_ADDR + "/nacos/v1/auth/users/login";
            String postData = "username=" + URLEncoder.encode(USERNAME, StandardCharsets.UTF_8) + 
                             "&password=" + URLEncoder.encode(PASSWORD, StandardCharsets.UTF_8);
            
            logger.info("🌐 准备发送HTTP登录请求（按官方文档格式）");
            logger.info("请求URL: {}", loginUrl);
            logger.info("请求方法: POST");
            logger.info("Content-Type: application/x-www-form-urlencoded");
            logger.info("请求体: {}", postData);
            
            URL url = new URL(loginUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("User-Agent", "Java-HTTP-Client-Test");
            conn.setDoOutput(true);
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            
            // 发送请求
            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 发送时间: {}", timestamp);
            
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = postData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = conn.getResponseCode();
            String responseMessage = conn.getResponseMessage();
            
            logger.info("📥 HTTP响应详情:");
            logger.info("响应状态码: {} {}", responseCode, responseMessage);
            
            // 读取响应体
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? conn.getInputStream() : conn.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            }
            
            String responseBody = response.toString();
            logger.info("响应体内容: {}", responseBody);
            
            // 解析accessToken
            if (responseCode == 200 && responseBody.contains("accessToken")) {
                String accessToken = extractAccessToken(responseBody);
                logger.info("✅ 提取到的accessToken: {}...", 
                           accessToken.length() > 20 ? accessToken.substring(0, 20) : accessToken);
                logger.info("accessToken完整长度: {} 字符", accessToken.length());
            } else {
                logger.error("❌ HTTP登录失败，状态码: {}", responseCode);
            }
            
        } catch (Exception e) {
            logger.error("❌ HTTP登录测试失败", e);
        }
    }
    
    /**
     * 测试2：使用Java SDK登录（按照官方文档标准方式）
     */
    private void testJavaSDKLogin() {
        printSectionHeader("测试2：Java SDK登录测试（官方文档标准方式）");
        
        try {
            // 按照官方文档配置Properties
            // 官方示例：properties.put("serverAddr", serverAddr);
            // 官方示例：properties.put("username","${username}");
            // 官方示例：properties.put("password","${password}");
            
            String serverAddr = SERVER_ADDR;
            Properties properties = new Properties();
            properties.put("serverAddr", serverAddr);
            
            // if need username and password to login
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            
            logger.info("🔧 Java SDK配置详情（按官方文档）:");
            logger.info("serverAddr: {}", properties.getProperty("serverAddr"));
            logger.info("username: {}", properties.getProperty("username"));
            logger.info("password: {}...", PASSWORD.substring(0, Math.min(20, PASSWORD.length())));
            logger.info("password完整长度: {} 字符", PASSWORD.length());
            logger.info("Properties总数: {}", properties.size());
            
            String timestamp = getCurrentTimestamp();
            logger.info("⏰ 创建服务时间: {}", timestamp);
            
            // 按照官方文档创建ConfigService和NamingService
            logger.info("🚀 开始创建ConfigService（官方标准方式）...");
            logger.info("调用: ConfigService configService = NacosFactory.createConfigService(properties);");
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            
            logger.info("🚀 开始创建NamingService（官方标准方式）...");
            logger.info("调用: NamingService namingService = NacosFactory.createNamingService(properties);");
            
            NamingService namingService = NacosFactory.createNamingService(properties);
            logger.info("✅ NamingService创建成功");
            
            // 尝试获取服务列表来验证连接
            logger.info("🔍 尝试获取服务列表验证连接...");
            try {
                namingService.getServicesOfServer(1, 10);
                logger.info("✅ 服务列表获取成功，连接正常");
            } catch (Exception e) {
                logger.error("❌ 服务列表获取失败: {}", e.getMessage());
                logger.error("异常详情:", e);
            }
            
            // 关闭连接
            try {
                configService.shutDown();
                logger.info("🔒 ConfigService已关闭");
                namingService.shutDown();
                logger.info("🔒 NamingService已关闭");
            } catch (Exception e) {
                logger.warn("关闭服务时出现警告: {}", e.getMessage());
            }
            
        } catch (NacosException e) {
            logger.error("❌ Java SDK登录失败");
            logger.error("错误码: {}", e.getErrCode());
            logger.error("错误信息: {}", e.getErrMsg());
            logger.error("详细异常:", e);
        } catch (Exception e) {
            logger.error("❌ Java SDK测试失败", e);
        }
    }
    
    /**
     * 测试3：对比分析
     */
    private void printComparisonAnalysis() {
        printSectionHeader("测试3：对比分析总结");
        
        logger.info("📊 官方文档标准方式 vs 实际实现对比:");
        logger.info("");
        logger.info("🔍 官方文档登录方式:");
        logger.info("1. HTTP API登录:");
        logger.info("   curl -X POST '127.0.0.1:8848/nacos/v3/auth/user/login' -d 'username=nacos&password=nacos'");
        logger.info("");
        logger.info("2. Java SDK登录:");
        logger.info("   Properties properties = new Properties();");
        logger.info("   properties.put(\"serverAddr\", serverAddr);");
        logger.info("   properties.put(\"username\", \"${username}\");");
        logger.info("   properties.put(\"password\", \"${password}\");");
        logger.info("   ConfigService configService = NacosFactory.createConfigService(properties);");
        logger.info("   NamingService namingService = NacosFactory.createNamingService(properties);");
        logger.info("");
        logger.info("🎯 测试结果分析:");
        logger.info("1. 检查HTTP登录是否成功");
        logger.info("2. 检查Java SDK是否能正确传递认证信息");
        logger.info("3. 对比服务端日志中的请求差异");
        logger.info("");
        logger.info("💡 预期结果:");
        logger.info("- HTTP请求应该成功获取accessToken");
        logger.info("- Java SDK应该能够成功创建服务并进行认证");
        logger.info("- 服务端日志应该显示正确的登录请求");
    }
    
    // 工具方法
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              Nacos Java SDK 官方标准登录测试                  ║");
        logger.info("║              Official Standard Login Test                    ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 按照官方文档标准方式进行登录                          ║");
        logger.info("║ 参考文档: https://nacos.io/docs/latest/manual/user/auth/     ║");
        logger.info("║ 服务器地址: {}                                    ║", SERVER_ADDR);
        logger.info("║ 测试用户: {}                                                 ║", USERNAME);
        logger.info("║ 测试时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    private static void printTestFooter() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                        测试完成                              ║");
        logger.info("║                    Test Completed                           ║");
        logger.info("║ 完成时间: {}                              ║", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }
    
    private static void printSectionHeader(String title) {
        logger.info("");
        logger.info("┌─────────────────────────────────────────────────────────────┐");
        logger.info("│ {}                                                    │", String.format("%-59s", title));
        logger.info("└─────────────────────────────────────────────────────────────┘");
    }
    
    private static void printSeparator() {
        logger.info("");
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("");
    }
    
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    private String extractAccessToken(String jsonResponse) {
        // 简单的JSON解析，提取accessToken
        try {
            String searchStr = "\"accessToken\":\"";
            int startIndex = jsonResponse.indexOf(searchStr);
            if (startIndex != -1) {
                startIndex += searchStr.length();
                int endIndex = jsonResponse.indexOf("\"", startIndex);
                if (endIndex != -1) {
                    return jsonResponse.substring(startIndex, endIndex);
                }
            }
        } catch (Exception e) {
            logger.warn("解析accessToken失败", e);
        }
        return "";
    }
}
