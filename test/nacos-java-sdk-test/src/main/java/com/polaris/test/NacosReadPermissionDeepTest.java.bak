package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Nacos读权限绕过问题深度测试
 * 
 * 专门用于调查读操作权限绕过漏洞的详细测试程序
 * 
 * 测试目标：
 * 1. 精确测试各种读权限场景
 * 2. 记录详细的请求和响应信息
 * 3. 与K8s日志进行时间关联分析
 * 4. 对比读写操作的权限验证差异
 */
public class NacosReadPermissionDeepTest {

    private static final Logger logger = LoggerFactory.getLogger(NacosReadPermissionDeepTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = TestConfig.SERVER_ADDR;
    private static final String VALID_USERNAME = "cf";
    private static final String VALID_TOKEN = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    private static final String INVALID_TOKEN = "invalid-token-123";
    
    // 测试命名空间
    private static final String ALLOWED_NAMESPACE = "cf";
    private static final String FORBIDDEN_NAMESPACE = "cf-n12s";
    
    // 测试服务配置
    private static final String TEST_SERVICE_NAME = "deep-test-service";
    private static final String TEST_GROUP_NAME = "DEFAULT_GROUP";
    private static final String TEST_INSTANCE_IP = "127.0.0.1";
    private static final int TEST_INSTANCE_PORT = 9090;
    
    // 测试结果统计
    private int totalTests = 0;
    private int passedTests = 0;
    private int failedTests = 0;
    
    public static void main(String[] args) {
        NacosReadPermissionDeepTest tester = new NacosReadPermissionDeepTest();
        tester.runAllTests();
    }
    
    public void runAllTests() {
        printTestHeader();
        
        try {
            // 阶段1：有权限命名空间测试
            testAllowedNamespaceScenarios();
            
            // 阶段2：无权限命名空间测试（核心测试）
            testForbiddenNamespaceScenarios();
            
            // 阶段3：Token验证测试
            testTokenValidationScenarios();
            
            // 阶段4：对比写操作权限验证
            testWriteOperationComparison();
            
        } catch (Exception e) {
            logger.error("❌ 测试执行过程中发生异常", e);
        }
        
        printTestSummary();
    }
    
    /**
     * 测试有权限命名空间的各种场景
     */
    private void testAllowedNamespaceScenarios() {
        printSectionHeader("阶段1：有权限命名空间(cf)测试");
        
        // 场景1.1：有效token + 有权限命名空间 → 读操作
        testReadOperationWithValidToken(ALLOWED_NAMESPACE, "有权限命名空间读操作");
        
        // 场景1.2：有效token + 有权限命名空间 → 写操作（对比用）
        testWriteOperationWithValidToken(ALLOWED_NAMESPACE, "有权限命名空间写操作");
    }
    
    /**
     * 测试无权限命名空间的各种场景（核心测试）
     */
    private void testForbiddenNamespaceScenarios() {
        printSectionHeader("阶段2：无权限命名空间(cf-n12s)测试 - 核心权限绕过测试");
        
        // 场景2.1：有效token + 无权限命名空间 → 读操作（应该被拒绝）
        testReadOperationWithValidToken(FORBIDDEN_NAMESPACE, "无权限命名空间读操作");
        
        // 场景2.2：有效token + 无权限命名空间 → 写操作（应该被拒绝，对比用）
        testWriteOperationWithValidToken(FORBIDDEN_NAMESPACE, "无权限命名空间写操作");
    }
    
    /**
     * 测试Token验证场景
     */
    private void testTokenValidationScenarios() {
        printSectionHeader("阶段3：Token验证测试");
        
        // 场景3.1：无效token + 有权限命名空间 → 读操作
        testReadOperationWithInvalidToken(ALLOWED_NAMESPACE, "无效token读操作");
        
        // 场景3.2：缺失token + 有权限命名空间 → 读操作
        testReadOperationWithoutToken(ALLOWED_NAMESPACE, "缺失token读操作");
        
        // 场景3.3：无效token + 有权限命名空间 → 写操作（对比用）
        testWriteOperationWithInvalidToken(ALLOWED_NAMESPACE, "无效token写操作");
    }
    
    /**
     * 对比写操作的权限验证行为
     */
    private void testWriteOperationComparison() {
        printSectionHeader("阶段4：读写操作权限验证对比");
        
        logger.info("📋 对比分析：");
        logger.info("   - 写操作权限验证：预期正常工作");
        logger.info("   - 读操作权限验证：预期存在绕过漏洞");
        logger.info("   - 通过对比找出权限验证的差异点");
    }
    
    /**
     * 测试读操作 - 有效token
     */
    private void testReadOperationWithValidToken(String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("🔍 [{}] 开始测试: {}", timestamp, testName);
        logger.info("   参数: namespace={}, token={}...", namespace, VALID_TOKEN.substring(0, 10));
        
        try {
            NamingService namingService = createNamingService(VALID_TOKEN);
            
            // 测试1：获取服务实例列表
            testGetAllInstances(namingService, namespace, testName + "-实例查询");
            
            // 测试2：获取服务列表
            testGetServicesOfServer(namingService, namespace, testName + "-服务列表");
            
        } catch (Exception e) {
            recordTestResult(testName, false, "创建NamingService失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试读操作 - 无效token
     */
    private void testReadOperationWithInvalidToken(String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("🔍 [{}] 开始测试: {}", timestamp, testName);
        logger.info("   参数: namespace={}, token={}", namespace, INVALID_TOKEN);
        
        try {
            NamingService namingService = createNamingService(INVALID_TOKEN);
            
            // 测试1：获取服务实例列表
            testGetAllInstances(namingService, namespace, testName + "-实例查询");
            
            // 测试2：获取服务列表
            testGetServicesOfServer(namingService, namespace, testName + "-服务列表");
            
        } catch (Exception e) {
            recordTestResult(testName, true, "正确被拒绝: " + e.getMessage());
        }
    }
    
    /**
     * 测试读操作 - 缺失token
     */
    private void testReadOperationWithoutToken(String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("🔍 [{}] 开始测试: {}", timestamp, testName);
        logger.info("   参数: namespace={}, token=<缺失>", namespace);
        
        try {
            NamingService namingService = createNamingService(null);
            
            // 测试1：获取服务实例列表
            testGetAllInstances(namingService, namespace, testName + "-实例查询");
            
            // 测试2：获取服务列表
            testGetServicesOfServer(namingService, namespace, testName + "-服务列表");
            
        } catch (Exception e) {
            recordTestResult(testName, true, "正确被拒绝: " + e.getMessage());
        }
    }
    
    /**
     * 测试写操作 - 有效token（对比用）
     */
    private void testWriteOperationWithValidToken(String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("✏️ [{}] 开始测试: {}", timestamp, testName);
        logger.info("   参数: namespace={}, token={}...", namespace, VALID_TOKEN.substring(0, 10));
        
        try {
            NamingService namingService = createNamingService(VALID_TOKEN);
            
            // 测试服务注册
            testRegisterInstance(namingService, namespace, testName + "-服务注册");
            
            // 等待一秒
            Thread.sleep(1000);
            
            // 测试服务删除
            testDeregisterInstance(namingService, namespace, testName + "-服务删除");
            
        } catch (Exception e) {
            recordTestResult(testName, false, "创建NamingService失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试写操作 - 无效token（对比用）
     */
    private void testWriteOperationWithInvalidToken(String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("✏️ [{}] 开始测试: {}", timestamp, testName);
        logger.info("   参数: namespace={}, token={}", namespace, INVALID_TOKEN);
        
        try {
            NamingService namingService = createNamingService(INVALID_TOKEN);
            
            // 测试服务注册
            testRegisterInstance(namingService, namespace, testName + "-服务注册");
            
        } catch (Exception e) {
            recordTestResult(testName, true, "正确被拒绝: " + e.getMessage());
        }
    }
    
    /**
     * 创建NamingService实例
     */
    private NamingService createNamingService(String token) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", VALID_USERNAME);

        if (token != null) {
            properties.setProperty("password", token);
        }

        return NacosFactory.createNamingService(properties);
    }

    /**
     * 测试获取所有服务实例
     */
    private void testGetAllInstances(NamingService namingService, String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("   🔍 [{}] 子测试: {}", timestamp, testName);

        try {
            List<Instance> instances = namingService.getAllInstances(TEST_SERVICE_NAME, TEST_GROUP_NAME, namespace);

            logger.info("   📊 查询结果: 共找到 {} 个实例", instances.size());
            for (int i = 0; i < instances.size(); i++) {
                Instance instance = instances.get(i);
                logger.info("     实例[{}]: IP={}, Port={}, Weight={}, Healthy={}, Enabled={}",
                    i + 1, instance.getIp(), instance.getPort(), instance.getWeight(),
                    instance.isHealthy(), instance.isEnabled());
            }

            // 根据命名空间判断是否应该成功
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, false, "权限绕过漏洞！应该被拒绝但成功返回了" + instances.size() + "个实例");
            } else {
                recordTestResult(testName, true, "成功返回" + instances.size() + "个实例");
            }

        } catch (NacosException e) {
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                recordTestResult(testName, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试获取服务列表
     */
    private void testGetServicesOfServer(NamingService namingService, String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("   🔍 [{}] 子测试: {}", timestamp, testName);

        try {
            ListView<String> serviceList = namingService.getServicesOfServer(1, 20, namespace);

            logger.info("   📊 查询结果: 总服务数量={}, 当前页服务数量={}",
                serviceList.getCount(), serviceList.getData().size());

            List<String> services = serviceList.getData();
            for (int i = 0; i < services.size(); i++) {
                logger.info("     服务[{}]: {}", i + 1, services.get(i));
            }

            // 根据命名空间判断是否应该成功
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, false, "权限绕过漏洞！应该被拒绝但成功返回了" + services.size() + "个服务");
            } else {
                recordTestResult(testName, true, "成功返回" + services.size() + "个服务");
            }

        } catch (NacosException e) {
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                recordTestResult(testName, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试服务注册
     */
    private void testRegisterInstance(NamingService namingService, String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("   ✏️ [{}] 子测试: {}", timestamp, testName);

        try {
            Instance instance = new Instance();
            instance.setIp(TEST_INSTANCE_IP);
            instance.setPort(TEST_INSTANCE_PORT);
            instance.setWeight(1.0);
            instance.setHealthy(true);
            instance.setEnabled(true);

            namingService.registerInstance(TEST_SERVICE_NAME, TEST_GROUP_NAME, namespace, instance);

            // 根据命名空间判断是否应该成功
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, false, "权限验证失效！应该被拒绝但成功注册了服务");
            } else {
                recordTestResult(testName, true, "成功注册服务实例");
            }

        } catch (NacosException e) {
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                recordTestResult(testName, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试服务删除
     */
    private void testDeregisterInstance(NamingService namingService, String namespace, String testName) {
        String timestamp = getCurrentTimestamp();
        logger.info("   ✏️ [{}] 子测试: {}", timestamp, testName);

        try {
            namingService.deregisterInstance(TEST_SERVICE_NAME, TEST_GROUP_NAME, namespace,
                TEST_INSTANCE_IP, TEST_INSTANCE_PORT);

            // 根据命名空间判断是否应该成功
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, false, "权限验证失效！应该被拒绝但成功删除了服务");
            } else {
                recordTestResult(testName, true, "成功删除服务实例");
            }

        } catch (NacosException e) {
            if (FORBIDDEN_NAMESPACE.equals(namespace)) {
                recordTestResult(testName, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                recordTestResult(testName, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    private void printTestHeader() {
        logger.info("🚀 ==========================================");
        logger.info("🚀 Nacos读权限绕过问题深度测试");
        logger.info("🚀 测试时间: {}", getCurrentTimestamp());
        logger.info("🚀 服务地址: {}", SERVER_ADDR);
        logger.info("🚀 测试用户: {}", VALID_USERNAME);
        logger.info("🚀 ==========================================");
    }
    
    private void printSectionHeader(String sectionName) {
        logger.info("");
        logger.info("📋 ==========================================");
        logger.info("📋 {}", sectionName);
        logger.info("📋 ==========================================");
    }
    
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void recordTestResult(String testName, boolean passed, String message) {
        totalTests++;
        if (passed) {
            passedTests++;
            logger.info("✅ {}: {}", testName, message);
        } else {
            failedTests++;
            logger.error("❌ {}: {}", testName, message);
        }
    }
    
    private void printTestSummary() {
        logger.info("");
        logger.info("📊 ==========================================");
        logger.info("📊 测试结果统计");
        logger.info("📊 ==========================================");
        logger.info("📊 总测试数: {}", totalTests);
        logger.info("📊 通过测试: {}", passedTests);
        logger.info("📊 失败测试: {}", failedTests);
        logger.info("📊 通过率: {:.1f}%", totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0);
        logger.info("📊 ==========================================");
    }
}
