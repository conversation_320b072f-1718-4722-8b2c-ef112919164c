package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 权限绕过问题分析测试程序
 * 
 * 背景：
 * - cf命名空间中已有测试配置：permission-verify-config-1757338541716
 * - 已通过控制台取消cf用户对cf命名空间的读权限
 * - 已通过控制台将cf-n12s命名空间赋予cf用户读写权限
 * - 发现：cf用户仍能读取cf命名空间的配置（权限绕过问题）
 */
public class PermissionBypassTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionBypassTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 已存在的测试配置
    private static final String EXISTING_CONFIG_DATA_ID = "permission-verify-config-1757338541716";
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              权限绕过问题分析测试程序                           ║");
        logger.info("║         Permission Bypass Analysis Test Program          ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 深入分析和修复权限绕过问题                              ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("║ 问题描述:                                                    ║");
        logger.info("║   - cf用户已被取消cf命名空间读权限                            ║");
        logger.info("║   - 但仍能读取cf命名空间的配置                                ║");
        logger.info("║   - 需要分析原因并提供修复方案                                ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 阶段1：验证当前权限状态
            logger.info("🔍 阶段1：验证当前权限状态");
            verifyCurrentPermissionStatus();
            
        } catch (Exception e) {
            logger.error("权限绕过分析测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 阶段1：验证当前权限状态
     */
    private static void verifyCurrentPermissionStatus() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 阶段1：验证当前权限状态");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        // 测试cf命名空间（应该被拒绝）
        logger.info("");
        logger.info("📋 测试命名空间: cf (已取消读权限)");
        logger.info("🎯 预期结果: 所有操作都应该失败");
        testNamespacePermissions("cf", false, false, false, false);
        
        // 测试cf-n12s命名空间（应该成功）
        logger.info("");
        logger.info("📋 测试命名空间: cf-n12s (读写权限)");
        logger.info("🎯 预期结果: 所有操作都应该成功");
        testNamespacePermissions("cf-n12s", true, true, true, true);
        
        // 测试cf-ns命名空间（应该被拒绝）
        logger.info("");
        logger.info("📋 测试命名空间: cf-ns (无权限)");
        logger.info("🎯 预期结果: 所有操作都应该失败");
        testNamespacePermissions("cf-ns", false, false, false, false);
        
        logger.info("");
        logger.info("✅ 阶段1完成：当前权限状态验证完毕");
        logger.info("");
        
        // 分析结果
        analyzeResults();
    }
    
    /**
     * 测试指定命名空间的权限
     */
    private static void testNamespacePermissions(String namespace, boolean expectedRead, boolean expectedCreate, boolean expectedUpdate, boolean expectedDelete) {
        ConfigService configService = null;
        try {
            // 创建ConfigService
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");
            
            // 测试读取已存在的配置（权限绕过问题的核心）
            testReadExistingConfig(configService, namespace, expectedRead);
            
            // 测试创建新配置
            testCreateConfig(configService, namespace, expectedCreate);
            
        } catch (Exception e) {
            logger.error("❌ 测试命名空间 {} 权限时出现异常: {}", namespace, e.getMessage());
        } finally {
            if (configService != null) {
                configService = null;
            }
        }
    }
    
    /**
     * 测试读取已存在的配置（权限绕过问题的核心）
     */
    private static void testReadExistingConfig(ConfigService configService, String namespace, boolean expectedSuccess) {
        logger.info("🔍 测试: 读取已存在配置");
        logger.info("📋 配置: namespace={}, dataId={}, group={}", namespace, EXISTING_CONFIG_DATA_ID, TEST_GROUP);
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        try {
            long startTime = System.currentTimeMillis();
            String content = configService.getConfig(EXISTING_CONFIG_DATA_ID, TEST_GROUP, 5000);
            long endTime = System.currentTimeMillis();
            
            if (content != null) {
                logger.info("📊 实际结果: ✅ 成功读取配置");
                logger.info("  - 响应时间: {}ms", endTime - startTime);
                logger.info("  - 内容长度: {} 字符", content.length());
                logger.info("  - 内容预览: {}", getContentPreview(content));
                
                if (expectedSuccess) {
                    logger.info("✅ 权限验证通过：预期成功，实际成功");
                } else {
                    logger.error("❌ 权限绕过问题确认：预期失败，实际成功");
                    logger.error("🚨 这是一个严重的权限绕过漏洞！");
                }
            } else {
                logger.info("📊 实际结果: ❌ 配置不存在或读取失败");
                
                if (!expectedSuccess) {
                    logger.info("✅ 权限验证通过：预期失败，实际失败");
                } else {
                    logger.warn("⚠️ 配置可能不存在，无法验证读权限");
                }
            }
            
        } catch (NacosException e) {
            logger.info("📊 实际结果: ❌ 读取失败");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            if (!expectedSuccess) {
                logger.info("✅ 权限验证通过：预期失败，实际失败");
            } else {
                logger.error("❌ 权限验证失败：预期成功，实际失败");
            }
        }
        
        logger.info("");
    }
    
    /**
     * 测试创建配置
     */
    private static void testCreateConfig(ConfigService configService, String namespace, boolean expectedSuccess) {
        logger.info("📝 测试: 创建新配置");
        String dataId = "bypass-test-config-" + System.currentTimeMillis();
        String content = "# Permission Bypass Test\ntest.timestamp=" + System.currentTimeMillis();
        
        logger.info("📋 配置: namespace={}, dataId={}, group={}", namespace, dataId, TEST_GROUP);
        logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");
        
        try {
            boolean result = configService.publishConfig(dataId, TEST_GROUP, content);
            
            logger.info("📊 实际结果: {}", result ? "✅ 创建成功" : "❌ 创建失败");
            
            if (result == expectedSuccess) {
                logger.info("✅ 权限验证通过：预期{}，实际{}", 
                    expectedSuccess ? "成功" : "失败", 
                    result ? "成功" : "失败");
            } else {
                logger.error("❌ 权限验证失败：预期{}，实际{}", 
                    expectedSuccess ? "成功" : "失败", 
                    result ? "成功" : "失败");
            }
            
            // 如果创建成功，尝试清理
            if (result) {
                try {
                    Thread.sleep(1000);
                    configService.removeConfig(dataId, TEST_GROUP);
                    logger.debug("🧹 测试配置已清理");
                } catch (Exception cleanupException) {
                    logger.debug("清理测试配置时出现异常（可忽略）: {}", cleanupException.getMessage());
                }
            }
            
        } catch (NacosException e) {
            logger.info("📊 实际结果: ❌ 创建失败");
            logger.info("  - 错误代码: {}", e.getErrCode());
            logger.info("  - 错误信息: {}", e.getErrMsg());
            
            if (!expectedSuccess) {
                logger.info("✅ 权限验证通过：预期失败，实际失败");
            } else {
                logger.error("❌ 权限验证失败：预期成功，实际失败");
            }
        }
        
        logger.info("");
    }
    
    /**
     * 获取内容预览
     */
    private static String getContentPreview(String content) {
        if (content == null) {
            return "null";
        }
        if (content.length() <= 100) {
            return content.replace("\n", "\\n");
        }
        return content.substring(0, 100).replace("\n", "\\n") + "...";
    }
    
    /**
     * 分析测试结果
     */
    private static void analyzeResults() {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 权限绕过问题分析");
        logger.info("═══════════════════════════════════════════════════════════════");
        
        logger.info("🔍 可能的权限绕过原因：");
        logger.info("1. 🕐 权限缓存问题：Nacos服务端可能存在权限缓存，变更后未及时刷新");
        logger.info("2. 👥 用户角色继承：cf用户可能通过其他角色获得了权限");
        logger.info("3. 🔄 客户端缓存：Nacos客户端可能缓存了旧的权限信息");
        logger.info("4. ⚙️ 配置问题：权限配置可能未正确生效");
        logger.info("5. 🐛 Nacos版本bug：当前Nacos版本可能存在权限验证漏洞");
        logger.info("");
        
        logger.info("🛠️ 建议的修复方案：");
        logger.info("1. 🔄 重启Nacos服务：清理所有缓存，强制重新加载权限配置");
        logger.info("2. 🧹 清理权限缓存：如果Nacos提供缓存清理接口，执行缓存清理");
        logger.info("3. 👤 检查用户角色：确认cf用户没有通过其他角色获得权限");
        logger.info("4. ⚙️ 重新配置权限：删除并重新创建权限配置");
        logger.info("5. 📈 升级Nacos版本：如果是已知bug，升级到修复版本");
        logger.info("");
    }
}
