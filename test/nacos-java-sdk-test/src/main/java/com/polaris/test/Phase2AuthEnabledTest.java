package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.UUID;

/**
 * 阶段2：客户端鉴权开启状态下的权限控制验证测试
 * 
 * 测试目标：
 * 1. 验证cf命名空间的读写权限（所有操作都应该成功）
 * 2. 验证cf-n12s命名空间的只读权限（读操作成功，写操作被拒绝）
 * 3. 验证cf-ns命名空间的无权限（所有操作都应该被拒绝）
 * 
 * 改进特性：
 * - 完整的响应信息捕获和输出
 * - 动态唯一命名策略避免冲突
 * - 缓存清理机制
 * - 详细的日志时间标记便于与服务端日志对应
 * - 中文输出和注释
 */
public class Phase2AuthEnabledTest {
    
    private static final Logger logger = LoggerFactory.getLogger(Phase2AuthEnabledTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = TestConfig.SERVER_ADDR;
    private static final String USERNAME = TestConfig.CF_USER_NAME;
    private static final String PASSWORD = TestConfig.CF_USER_TOKEN;
    
    // 测试命名空间配置
    private static final String CF_NAMESPACE = "cf";           // 读写权限
    private static final String CF_N12S_NAMESPACE = "cf-n12s"; // 只读权限
    private static final String CF_NS_NAMESPACE = "cf-ns";     // 无权限
    
    // 测试配置
    private static final String TEST_GROUP = "DEFAULT_GROUP";
    private static final String TEST_IP = "*************";
    private static final int TEST_PORT = 8080;
    
    public static void main(String[] args) {
        printTestHeader();
        
        try {
            // 清理本地缓存
            clearLocalCache();
            
            // 执行权限测试
            executePermissionTests();
            
        } catch (Exception e) {
            logger.error("❌ 阶段2测试执行失败", e);
            System.exit(1);
        }
        
        printTestFooter();
    }
    
    /**
     * 打印测试头部信息
     */
    private static void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                阶段2：客户端鉴权开启状态测试                    ║");
        logger.info("║            Phase 2: Client Auth Enabled Test               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证权限控制系统在鉴权开启状态下的工作情况             ║");
        logger.info("║ 测试时间: {}", getCurrentTimestamp());
        logger.info("║ 测试用户: {}", USERNAME);
        logger.info("║ 服务地址: {}", SERVER_ADDR);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
    
    /**
     * 打印测试尾部信息
     */
    private static void printTestFooter() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    阶段2测试完成                              ║");
        logger.info("║ 完成时间: {}", getCurrentTimestamp());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }
    
    /**
     * 获取当前时间戳
     */
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 清理本地缓存
     */
    private static void clearLocalCache() {
        logger.info("🧹 清理本地缓存...");
        
        try {
            // 清理用户主目录下的nacos缓存
            String userHome = System.getProperty("user.home");
            String[] cachePaths = {
                userHome + "/nacos",
                userHome + "/.nacos",
                System.getProperty("java.io.tmpdir") + "/nacos"
            };
            
            for (String cachePath : cachePaths) {
                File cacheDir = new File(cachePath);
                if (cacheDir.exists()) {
                    logger.info("  - 发现缓存目录: {}", cachePath);
                    deleteCacheDirectory(cacheDir);
                    logger.info("  - 缓存目录已清理: {}", cachePath);
                }
            }
            
            logger.info("✅ 本地缓存清理完成");
            
        } catch (Exception e) {
            logger.warn("⚠️ 缓存清理过程中出现问题: {}", e.getMessage());
        }
        
        logger.info("");
    }
    
    /**
     * 递归删除缓存目录
     */
    private static void deleteCacheDirectory(File dir) {
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteCacheDirectory(file);
                }
            }
        }
        dir.delete();
    }
    
    /**
     * 执行权限测试
     */
    private static void executePermissionTests() {
        logger.info("🚀 开始执行权限控制验证测试");
        logger.info("");
        
        // 测试cf命名空间（读写权限）
        testNamespacePermissions(CF_NAMESPACE, "读写权限", true, true);
        
        // 测试cf-n12s命名空间（只读权限）
        testNamespacePermissions(CF_N12S_NAMESPACE, "只读权限", true, false);
        
        // 测试cf-ns命名空间（无权限）
        testNamespacePermissions(CF_NS_NAMESPACE, "无权限", false, false);
    }
    
    /**
     * 测试指定命名空间的权限
     */
    private static void testNamespacePermissions(String namespace, String expectedPermission, 
                                               boolean shouldAllowRead, boolean shouldAllowWrite) {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 测试命名空间: {} (预期权限: {})", namespace, expectedPermission);
        logger.info("  - 预期读权限: {}", shouldAllowRead ? "✅ 允许" : "❌ 拒绝");
        logger.info("  - 预期写权限: {}", shouldAllowWrite ? "✅ 允许" : "❌ 拒绝");
        logger.info("  - 测试开始时间: {}", getCurrentTimestamp());
        logger.info("═══════════════════════════════════════════════════════════════");
        
        try {
            // 为每个命名空间创建独立的服务实例
            NamingService namingService = createNamingService(namespace);
            ConfigService configService = createConfigService(namespace);
            
            // 测试配置中心权限
            testConfigPermissions(configService, namespace, shouldAllowRead, shouldAllowWrite);
            
            // 测试服务注册发现权限
            testServicePermissions(namingService, namespace, shouldAllowRead, shouldAllowWrite);
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 测试失败: {}", namespace, e.getMessage(), e);
        }
        
        logger.info("");
    }
    
    /**
     * 创建命名服务实例
     */
    private static NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createNamingService(properties);
    }
    
    /**
     * 创建配置服务实例
     */
    private static ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        // 禁用本地缓存以确保每次都从服务器获取最新数据
        properties.setProperty("enableRemoteSyncConfig", "false");
        
        return NacosFactory.createConfigService(properties);
    }
    
    /**
     * 生成唯一的测试标识符
     */
    private static String generateUniqueId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 生成唯一的服务名
     */
    private static String generateUniqueServiceName() {
        return "phase2-test-service-" + generateUniqueId() + "-" + System.currentTimeMillis();
    }
    
    /**
     * 生成唯一的配置ID
     */
    private static String generateUniqueConfigId() {
        return "phase2-test-config-" + generateUniqueId() + "-" + System.currentTimeMillis();
    }

    /**
     * 测试配置中心权限
     */
    private static void testConfigPermissions(ConfigService configService, String namespace,
                                            boolean shouldAllowRead, boolean shouldAllowWrite) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 测试配置中心权限 (命名空间: {})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");

        // 测试配置创建（写操作）
        String createdConfigId = testConfigCreate(configService, namespace, shouldAllowWrite);

        // 等待缓存同步
        waitForCacheSync(2000);

        // 测试配置读取（读操作）
        testConfigRead(configService, namespace, shouldAllowRead, createdConfigId);

        // 测试配置更新（写操作）
        if (createdConfigId != null && shouldAllowWrite) {
            testConfigUpdate(configService, namespace, createdConfigId, shouldAllowWrite);
            waitForCacheSync(1000);
        }

        // 测试配置删除（写操作）
        if (createdConfigId != null && shouldAllowWrite) {
            testConfigDelete(configService, namespace, createdConfigId, shouldAllowWrite);
            waitForCacheSync(1000);
        }

        // 测试配置列表查询（读操作）
        testConfigList(configService, namespace, shouldAllowRead);
    }

    /**
     * 测试配置创建
     */
    private static String testConfigCreate(ConfigService configService, String namespace, boolean shouldAllowWrite) {
        String dataId = generateUniqueConfigId();
        String content = "阶段2测试配置内容 - " + namespace + " - " + getCurrentTimestamp();

        logger.info("🔧 测试配置创建:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: {}", TEST_GROUP);
        logger.info("  - 内容长度: {} 字符", content.length());
        logger.info("  - 预期结果: {}", shouldAllowWrite ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            boolean result = configService.publishConfig(dataId, TEST_GROUP, content);

            logger.info("📊 配置创建响应:");
            logger.info("  - 返回值: {}", result);
            logger.info("  - 操作状态: {}", result ? "成功" : "失败");

            if (shouldAllowWrite) {
                if (result) {
                    logger.info("✅ 配置创建成功 - 符合预期（应该允许写操作）");
                    return dataId; // 返回创建成功的配置ID
                } else {
                    logger.error("❌ 配置创建失败 - 不符合预期（应该允许写操作但实际失败）");
                }
            } else {
                if (result) {
                    logger.error("❌ 配置创建成功 - 不符合预期（应该拒绝写操作但实际成功）");
                    logger.error("🚨 发现权限控制漏洞：配置中心写权限验证失效");
                    return dataId; // 即使不应该成功，也返回ID用于后续测试
                } else {
                    logger.info("✅ 配置创建失败 - 符合预期（应该拒绝写操作）");
                }
            }

        } catch (NacosException e) {
            logger.info("📊 配置创建异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowWrite) {
                logger.error("❌ 配置创建被拒绝 - 不符合预期（应该允许写操作）");
                logger.error("  - 可能的原因: 权限配置错误或系统异常");
            } else {
                logger.info("✅ 配置创建被拒绝 - 符合预期（应该拒绝写操作）");
                logger.info("  - 权限控制正常工作");
            }
        }

        logger.info("");
        return null; // 创建失败或被拒绝时返回null
    }

    /**
     * 测试配置读取
     */
    private static void testConfigRead(ConfigService configService, String namespace,
                                     boolean shouldAllowRead, String createdConfigId) {
        // 根据命名空间选择要读取的配置ID
        String dataId;
        String description;

        if (CF_NAMESPACE.equals(namespace) && createdConfigId != null) {
            // cf命名空间：读取刚创建的配置
            dataId = createdConfigId;
            description = "刚创建的配置";
        } else if (CF_N12S_NAMESPACE.equals(namespace)) {
            // cf-n12s命名空间：读取指定的已存在配置
            dataId = "update-test-config-1757916395291";
            description = "已存在的配置";
        } else if (CF_NS_NAMESPACE.equals(namespace)) {
            // cf-ns命名空间：读取指定的已存在配置
            dataId = "update-test-config-1757916373910";
            description = "已存在的配置";
        } else {
            // 默认情况
            dataId = "existing-config";
            description = "默认配置";
        }

        logger.info("🔍 测试配置读取:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 配置描述: {}", description);
        logger.info("  - 分组: {}", TEST_GROUP);
        logger.info("  - 预期结果: {}", shouldAllowRead ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            String content = configService.getConfig(dataId, TEST_GROUP, 3000L);

            logger.info("📊 配置读取响应:");
            logger.info("  - 返回内容: {}", content != null ? "有内容" : "null");
            if (content != null) {
                logger.info("  - 内容长度: {} 字符", content.length());
                logger.info("  - 内容预览: {}", content.length() > 100 ? content.substring(0, 100) + "..." : content);
            }

            if (shouldAllowRead) {
                if (content != null) {
                    logger.info("✅ 配置读取成功 - 符合预期（应该允许读操作）");
                } else {
                    logger.warn("⚠️ 配置读取返回null - 可能配置不存在或权限问题");
                }
            } else {
                if (content != null) {
                    logger.error("❌ 配置读取成功 - 不符合预期（应该拒绝读操作但实际成功）");
                    logger.error("🚨 发现权限控制漏洞：配置中心读权限验证失效");
                } else {
                    logger.info("✅ 配置读取失败 - 符合预期（应该拒绝读操作）");
                }
            }

        } catch (NacosException e) {
            logger.info("📊 配置读取异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowRead) {
                logger.error("❌ 配置读取被拒绝 - 不符合预期（应该允许读操作）");
            } else {
                logger.info("✅ 配置读取被拒绝 - 符合预期（应该拒绝读操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试配置列表查询
     */
    private static void testConfigList(ConfigService configService, String namespace, boolean shouldAllowRead) {
        logger.info("📋 测试配置列表查询:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 预期结果: {}", shouldAllowRead ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            // 注意：Nacos Java SDK的ConfigService没有直接的列表查询方法
            // 这里我们尝试读取一个不存在的配置来测试权限
            String content = configService.getConfig("non-existent-config", TEST_GROUP, 1000L);

            logger.info("📊 配置列表查询响应:");
            logger.info("  - 查询结果: {}", content != null ? "有结果" : "无结果");

            if (shouldAllowRead) {
                logger.info("✅ 配置查询操作完成 - 符合预期（应该允许读操作）");
            } else {
                logger.error("❌ 配置查询操作完成 - 不符合预期（应该拒绝读操作）");
                logger.error("🚨 发现权限控制漏洞：配置中心读权限验证失效");
            }

        } catch (NacosException e) {
            logger.info("📊 配置列表查询异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());

            if (shouldAllowRead) {
                logger.error("❌ 配置查询被拒绝 - 不符合预期（应该允许读操作）");
            } else {
                logger.info("✅ 配置查询被拒绝 - 符合预期（应该拒绝读操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试配置更新
     */
    private static void testConfigUpdate(ConfigService configService, String namespace,
                                       String dataId, boolean shouldAllowWrite) {
        String updatedContent = "更新后的配置内容 - " + namespace + " - " + getCurrentTimestamp();

        logger.info("🔄 测试配置更新:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: {}", TEST_GROUP);
        logger.info("  - 更新内容长度: {} 字符", updatedContent.length());
        logger.info("  - 预期结果: {}", shouldAllowWrite ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            boolean result = configService.publishConfig(dataId, TEST_GROUP, updatedContent);

            logger.info("📊 配置更新响应:");
            logger.info("  - 返回值: {}", result);
            logger.info("  - 操作状态: {}", result ? "成功" : "失败");

            if (shouldAllowWrite) {
                if (result) {
                    logger.info("✅ 配置更新成功 - 符合预期（应该允许写操作）");
                } else {
                    logger.error("❌ 配置更新失败 - 不符合预期（应该允许写操作但实际失败）");
                }
            } else {
                if (result) {
                    logger.error("❌ 配置更新成功 - 不符合预期（应该拒绝写操作但实际成功）");
                    logger.error("🚨 发现权限控制漏洞：配置更新写权限验证失效");
                } else {
                    logger.info("✅ 配置更新失败 - 符合预期（应该拒绝写操作）");
                }
            }

        } catch (NacosException e) {
            logger.info("📊 配置更新异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowWrite) {
                logger.error("❌ 配置更新被拒绝 - 不符合预期（应该允许写操作）");
            } else {
                logger.info("✅ 配置更新被拒绝 - 符合预期（应该拒绝写操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试配置删除
     */
    private static void testConfigDelete(ConfigService configService, String namespace,
                                       String dataId, boolean shouldAllowWrite) {
        logger.info("🗑️ 测试配置删除:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 配置ID: {}", dataId);
        logger.info("  - 分组: {}", TEST_GROUP);
        logger.info("  - 预期结果: {}", shouldAllowWrite ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            boolean result = configService.removeConfig(dataId, TEST_GROUP);

            logger.info("📊 配置删除响应:");
            logger.info("  - 返回值: {}", result);
            logger.info("  - 操作状态: {}", result ? "成功" : "失败");

            if (shouldAllowWrite) {
                if (result) {
                    logger.info("✅ 配置删除成功 - 符合预期（应该允许写操作）");
                } else {
                    logger.error("❌ 配置删除失败 - 不符合预期（应该允许写操作但实际失败）");
                }
            } else {
                if (result) {
                    logger.error("❌ 配置删除成功 - 不符合预期（应该拒绝写操作但实际成功）");
                    logger.error("🚨 发现权限控制漏洞：配置删除写权限验证失效");
                } else {
                    logger.info("✅ 配置删除失败 - 符合预期（应该拒绝写操作）");
                }
            }

        } catch (NacosException e) {
            logger.info("📊 配置删除异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowWrite) {
                logger.error("❌ 配置删除被拒绝 - 不符合预期（应该允许写操作）");
            } else {
                logger.info("✅ 配置删除被拒绝 - 符合预期（应该拒绝写操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试服务注册发现权限
     */
    private static void testServicePermissions(NamingService namingService, String namespace,
                                             boolean shouldAllowRead, boolean shouldAllowWrite) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🚀 测试服务注册发现权限 (命名空间: {})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");

        // 测试服务注册（写操作）
        String serviceName = testServiceRegister(namingService, namespace, shouldAllowWrite);

        // 等待缓存同步
        waitForCacheSync(3000);

        // 测试服务查询（读操作）
        if (serviceName != null) {
            testServiceQuery(namingService, namespace, serviceName, shouldAllowRead);
        }

        // 测试服务列表查询（读操作）
        testServiceList(namingService, namespace, shouldAllowRead);

        // 测试服务注销（写操作）
        if (serviceName != null && shouldAllowWrite) {
            testServiceDeregister(namingService, namespace, serviceName, shouldAllowWrite);
        }
    }

    /**
     * 测试服务注册
     */
    private static String testServiceRegister(NamingService namingService, String namespace, boolean shouldAllowWrite) {
        String serviceName = generateUniqueServiceName();

        logger.info("🔧 测试服务注册:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 服务名: {}", serviceName);
        logger.info("  - 实例IP: {}", TEST_IP);
        logger.info("  - 实例端口: {}", TEST_PORT);
        logger.info("  - 预期结果: {}", shouldAllowWrite ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            Instance instance = new Instance();
            instance.setIp(TEST_IP);
            instance.setPort(TEST_PORT);
            instance.setHealthy(true);
            instance.setWeight(1.0);

            namingService.registerInstance(serviceName, instance);

            logger.info("📊 服务注册响应:");
            logger.info("  - 注册状态: 成功");
            logger.info("  - 服务名: {}", serviceName);

            if (shouldAllowWrite) {
                logger.info("✅ 服务注册成功 - 符合预期（应该允许写操作）");
                return serviceName;
            } else {
                logger.error("❌ 服务注册成功 - 不符合预期（应该拒绝写操作但实际成功）");
                logger.error("🚨 发现权限控制漏洞：服务注册写权限验证失效");
                return serviceName;
            }

        } catch (NacosException e) {
            logger.info("📊 服务注册异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowWrite) {
                logger.error("❌ 服务注册被拒绝 - 不符合预期（应该允许写操作）");
            } else {
                logger.info("✅ 服务注册被拒绝 - 符合预期（应该拒绝写操作）");
            }

            return null;
        }
    }

    /**
     * 等待缓存同步
     */
    private static void waitForCacheSync(long milliseconds) {
        logger.info("⏳ 等待缓存同步 ({} 毫秒)...", milliseconds);
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 测试服务查询
     */
    private static void testServiceQuery(NamingService namingService, String namespace,
                                       String serviceName, boolean shouldAllowRead) {
        logger.info("🔍 测试服务查询:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 服务名: {}", serviceName);
        logger.info("  - 预期结果: {}", shouldAllowRead ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            List<Instance> instances = namingService.getAllInstances(serviceName);

            logger.info("📊 服务查询响应:");
            logger.info("  - 实例数量: {}", instances != null ? instances.size() : 0);
            if (instances != null && !instances.isEmpty()) {
                for (int i = 0; i < instances.size(); i++) {
                    Instance instance = instances.get(i);
                    logger.info("  - 实例{}: {}:{} (健康状态: {})",
                              i + 1, instance.getIp(), instance.getPort(), instance.isHealthy());
                }
            }

            if (shouldAllowRead) {
                logger.info("✅ 服务查询成功 - 符合预期（应该允许读操作）");
            } else {
                logger.error("❌ 服务查询成功 - 不符合预期（应该拒绝读操作但实际成功）");
                logger.error("🚨 发现权限控制漏洞：服务查询读权限验证失效");
            }

        } catch (NacosException e) {
            logger.info("📊 服务查询异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowRead) {
                logger.error("❌ 服务查询被拒绝 - 不符合预期（应该允许读操作）");
            } else {
                logger.info("✅ 服务查询被拒绝 - 符合预期（应该拒绝读操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试服务列表查询
     */
    private static void testServiceList(NamingService namingService, String namespace, boolean shouldAllowRead) {
        logger.info("📋 测试服务列表查询:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 预期结果: {}", shouldAllowRead ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);

            logger.info("📊 服务列表查询响应:");
            logger.info("  - 服务总数: {}", serviceList != null ? serviceList.getCount() : 0);
            if (serviceList != null && serviceList.getData() != null) {
                logger.info("  - 当前页服务数: {}", serviceList.getData().size());
                for (int i = 0; i < Math.min(5, serviceList.getData().size()); i++) {
                    logger.info("  - 服务{}: {}", i + 1, serviceList.getData().get(i));
                }
                if (serviceList.getData().size() > 5) {
                    logger.info("  - ... 还有 {} 个服务", serviceList.getData().size() - 5);
                }
            }

            if (shouldAllowRead) {
                logger.info("✅ 服务列表查询成功 - 符合预期（应该允许读操作）");
            } else {
                logger.error("❌ 服务列表查询成功 - 不符合预期（应该拒绝读操作但实际成功）");
                logger.error("🚨 发现权限控制漏洞：服务列表读权限验证失效");
            }

        } catch (NacosException e) {
            logger.info("📊 服务列表查询异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowRead) {
                logger.error("❌ 服务列表查询被拒绝 - 不符合预期（应该允许读操作）");
            } else {
                logger.info("✅ 服务列表查询被拒绝 - 符合预期（应该拒绝读操作）");
            }
        }

        logger.info("");
    }

    /**
     * 测试服务注销
     */
    private static void testServiceDeregister(NamingService namingService, String namespace,
                                            String serviceName, boolean shouldAllowWrite) {
        logger.info("🗑️ 测试服务注销:");
        logger.info("  - 命名空间: {}", namespace);
        logger.info("  - 服务名: {}", serviceName);
        logger.info("  - 实例IP: {}", TEST_IP);
        logger.info("  - 实例端口: {}", TEST_PORT);
        logger.info("  - 预期结果: {}", shouldAllowWrite ? "成功" : "权限拒绝");
        logger.info("  - 操作时间: {}", getCurrentTimestamp());

        try {
            namingService.deregisterInstance(serviceName, TEST_IP, TEST_PORT);

            logger.info("📊 服务注销响应:");
            logger.info("  - 注销状态: 成功");
            logger.info("  - 服务名: {}", serviceName);

            if (shouldAllowWrite) {
                logger.info("✅ 服务注销成功 - 符合预期（应该允许写操作）");
            } else {
                logger.error("❌ 服务注销成功 - 不符合预期（应该拒绝写操作但实际成功）");
                logger.error("🚨 发现权限控制漏洞：服务注销写权限验证失效");
            }

            // 等待注销生效
            waitForCacheSync(2000);

            // 验证注销是否生效
            try {
                List<Instance> instances = namingService.getAllInstances(serviceName);
                boolean hasInstance = instances != null && !instances.isEmpty();
                logger.info("📊 注销验证:");
                logger.info("  - 剩余实例数: {}", instances != null ? instances.size() : 0);
                logger.info("  - 注销是否生效: {}", hasInstance ? "否" : "是");
            } catch (Exception e) {
                logger.info("📊 注销验证查询失败: {}", e.getMessage());
            }

        } catch (NacosException e) {
            logger.info("📊 服务注销异常响应:");
            logger.info("  - 异常类型: {}", e.getClass().getSimpleName());
            logger.info("  - 错误码: {}", e.getErrCode());
            logger.info("  - 错误消息: {}", e.getErrMsg());
            logger.info("  - 完整异常: {}", e.getMessage());

            if (shouldAllowWrite) {
                logger.error("❌ 服务注销被拒绝 - 不符合预期（应该允许写操作）");
            } else {
                logger.info("✅ 服务注销被拒绝 - 符合预期（应该拒绝写操作）");
            }
        }

        logger.info("");
    }
}
