package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * Nacos 客户端工具类
 * 封装 Nacos Java SDK 的常用操作
 */
public class NacosClientUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(NacosClientUtil.class);
    
    /**
     * 创建 NamingService 实例
     */
    public static NamingService createNamingService(String namespace, String token) throws NacosException {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.SERVER_ADDR, TestConfig.SERVER_ADDR);
        properties.put(PropertyKeyConst.NAMESPACE, namespace);

        // 🔧 修复：根据Polaris官方文档，正确的认证方式
        // USERNAME: 可任意值
        // PASSWORD: 北极星用户/用户组的资源访问凭据 Token
        properties.put("username", "cf"); // 可任意值
        properties.put("password", token); // 使用北极星访问凭据Token

        // 📝 详细调试日志：记录所有Properties参数
        logger.info("=== Java SDK Properties 配置详情 ===");
        logger.info("SERVER_ADDR: {}", properties.getProperty(PropertyKeyConst.SERVER_ADDR));
        logger.info("NAMESPACE: {}", properties.getProperty(PropertyKeyConst.NAMESPACE));
        logger.info("USERNAME: {}", properties.getProperty("username"));
        logger.info("PASSWORD (token): {}...", token.substring(0, Math.min(30, token.length())));
        logger.info("PASSWORD 完整长度: {} 字符", token.length());
        logger.info("PASSWORD 是否为空: {}", token.isEmpty());
        logger.info("Properties 总数: {}", properties.size());

        // 📝 记录所有Properties内容
        logger.debug("完整Properties内容:");
        for (String key : properties.stringPropertyNames()) {
            String value = properties.getProperty(key);
            if ("password".equalsIgnoreCase(key) || key.contains("password") || key.contains("token")) {
                logger.debug("  {}: {}...", key, value.substring(0, Math.min(20, value.length())));
            } else {
                logger.debug("  {}: {}", key, value);
            }
        }

        // 📝 特别检查password属性
        String passwordValue = properties.getProperty("password");
        if (passwordValue != null) {
            logger.info("✅ password属性已设置: {}...", passwordValue.substring(0, Math.min(20, passwordValue.length())));
        } else {
            logger.error("❌ password属性未设置！这是问题的根源！");
        }
        logger.info("===============================");

        logger.info("创建 NamingService - 命名空间: {}, 用户: polaris-test-user, token: {}...",
                   namespace, token.substring(0, 20));

        // 📝 记录NacosFactory调用
        logger.info("🔄 调用 NacosFactory.createNamingService()...");
        NamingService namingService = NacosFactory.createNamingService(properties);
        logger.info("✅ NamingService 创建成功");

        return namingService;
    }
    
    /**
     * 注册服务实例
     */
    public static boolean registerInstance(NamingService namingService, String serviceName,
                                         String ip, int port, String groupName, String clusterName) {
        long operationStart = System.currentTimeMillis();
        logger.info("🔄 开始服务实例注册操作");

        try {
            // 构建实例对象
            logger.debug("构建服务实例对象...");
            Instance instance = new Instance();
            instance.setIp(ip);
            instance.setPort(port);
            instance.setHealthy(true);
            instance.setWeight(1.0);
            instance.setClusterName(clusterName);

            logger.info("📝 服务实例详细信息：");
            logger.info("  - 服务名称: {}", serviceName);
            logger.info("  - 服务分组: {}", groupName);
            logger.info("  - 实例IP: {}", ip);
            logger.info("  - 实例端口: {}", port);
            logger.info("  - 集群名称: {}", clusterName);
            logger.info("  - 健康状态: {}", instance.isHealthy());
            logger.info("  - 实例权重: {}", instance.getWeight());

            // 📝 记录认证信息传递状态
            logger.info("=== 服务注册前认证信息检查 ===");
            try {
                // 尝试通过反射获取NamingService内部的认证信息
                logger.info("NamingService 类型: {}", namingService.getClass().getName());
                logger.info("准备发送服务注册请求，检查认证状态...");
            } catch (Exception e) {
                logger.debug("无法获取NamingService内部信息: {}", e.getMessage());
            }
            logger.info("=====================================");

            logger.debug("向 Nacos 服务端发送注册请求...");
            logger.info("🌐 即将发送gRPC请求到服务端，期望携带认证信息");
            long requestStart = System.currentTimeMillis();

            namingService.registerInstance(serviceName, groupName, instance);

            long requestEnd = System.currentTimeMillis();
            logger.debug("服务端响应时间: {}ms", requestEnd - requestStart);

            // 等待注册完成
            logger.debug("等待注册操作完成...");
            Thread.sleep(1000);

            long operationEnd = System.currentTimeMillis();
            logger.info("✅ 服务实例注册成功，总耗时: {}ms", operationEnd - operationStart);
            return true;

        } catch (Exception e) {
            long operationEnd = System.currentTimeMillis();
            logger.error("❌ 服务实例注册失败，总耗时: {}ms", operationEnd - operationStart);
            logger.error("失败原因: {}", e.getMessage());
            logger.error("异常类型: {}", e.getClass().getSimpleName());

            // 分析具体的失败原因
            if (e.getMessage().contains("no permission") ||
                e.getMessage().contains("access is not approved")) {
                logger.warn("🔒 权限验证失败：用户无权限执行此操作");
            } else if (e.getMessage().contains("timeout")) {
                logger.warn("⏰ 网络超时：请检查网络连接和服务端状态");
            } else if (e.getMessage().contains("connection")) {
                logger.warn("🔌 连接失败：请检查服务端地址和端口");
            } else {
                logger.warn("❓ 未知错误：{}", e.getMessage());
            }

            logger.debug("详细异常信息：", e);
            return false;
        }
    }
    
    /**
     * 注销服务实例
     */
    public static boolean deregisterInstance(NamingService namingService, String serviceName,
                                           String ip, int port, String groupName, String clusterName) {
        long operationStart = System.currentTimeMillis();
        logger.info("🔄 开始服务实例注销操作");

        try {
            logger.info("📝 服务实例注销详细信息：");
            logger.info("  - 服务名称: {}", serviceName);
            logger.info("  - 服务分组: {}", groupName);
            logger.info("  - 实例IP: {}", ip);
            logger.info("  - 实例端口: {}", port);
            logger.info("  - 集群名称: {}", clusterName);

            logger.debug("向 Nacos 服务端发送注销请求...");
            long requestStart = System.currentTimeMillis();

            namingService.deregisterInstance(serviceName, groupName, ip, port, clusterName);

            long requestEnd = System.currentTimeMillis();
            logger.debug("服务端响应时间: {}ms", requestEnd - requestStart);

            // 等待注销完成
            logger.debug("等待注销操作完成...");
            Thread.sleep(1000);

            long operationEnd = System.currentTimeMillis();
            logger.info("✅ 服务实例注销成功，总耗时: {}ms", operationEnd - operationStart);
            return true;

        } catch (Exception e) {
            long operationEnd = System.currentTimeMillis();
            logger.error("❌ 服务实例注销失败，总耗时: {}ms", operationEnd - operationStart);
            logger.error("失败原因: {}", e.getMessage());
            logger.error("异常类型: {}", e.getClass().getSimpleName());

            // 分析具体的失败原因
            if (e.getMessage().contains("no permission") ||
                e.getMessage().contains("access is not approved")) {
                logger.warn("🔒 权限验证失败：用户无权限执行此操作");
                logger.info("这是权限控制系统的正常行为");
            } else if (e.getMessage().contains("timeout")) {
                logger.warn("⏰ 网络超时：请检查网络连接和服务端状态");
            } else if (e.getMessage().contains("connection")) {
                logger.warn("🔌 连接失败：请检查服务端地址和端口");
            } else if (e.getMessage().contains("not found") ||
                       e.getMessage().contains("does not exist")) {
                logger.warn("🔍 服务实例不存在：可能已被删除或从未注册");
            } else {
                logger.warn("❓ 未知错误：{}", e.getMessage());
            }

            logger.debug("详细异常信息：", e);
            return false;
        }
    }
    
    /**
     * 查询服务实例列表
     */
    public static List<Instance> getAllInstances(NamingService namingService, String serviceName, String groupName) {
        try {
            logger.info("查询服务实例 - 服务: {}, 分组: {}", serviceName, groupName);
            
            List<Instance> instances = namingService.getAllInstances(serviceName, groupName);
            
            logger.info("查询到 {} 个服务实例", instances.size());
            for (Instance instance : instances) {
                logger.info("实例: {}:{}, 健康状态: {}, 权重: {}, 集群: {}", 
                           instance.getIp(), instance.getPort(), instance.isHealthy(), 
                           instance.getWeight(), instance.getClusterName());
            }
            
            return instances;
            
        } catch (Exception e) {
            logger.error("查询服务实例失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 检查服务是否存在
     */
    public static boolean isServiceExists(NamingService namingService, String serviceName, String groupName) {
        try {
            List<Instance> instances = namingService.getAllInstances(serviceName, groupName);
            return instances != null && !instances.isEmpty();
        } catch (Exception e) {
            logger.debug("检查服务存在性时出现异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭 NamingService
     */
    public static void shutdown(NamingService namingService) {
        try {
            if (namingService != null) {
                namingService.shutDown();
                logger.info("NamingService 已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭 NamingService 失败: {}", e.getMessage(), e);
        }
    }
}
