package com.polaris.test;

/**
 * test-user用户的测试配置类
 * 用于客户端鉴权功能测试
 */
public class TestUserConfig {
    
    // 服务端配置
    public static final String SERVER_ADDR = "180.76.109.137:8848";
    
    // test-user 用户配置
    // 注意：根据Polaris官方文档，PASSWORD字段应该使用北极星用户/用户组的资源访问凭据Token
    // USERNAME字段可以是任意值，PASSWORD字段是实际的访问凭据Token
    public static final String TEST_USER_TOKEN = "c2hb2u8P4P6b3xfhJEPoLmdQkRrm/+xEqV+8HQ6WKGdaBLZpQOWZHyhv18gXXQHJqrHHL6r9XZferoQnUI8=";
    public static final String TEST_USER_NAME = "test-user";
    public static final String TEST_USER_ID = "41c8ec3a3ab640378fe9434bbdaf4198";

    // 测试命名空间配置（三种不同权限）
    public static final String WR_AUTH_NAMESPACE = "wr-auth";        // test-user有读写权限的命名空间
    public static final String READ_ONLY_NAMESPACE = "read-only";    // test-user有只读权限的命名空间
    public static final String NO_AUTH_NAMESPACE = "noauth";         // test-user无权限的命名空间
    
    // 测试服务配置
    public static final String TEST_SERVICE_PREFIX = "test-user-service";
    public static final String TEST_GROUP = "DEFAULT_GROUP";
    public static final String TEST_CLUSTER = "DEFAULT";
    
    // 测试实例配置
    public static final String TEST_IP = "*************";
    public static final int TEST_PORT_BASE = 10000;
    
    // 测试配置中心配置
    public static final String TEST_CONFIG_PREFIX = "test-user-config";
    public static final String TEST_CONFIG_GROUP = "DEFAULT_GROUP";
    
    // 测试超时配置
    public static final int OPERATION_TIMEOUT_MS = 5000;
    public static final int RETRY_COUNT = 3;
    public static final int RETRY_DELAY_MS = 1000;
    
    /**
     * 获取测试服务名
     */
    public static String getTestServiceName(String suffix) {
        return TEST_SERVICE_PREFIX + "-" + suffix + "-" + System.currentTimeMillis();
    }
    
    /**
     * 获取测试配置ID
     */
    public static String getTestConfigId(String suffix) {
        return TEST_CONFIG_PREFIX + "-" + suffix + "-" + System.currentTimeMillis();
    }
    
    /**
     * 获取测试端口
     */
    public static int getTestPort(int offset) {
        return TEST_PORT_BASE + offset;
    }
    
    /**
     * 获取所有测试命名空间
     */
    public static String[] getAllNamespaces() {
        return new String[]{WR_AUTH_NAMESPACE, READ_ONLY_NAMESPACE, NO_AUTH_NAMESPACE};
    }
    
    /**
     * 获取命名空间权限描述
     */
    public static String getNamespacePermission(String namespace) {
        switch (namespace) {
            case WR_AUTH_NAMESPACE:
                return "读写权限";
            case READ_ONLY_NAMESPACE:
                return "只读权限";
            case NO_AUTH_NAMESPACE:
                return "无权限";
            default:
                return "未知权限";
        }
    }
    
    /**
     * 判断命名空间是否应该有读权限
     */
    public static boolean shouldAllowRead(String namespace) {
        return WR_AUTH_NAMESPACE.equals(namespace) || READ_ONLY_NAMESPACE.equals(namespace);
    }
    
    /**
     * 判断命名空间是否应该有写权限
     */
    public static boolean shouldAllowWrite(String namespace) {
        return WR_AUTH_NAMESPACE.equals(namespace);
    }

    /**
     * 获取测试配置DataId
     */
    public static String getTestConfigDataId(String suffix) {
        return TEST_CONFIG_PREFIX + "-" + suffix + "-" + System.currentTimeMillis();
    }

    /**
     * 获取测试配置内容
     */
    public static String getTestConfigContent(String content, String namespace) {
        return String.format("# 测试配置\n# 命名空间: %s\n# 时间: %s\n\n%s",
                           namespace,
                           java.time.LocalDateTime.now().toString(),
                           content);
    }
    
    /**
     * 打印测试配置信息
     */
    public static void printConfig() {
        System.out.println("=== test-user 客户端鉴权测试配置 ===");
        System.out.println("服务端地址: " + SERVER_ADDR);
        System.out.println("测试用户: " + TEST_USER_NAME);
        System.out.println("用户ID: " + TEST_USER_ID);
        System.out.println("Token: " + TEST_USER_TOKEN.substring(0, 20) + "...");
        System.out.println("");
        System.out.println("测试命名空间:");
        System.out.println("- " + WR_AUTH_NAMESPACE + ": " + getNamespacePermission(WR_AUTH_NAMESPACE));
        System.out.println("- " + READ_ONLY_NAMESPACE + ": " + getNamespacePermission(READ_ONLY_NAMESPACE));
        System.out.println("- " + NO_AUTH_NAMESPACE + ": " + getNamespacePermission(NO_AUTH_NAMESPACE));
        System.out.println("");
        System.out.println("测试服务前缀: " + TEST_SERVICE_PREFIX);
        System.out.println("测试配置前缀: " + TEST_CONFIG_PREFIX);
        System.out.println("测试IP: " + TEST_IP);
        System.out.println("测试端口范围: " + TEST_PORT_BASE + "+");
        System.out.println("操作超时: " + OPERATION_TIMEOUT_MS + "ms");
        System.out.println("===============================");
    }
}
