package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 交互式注册中心测试 - 对比鉴权开启/关闭状态下的行为
 * Interactive Registry Test - Compare behavior with auth enabled/disabled
 */
public class InteractiveRegistryTest {
    
    private static final Logger logger = LoggerFactory.getLogger(InteractiveRegistryTest.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间和权限配置
    private static final Map<String, String> NAMESPACE_PERMISSIONS = new LinkedHashMap<>();
    static {
        NAMESPACE_PERMISSIONS.put("cf", "只读权限");
        NAMESPACE_PERMISSIONS.put("cf-ns", "读写权限");
        NAMESPACE_PERMISSIONS.put("cf-n12s", "无权限");
    }
    
    // 测试结果记录
    private static final List<TestResult> testResults = new ArrayList<>();
    private static String currentAuthState = "关闭";
    
    static class TestResult {
        String authState;
        String namespace;
        String testCase;
        boolean passed;
        String details;
        String errorMessage;
        long executionTime;
        
        TestResult(String authState, String namespace, String testCase, boolean passed, 
                  String details, String errorMessage, long executionTime) {
            this.authState = authState;
            this.namespace = namespace;
            this.testCase = testCase;
            this.passed = passed;
            this.details = details;
            this.errorMessage = errorMessage;
            this.executionTime = executionTime;
        }
    }
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              交互式注册中心测试程序                             ║");
        logger.info("║         Interactive Registry Test Program              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 对比鉴权开启/关闭状态下注册中心的行为差异                  ║");
        logger.info("║ 当前鉴权状态: {} 🔓", currentAuthState);
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 第一阶段：鉴权关闭状态测试
            logger.info("🔓 第一阶段：客户端鉴权关闭状态测试");
            logger.info("═══════════════════════════════════════════════════════════════");
            executeRegistryTests("关闭");
            
            // 等待用户确认切换鉴权状态
            waitForAuthStateChange();
            
            // 第二阶段：鉴权开启状态测试
            logger.info("🔒 第二阶段：客户端鉴权开启状态测试");
            logger.info("═══════════════════════════════════════════════════════════════");
            executeRegistryTests("开启");
            
            // 生成对比报告
            generateComparisonReport();
            
            // 输出测试总结
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("❌ 交互式测试执行失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 执行注册中心测试
     */
    private static void executeRegistryTests(String authState) {
        currentAuthState = authState;
        logger.info("🚀 开始执行注册中心测试 (鉴权状态: {})", authState);
        logger.info("");
        
        for (Map.Entry<String, String> entry : NAMESPACE_PERMISSIONS.entrySet()) {
            String namespace = entry.getKey();
            String expectedPermission = entry.getValue();
            
            logger.info("───────────────────────────────────────────────────────────────");
            logger.info("🔍 测试命名空间: {} (期望权限: {})", namespace, expectedPermission);
            logger.info("───────────────────────────────────────────────────────────────");
            
            // 基础服务操作测试
            testBasicServiceOperations(namespace);
            
            // 额外测试：批量操作验证
            testBatchServiceOperations(namespace);
            
            logger.info("");
        }
    }
    
    /**
     * 测试基础服务操作 - 按照标准化流程
     */
    private static void testBasicServiceOperations(String namespace) {
        logger.info("📋 基础服务操作测试 (标准化流程)");

        try {
            NamingService namingService = createNamingService(namespace);

            // 执行标准化的服务注册发现测试流程
            executeStandardRegistryTest(namingService, namespace);

        } catch (Exception e) {
            logger.error("❌ 基础服务操作测试失败: {}", e.getMessage());
            recordTestResult(namespace, "基础操作-连接创建", false, "", e.getMessage(), 0);
        }
    }

    /**
     * 执行标准化的注册中心测试流程
     */
    private static void executeStandardRegistryTest(NamingService namingService, String namespace) {
        String serviceName = "standard-test-service-" + System.currentTimeMillis();
        Instance testInstance = createTestInstance();

        logger.info("🚀 开始标准化注册中心测试流程");
        logger.info("   服务名: {}", serviceName);
        logger.info("   命名空间: {}", namespace);
        logger.info("   测试实例: IP={}, Port={}, Weight={}",
            testInstance.getIp(), testInstance.getPort(), testInstance.getWeight());
        logger.info("");

        // 步骤1: 服务注册
        boolean registrationSuccess = executeStep1_ServiceRegistration(namingService, serviceName, testInstance, namespace);

        // 步骤2: 等待生效
        executeStep2_WaitForEffect();

        // 步骤3: 获取服务列表
        List<Instance> allInstances = executeStep3_GetServiceList(namingService, serviceName, namespace);

        // 步骤4: 查询服务详情
        executeStep4_QueryServiceDetails(namingService, serviceName, namespace, allInstances);

        // 步骤5: 查询实例详情
        executeStep5_QueryInstanceDetails(allInstances, testInstance, namespace);

        // 步骤6: 服务注销
        boolean deregistrationSuccess = executeStep6_ServiceDeregistration(namingService, serviceName, testInstance, namespace);

        // 步骤7: 验证注销
        executeStep7_VerifyDeregistration(namingService, serviceName, namespace);

        // 记录整体测试结果
        boolean overallSuccess = registrationSuccess && (allInstances != null && !allInstances.isEmpty()) && deregistrationSuccess;
        String details = String.format("标准化测试完成: 注册=%s, 查询=%s, 注销=%s",
            registrationSuccess ? "成功" : "失败",
            (allInstances != null && !allInstances.isEmpty()) ? "成功" : "失败",
            deregistrationSuccess ? "成功" : "失败");

        recordTestResult(namespace, "标准化流程-完整测试", overallSuccess, details, "", 0);
    }

    /**
     * 创建测试实例
     */
    private static Instance createTestInstance() {
        Instance instance = new Instance();
        instance.setIp("**************");
        instance.setPort(8080);
        instance.setWeight(1.0);
        instance.setHealthy(true);
        instance.setClusterName("DEFAULT");

        Map<String, String> metadata = new HashMap<>();
        metadata.put("version", "1.0.0");
        metadata.put("env", "test");
        metadata.put("authState", currentAuthState);
        metadata.put("timestamp", String.valueOf(System.currentTimeMillis()));
        instance.setMetadata(metadata);

        return instance;
    }
    
    /**
     * 步骤1: 服务注册
     */
    private static boolean executeStep1_ServiceRegistration(NamingService namingService, String serviceName,
                                                          Instance instance, String namespace) {
        logger.info("🔍 步骤1: 服务注册");
        logger.info("   调用: namingService.registerInstance(\"{}\", instance)", serviceName);
        logger.info("   参数: serviceName={}, ip={}, port={}, weight={}, healthy={}, cluster={}",
            serviceName, instance.getIp(), instance.getPort(), instance.getWeight(),
            instance.isHealthy(), instance.getClusterName());
        logger.info("   元数据: {}", instance.getMetadata());

        long startTime = System.currentTimeMillis();

        try {
            namingService.registerInstance(serviceName, instance);
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info("   结果: 注册成功");
            logger.info("   响应时间: {}ms", executionTime);
            logger.info("");

            recordTestResult(namespace, "步骤1-服务注册", true,
                String.format("注册成功: %s (IP: %s:%d)", serviceName, instance.getIp(), instance.getPort()),
                "", executionTime);
            return true;

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());

            logger.error("   结果: 注册失败");
            logger.error("   异常信息: {}", errorMsg);
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤1-服务注册", false, "", errorMsg, executionTime);
            return false;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            logger.error("   结果: 注册失败");
            logger.error("   异常信息: {}", e.getMessage());
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤1-服务注册", false, "", e.getMessage(), executionTime);
            return false;
        }
    }

    /**
     * 步骤2: 等待生效
     */
    private static void executeStep2_WaitForEffect() {
        logger.info("🔍 步骤2: 等待生效 (3000ms)");

        try {
            Thread.sleep(3000);
            logger.info("   等待完成");
            logger.info("");
        } catch (InterruptedException e) {
            logger.warn("   等待被中断: {}", e.getMessage());
            logger.info("");
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 步骤3: 获取服务列表
     */
    private static List<Instance> executeStep3_GetServiceList(NamingService namingService, String serviceName, String namespace) {
        logger.info("🔍 步骤3: 获取服务列表");
        logger.info("   调用: namingService.getAllInstances(\"{}\")", serviceName);

        long startTime = System.currentTimeMillis();

        try {
            List<Instance> instances = namingService.getAllInstances(serviceName);
            long executionTime = System.currentTimeMillis() - startTime;

            if (instances != null) {
                logger.info("   结果: 找到 {} 个实例", instances.size());

                if (!instances.isEmpty()) {
                    logger.info("   实例详情:");
                    for (int i = 0; i < instances.size(); i++) {
                        Instance inst = instances.get(i);
                        logger.info("     [{}] Instance{{ip='{}', port={}, weight={}, healthy={}, cluster='{}', metadata={}}}",
                            i, inst.getIp(), inst.getPort(), inst.getWeight(), inst.isHealthy(),
                            inst.getClusterName(), inst.getMetadata());
                    }
                } else {
                    logger.info("   实例详情: 无实例");
                }

                logger.info("   响应时间: {}ms", executionTime);
                logger.info("");

                recordTestResult(namespace, "步骤3-获取服务列表", true,
                    String.format("找到 %d 个实例", instances.size()), "", executionTime);
                return instances;

            } else {
                logger.error("   结果: 返回null");
                logger.info("   响应时间: {}ms", executionTime);
                logger.info("");

                recordTestResult(namespace, "步骤3-获取服务列表", false, "", "返回null", executionTime);
                return null;
            }

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());

            logger.error("   结果: 查询失败");
            logger.error("   异常信息: {}", errorMsg);
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤3-获取服务列表", false, "", errorMsg, executionTime);
            return null;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            logger.error("   结果: 查询失败");
            logger.error("   异常信息: {}", e.getMessage());
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤3-获取服务列表", false, "", e.getMessage(), executionTime);
            return null;
        }
    }
    
    /**
     * 步骤4: 查询服务详情
     */
    private static void executeStep4_QueryServiceDetails(NamingService namingService, String serviceName,
                                                       String namespace, List<Instance> allInstances) {
        logger.info("🔍 步骤4: 查询服务详情");

        if (allInstances == null || allInstances.isEmpty()) {
            logger.info("   跳过: 无服务实例可查询");
            logger.info("");
            recordTestResult(namespace, "步骤4-查询服务详情", false, "", "无服务实例", 0);
            return;
        }

        // 查询健康实例
        logger.info("   调用: namingService.selectInstances(\"{}\", true)", serviceName);
        long startTime = System.currentTimeMillis();

        try {
            List<Instance> healthyInstances = namingService.selectInstances(serviceName, true);
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info("   结果: 找到 {} 个健康实例", healthyInstances != null ? healthyInstances.size() : 0);

            // 查询不健康实例
            logger.info("   调用: namingService.selectInstances(\"{}\", false)", serviceName);
            long startTime2 = System.currentTimeMillis();

            List<Instance> unhealthyInstances = namingService.selectInstances(serviceName, false);
            long executionTime2 = System.currentTimeMillis() - startTime2;

            logger.info("   结果: 找到 {} 个不健康实例", unhealthyInstances != null ? unhealthyInstances.size() : 0);

            // 对比分析
            int totalFromGetAll = allInstances.size();
            int healthyCount = healthyInstances != null ? healthyInstances.size() : 0;
            int unhealthyCount = unhealthyInstances != null ? unhealthyInstances.size() : 0;
            int totalFromSelect = healthyCount + unhealthyCount;

            logger.info("   对比分析:");
            logger.info("     getAllInstances: {} 个实例", totalFromGetAll);
            logger.info("     selectInstances(健康): {} 个实例", healthyCount);
            logger.info("     selectInstances(不健康): {} 个实例", unhealthyCount);
            logger.info("     selectInstances(总计): {} 个实例", totalFromSelect);
            logger.info("     数据一致性: {}", totalFromGetAll == totalFromSelect ? "一致" : "不一致");
            logger.info("   响应时间: 健康实例查询={}ms, 不健康实例查询={}ms", executionTime, executionTime2);
            logger.info("");

            boolean success = healthyInstances != null && totalFromGetAll == totalFromSelect;
            String details = String.format("健康实例: %d, 不健康实例: %d, 数据一致性: %s",
                healthyCount, unhealthyCount, totalFromGetAll == totalFromSelect ? "一致" : "不一致");

            recordTestResult(namespace, "步骤4-查询服务详情", success, details, "", executionTime + executionTime2);

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());

            logger.error("   结果: 查询失败");
            logger.error("   异常信息: {}", errorMsg);
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤4-查询服务详情", false, "", errorMsg, executionTime);

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            logger.error("   结果: 查询失败");
            logger.error("   异常信息: {}", e.getMessage());
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤4-查询服务详情", false, "", e.getMessage(), executionTime);
        }
    }

    /**
     * 步骤5: 查询实例详情
     */
    private static void executeStep5_QueryInstanceDetails(List<Instance> instances, Instance originalInstance, String namespace) {
        logger.info("🔍 步骤5: 查询实例详情");

        if (instances == null || instances.isEmpty()) {
            logger.info("   跳过: 无实例可查询");
            logger.info("");
            recordTestResult(namespace, "步骤5-查询实例详情", false, "", "无实例", 0);
            return;
        }

        logger.info("   遍历实例列表: {} 个实例", instances.size());

        boolean foundOriginal = false;
        int consistentInstances = 0;

        for (int i = 0; i < instances.size(); i++) {
            Instance instance = instances.get(i);

            logger.info("   实例[{}] 完整属性:", i);
            logger.info("     IP: {}", instance.getIp());
            logger.info("     端口: {}", instance.getPort());
            logger.info("     权重: {}", instance.getWeight());
            logger.info("     健康状态: {}", instance.isHealthy());
            logger.info("     集群名: {}", instance.getClusterName());
            logger.info("     实例ID: {}", instance.getInstanceId());
            logger.info("     服务名: {}", instance.getServiceName());
            logger.info("     元数据: {}", instance.getMetadata());

            // 验证是否与注册时一致
            boolean isOriginal = instance.getIp().equals(originalInstance.getIp()) &&
                               instance.getPort() == originalInstance.getPort();

            if (isOriginal) {
                foundOriginal = true;
                logger.info("     验证结果: ✅ 这是原始注册的实例");

                // 详细对比属性
                boolean ipMatch = instance.getIp().equals(originalInstance.getIp());
                boolean portMatch = instance.getPort() == originalInstance.getPort();
                boolean weightMatch = Math.abs(instance.getWeight() - originalInstance.getWeight()) < 0.01;
                boolean healthMatch = instance.isHealthy() == originalInstance.isHealthy();

                logger.info("     属性对比:");
                logger.info("       IP: {} ({})", ipMatch ? "一致" : "不一致", ipMatch ? "✅" : "❌");
                logger.info("       端口: {} ({})", portMatch ? "一致" : "不一致", portMatch ? "✅" : "❌");
                logger.info("       权重: {} ({})", weightMatch ? "一致" : "不一致", weightMatch ? "✅" : "❌");
                logger.info("       健康状态: {} ({})", healthMatch ? "一致" : "不一致", healthMatch ? "✅" : "❌");

                if (ipMatch && portMatch && weightMatch && healthMatch) {
                    consistentInstances++;
                }
            } else {
                logger.info("     验证结果: ℹ️ 其他实例");
            }

            logger.info("");
        }

        logger.info("   验证总结:");
        logger.info("     找到原始实例: {}", foundOriginal ? "是" : "否");
        logger.info("     属性一致实例: {} 个", consistentInstances);
        logger.info("");

        boolean success = foundOriginal && consistentInstances > 0;
        String details = String.format("找到原始实例: %s, 属性一致: %d个", foundOriginal ? "是" : "否", consistentInstances);

        recordTestResult(namespace, "步骤5-查询实例详情", success, details, "", 0);
    }
    
    /**
     * 创建NamingService
     */
    private static NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", namespace);
        
        return NacosFactory.createNamingService(properties);
    }
    
    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testCase, boolean passed, 
                                       String details, String errorMessage, long executionTime) {
        testResults.add(new TestResult(currentAuthState, namespace, testCase, passed, 
                                     details, errorMessage, executionTime));
    }
    
    /**
     * 步骤6: 服务注销
     */
    private static boolean executeStep6_ServiceDeregistration(NamingService namingService, String serviceName,
                                                            Instance instance, String namespace) {
        logger.info("🔍 步骤6: 服务注销");
        logger.info("   调用: namingService.deregisterInstance(\"{}\", instance)", serviceName);
        logger.info("   参数: serviceName={}, ip={}, port={}, cluster={}",
            serviceName, instance.getIp(), instance.getPort(), instance.getClusterName());

        long startTime = System.currentTimeMillis();

        try {
            namingService.deregisterInstance(serviceName, instance);
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info("   结果: 注销成功");
            logger.info("   响应时间: {}ms", executionTime);
            logger.info("");

            recordTestResult(namespace, "步骤6-服务注销", true,
                String.format("注销成功: %s (IP: %s:%d)", serviceName, instance.getIp(), instance.getPort()),
                "", executionTime);
            return true;

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());

            logger.error("   结果: 注销失败");
            logger.error("   异常信息: {}", errorMsg);
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤6-服务注销", false, "", errorMsg, executionTime);
            return false;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            logger.error("   结果: 注销失败");
            logger.error("   异常信息: {}", e.getMessage());
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤6-服务注销", false, "", e.getMessage(), executionTime);
            return false;
        }
    }

    /**
     * 步骤7: 验证注销
     */
    private static void executeStep7_VerifyDeregistration(NamingService namingService, String serviceName, String namespace) {
        logger.info("🔍 步骤7: 验证注销");
        logger.info("   等待注销生效 (3000ms)...");

        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        logger.info("   调用: namingService.getAllInstances(\"{}\")", serviceName);

        long startTime = System.currentTimeMillis();

        try {
            List<Instance> instances = namingService.getAllInstances(serviceName);
            long executionTime = System.currentTimeMillis() - startTime;

            if (instances != null) {
                logger.info("   结果: 找到 {} 个实例", instances.size());

                if (instances.isEmpty()) {
                    logger.info("   验证结果: ✅ 服务已完全注销");
                } else {
                    logger.info("   验证结果: ⚠️ 注销后仍有实例存在");
                    logger.info("   剩余实例详情:");
                    for (int i = 0; i < instances.size(); i++) {
                        Instance inst = instances.get(i);
                        logger.info("     [{}] Instance{{ip='{}', port={}, healthy={}}}",
                            i, inst.getIp(), inst.getPort(), inst.isHealthy());
                    }
                }

                logger.info("   响应时间: {}ms", executionTime);
                logger.info("");

                boolean success = instances.isEmpty();
                String details = String.format("注销验证: %s (%d个剩余实例)",
                    success ? "完全注销" : "部分注销", instances.size());

                recordTestResult(namespace, "步骤7-验证注销", success, details, "", executionTime);

            } else {
                logger.error("   结果: 返回null");
                logger.info("   响应时间: {}ms", executionTime);
                logger.info("");

                recordTestResult(namespace, "步骤7-验证注销", false, "", "返回null", executionTime);
            }

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());

            logger.error("   结果: 验证失败");
            logger.error("   异常信息: {}", errorMsg);
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤7-验证注销", false, "", errorMsg, executionTime);

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            logger.error("   结果: 验证失败");
            logger.error("   异常信息: {}", e.getMessage());
            logger.error("   响应时间: {}ms", executionTime);
            logger.error("   异常堆栈: ", e);
            logger.info("");

            recordTestResult(namespace, "步骤7-验证注销", false, "", e.getMessage(), executionTime);
        }
    }

    /**
     * 测试批量服务操作
     */
    private static void testBatchServiceOperations(String namespace) {
        logger.info("📋 批量服务操作测试");

        try {
            NamingService namingService = createNamingService(namespace);

            String servicePrefix = "batch-test-" + System.currentTimeMillis();
            int serviceCount = 3;

            logger.info("🔍 批量注册 {} 个服务", serviceCount);

            // 批量注册
            List<String> serviceNames = new ArrayList<>();
            for (int i = 0; i < serviceCount; i++) {
                String serviceName = servicePrefix + "-" + i;
                serviceNames.add(serviceName);

                Instance instance = new Instance();
                instance.setIp("192.168.100." + (50 + i));
                instance.setPort(8200 + i);
                instance.setWeight(1.0 + i * 0.5);
                instance.setMetadata(Map.of("batch", "true", "index", String.valueOf(i)));

                logger.info("   注册服务[{}]: {} (IP: {}:{})", i, serviceName, instance.getIp(), instance.getPort());

                try {
                    namingService.registerInstance(serviceName, instance);
                    logger.info("     ✅ 注册成功");
                } catch (Exception e) {
                    logger.error("     ❌ 注册失败: {}", e.getMessage());
                }
            }

            // 等待生效
            logger.info("   等待批量注册生效 (3000ms)...");
            Thread.sleep(3000);

            // 验证批量注册
            logger.info("🔍 验证批量注册结果");
            int successCount = 0;
            for (int i = 0; i < serviceCount; i++) {
                String serviceName = serviceNames.get(i);
                try {
                    List<Instance> instances = namingService.getAllInstances(serviceName);
                    if (instances != null && !instances.isEmpty()) {
                        successCount++;
                        logger.info("   服务[{}]: {} - ✅ 找到 {} 个实例", i, serviceName, instances.size());
                    } else {
                        logger.info("   服务[{}]: {} - ❌ 未找到实例", i, serviceName);
                    }
                } catch (Exception e) {
                    logger.error("   服务[{}]: {} - ❌ 查询异常: {}", i, serviceName, e.getMessage());
                }
            }

            logger.info("🔍 批量操作结果: {}/{} 个服务成功", successCount, serviceCount);

            boolean success = successCount == serviceCount;
            String details = String.format("批量操作: %d/%d 个服务成功", successCount, serviceCount);
            recordTestResult(namespace, "批量操作-服务注册", success, details, "", 0);

        } catch (Exception e) {
            logger.error("❌ 批量服务操作测试失败: {}", e.getMessage());
            recordTestResult(namespace, "批量操作-连接创建", false, "", e.getMessage(), 0);
        }
    }





    /**
     * 测试批量服务注册
     */
    private static void testBatchServiceRegistration(NamingService namingService, String namespace) {
        logger.info("  🔍 测试: 批量服务注册");
        long startTime = System.currentTimeMillis();

        try {
            String servicePrefix = "batch-service-" + System.currentTimeMillis();
            int serviceCount = 3;

            // 批量注册服务
            for (int i = 0; i < serviceCount; i++) {
                String serviceName = servicePrefix + "-" + i;
                Instance instance = new Instance();
                instance.setIp("192.168.100." + (40 + i));
                instance.setPort(8110 + i);
                instance.setMetadata(Map.of("batch", "true", "index", String.valueOf(i)));

                namingService.registerInstance(serviceName, instance);
            }

            Thread.sleep(3000); // 等待批量注册生效

            // 验证批量注册
            int successCount = 0;
            for (int i = 0; i < serviceCount; i++) {
                String serviceName = servicePrefix + "-" + i;
                List<Instance> instances = namingService.getAllInstances(serviceName);
                if (instances != null && !instances.isEmpty()) {
                    successCount++;
                }
            }

            long executionTime = System.currentTimeMillis() - startTime;

            if (successCount == serviceCount) {
                String details = String.format("批量服务注册成功: %d/%d 个服务", successCount, serviceCount);
                logger.info("    ✅ 批量服务注册成功: {}/{} 个服务 ({}ms)", successCount, serviceCount, executionTime);
                recordTestResult(namespace, "批量操作-服务注册", true, details, "", executionTime);
            } else {
                logger.error("    ❌ 批量服务注册失败: {}/{} 个服务成功", successCount, serviceCount);
                recordTestResult(namespace, "批量操作-服务注册", false, "",
                    String.format("%d/%d个服务注册失败", serviceCount - successCount, serviceCount), executionTime);
            }

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());
            logger.error("    ❌ 批量服务注册异常: {}", errorMsg);
            recordTestResult(namespace, "批量操作-服务注册", false, "", errorMsg, executionTime);
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("    ❌ 批量服务注册异常: {}", e.getMessage());
            recordTestResult(namespace, "批量操作-服务注册", false, "", e.getMessage(), executionTime);
        }
    }

    /**
     * 测试并发操作
     */
    private static void testConcurrentOperations(String namespace) {
        logger.info("📋 并发操作测试");

        try {
            NamingService namingService = createNamingService(namespace);

            // 测试并发服务注册
            testConcurrentServiceRegistration(namingService, namespace);

        } catch (Exception e) {
            logger.error("❌ 并发操作测试失败: {}", e.getMessage());
            recordTestResult(namespace, "并发操作-连接创建", false, "", e.getMessage(), 0);
        }
    }

    /**
     * 测试并发服务注册
     */
    private static void testConcurrentServiceRegistration(NamingService namingService, String namespace) {
        logger.info("  🔍 测试: 并发服务注册");
        long startTime = System.currentTimeMillis();

        try {
            String serviceName = "concurrent-service-" + System.currentTimeMillis();
            int threadCount = 3;
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            CountDownLatch latch = new CountDownLatch(threadCount);
            List<Boolean> results = Collections.synchronizedList(new ArrayList<>());

            // 并发注册服务实例
            for (int i = 0; i < threadCount; i++) {
                final int threadIndex = i;
                executor.submit(() -> {
                    try {
                        Instance instance = new Instance();
                        instance.setIp("192.168.100." + (50 + threadIndex));
                        instance.setPort(8120 + threadIndex);
                        instance.setMetadata(Map.of("thread", String.valueOf(threadIndex)));

                        namingService.registerInstance(serviceName, instance);
                        results.add(true);

                    } catch (Exception e) {
                        logger.warn("并发注册线程{}异常: {}", threadIndex, e.getMessage());
                        results.add(false);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有线程完成
            latch.await();
            executor.shutdown();

            Thread.sleep(2000); // 等待注册生效

            // 验证并发注册结果
            List<Instance> instances = namingService.getAllInstances(serviceName);
            long successCount = results.stream().mapToLong(r -> r ? 1 : 0).sum();

            long executionTime = System.currentTimeMillis() - startTime;

            if (instances != null && instances.size() >= successCount && successCount > 0) {
                String details = String.format("并发服务注册成功: %d 个线程, %d 个实例",
                    threadCount, instances.size());
                logger.info("    ✅ 并发服务注册成功: {} 个实例 ({}ms)", instances.size(), executionTime);
                recordTestResult(namespace, "并发操作-服务注册", true, details, "", executionTime);
            } else {
                logger.error("    ❌ 并发服务注册失败: 期望 {} 个，实际 {} 个", successCount,
                    instances != null ? instances.size() : 0);
                recordTestResult(namespace, "并发操作-服务注册", false, "",
                    String.format("期望%d个实例，实际%d个", successCount, instances != null ? instances.size() : 0),
                    executionTime);
            }

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("    ❌ 并发服务注册异常: {}", e.getMessage());
            recordTestResult(namespace, "并发操作-服务注册", false, "", e.getMessage(), executionTime);
        }
    }

    /**
     * 测试异常场景
     */
    private static void testExceptionScenarios(String namespace) {
        logger.info("📋 异常场景测试");

        try {
            NamingService namingService = createNamingService(namespace);

            // 测试无效服务名处理
            testInvalidServiceNameHandling(namingService, namespace);

        } catch (Exception e) {
            logger.error("❌ 异常场景测试失败: {}", e.getMessage());
            recordTestResult(namespace, "异常场景-连接创建", false, "", e.getMessage(), 0);
        }
    }

    /**
     * 测试无效服务名处理
     */
    private static void testInvalidServiceNameHandling(NamingService namingService, String namespace) {
        logger.info("  🔍 测试: 无效服务名处理");
        long startTime = System.currentTimeMillis();

        try {
            // 测试查询不存在的服务
            String nonExistentService = "non-existent-service-" + System.currentTimeMillis();
            List<Instance> instances = namingService.getAllInstances(nonExistentService);

            long executionTime = System.currentTimeMillis() - startTime;

            // 查询不存在的服务应该返回空列表，而不是异常
            if (instances != null) {
                String details = String.format("无效服务名处理正确: %s (返回 %d 个实例)",
                    nonExistentService, instances.size());
                logger.info("    ✅ 无效服务名处理正确: 返回 {} 个实例 ({}ms)", instances.size(), executionTime);
                recordTestResult(namespace, "异常场景-无效服务名", true, details, "", executionTime);
            } else {
                logger.error("    ❌ 无效服务名处理异常: 返回null");
                recordTestResult(namespace, "异常场景-无效服务名", false, "", "返回null", executionTime);
            }

        } catch (NacosException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            // 如果是权限异常，这可能是预期的
            if (e.getErrCode() == 401001) {
                String details = String.format("权限验证正确: 无权限访问 (错误码: %d)", e.getErrCode());
                logger.info("    ✅ 权限验证正确: 无权限访问 ({}ms)", executionTime);
                recordTestResult(namespace, "异常场景-无效服务名", true, details, "", executionTime);
            } else {
                String errorMsg = String.format("Nacos异常: %d - %s", e.getErrCode(), e.getErrMsg());
                logger.error("    ❌ 无效服务名处理异常: {}", errorMsg);
                recordTestResult(namespace, "异常场景-无效服务名", false, "", errorMsg, executionTime);
            }
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("    ❌ 无效服务名处理异常: {}", e.getMessage());
            recordTestResult(namespace, "异常场景-无效服务名", false, "", e.getMessage(), executionTime);
        }
    }

    /**
     * 生成对比报告
     */
    private static void generateComparisonReport() {
        logger.info("");
        logger.info("📊 生成测试对比报告...");

        try {
            String reportFileName = "注册中心鉴权对比测试报告.md";
            FileWriter writer = new FileWriter(reportFileName);

            writer.write("# 注册中心鉴权对比测试报告\n\n");
            writer.write("## 测试环境配置\n\n");
            writer.write("- **服务器地址**: " + SERVER_ADDR + "\n");
            writer.write("- **测试用户**: " + USERNAME + "\n");
            writer.write("- **测试时间**: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("- **Java SDK版本**: Nacos Java Client\n\n");

            // 统计数据
            Map<String, Map<String, Integer>> stats = calculateStatistics();

            writer.write("## 测试执行摘要\n\n");
            for (String authState : Arrays.asList("关闭", "开启")) {
                Map<String, Integer> authStats = stats.get(authState);
                if (authStats != null) {
                    int total = authStats.get("total");
                    int passed = authStats.get("passed");
                    double passRate = total > 0 ? (passed * 100.0 / total) : 0;

                    writer.write(String.format("### 鉴权%s状态\n", authState));
                    writer.write(String.format("- **总测试数**: %d\n", total));
                    writer.write(String.format("- **通过测试**: %d (%.1f%%)\n", passed, passRate));
                    writer.write(String.format("- **失败测试**: %d (%.1f%%)\n\n", total - passed, 100 - passRate));
                }
            }

            // 详细结果
            writer.write("## 详细测试结果对比\n\n");

            for (String namespace : NAMESPACE_PERMISSIONS.keySet()) {
                writer.write(String.format("### %s 命名空间 (期望权限: %s)\n\n",
                    namespace, NAMESPACE_PERMISSIONS.get(namespace)));

                writer.write("| 测试用例 | 鉴权关闭 | 鉴权开启 | 对比结果 | 说明 |\n");
                writer.write("|---------|---------|---------|---------|-----|\n");

                // 获取该命名空间的所有测试用例
                Set<String> testCases = testResults.stream()
                    .filter(r -> r.namespace.equals(namespace))
                    .map(r -> r.testCase)
                    .collect(LinkedHashSet::new, Set::add, Set::addAll);

                for (String testCase : testCases) {
                    TestResult disabledResult = findTestResult("关闭", namespace, testCase);
                    TestResult enabledResult = findTestResult("开启", namespace, testCase);

                    String disabledStatus = disabledResult != null ? (disabledResult.passed ? "✅ PASS" : "❌ FAIL") : "⚪ N/A";
                    String enabledStatus = enabledResult != null ? (enabledResult.passed ? "✅ PASS" : "❌ FAIL") : "⚪ N/A";

                    String comparison = getComparisonResult(disabledResult, enabledResult, namespace);
                    String explanation = getTestExplanation(disabledResult, enabledResult, namespace, testCase);

                    writer.write(String.format("| %s | %s | %s | %s | %s |\n",
                        testCase, disabledStatus, enabledStatus, comparison, explanation));
                }

                writer.write("\n");
            }

            // 问题分析
            writer.write("## 问题分析\n\n");

            List<TestResult> failedTests = testResults.stream()
                .filter(r -> !r.passed)
                .collect(ArrayList::new, List::add, List::addAll);

            if (!failedTests.isEmpty()) {
                writer.write("### 失败测试分析\n\n");
                for (TestResult result : failedTests) {
                    writer.write(String.format("**%s - %s - %s**\n",
                        result.authState, result.namespace, result.testCase));
                    writer.write(String.format("- 错误信息: %s\n", result.errorMessage));
                    writer.write(String.format("- 执行时间: %dms\n\n", result.executionTime));
                }
            }

            // 结论
            writer.write("## 测试结论\n\n");
            writer.write("### 鉴权功能验证\n\n");

            // 分析权限行为是否符合预期
            for (String namespace : NAMESPACE_PERMISSIONS.keySet()) {
                String expectedPermission = NAMESPACE_PERMISSIONS.get(namespace);
                writer.write(String.format("**%s 命名空间** (期望权限: %s):\n", namespace, expectedPermission));

                // 分析鉴权关闭状态
                long disabledPassed = testResults.stream()
                    .filter(r -> "关闭".equals(r.authState) && namespace.equals(r.namespace) && r.passed)
                    .count();
                long disabledTotal = testResults.stream()
                    .filter(r -> "关闭".equals(r.authState) && namespace.equals(r.namespace))
                    .count();

                // 分析鉴权开启状态
                long enabledPassed = testResults.stream()
                    .filter(r -> "开启".equals(r.authState) && namespace.equals(r.namespace) && r.passed)
                    .count();
                long enabledTotal = testResults.stream()
                    .filter(r -> "开启".equals(r.authState) && namespace.equals(r.namespace))
                    .count();

                writer.write(String.format("- 鉴权关闭: %d/%d 通过 (%.1f%%)\n",
                    disabledPassed, disabledTotal, disabledTotal > 0 ? disabledPassed * 100.0 / disabledTotal : 0));
                writer.write(String.format("- 鉴权开启: %d/%d 通过 (%.1f%%)\n\n",
                    enabledPassed, enabledTotal, enabledTotal > 0 ? enabledPassed * 100.0 / enabledTotal : 0));
            }

            writer.write("---\n\n");
            writer.write("*报告生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "*\n");
            writer.write("*测试工具: 交互式注册中心测试程序*\n");

            writer.close();

            logger.info("✅ 测试报告已生成: {}", reportFileName);

        } catch (IOException e) {
            logger.error("❌ 生成测试报告失败", e);
        }
    }

    /**
     * 等待用户确认切换鉴权状态
     */
    private static void waitForAuthStateChange() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    鉴权状态切换提示                             ║");
        logger.info("║                Auth State Change Prompt               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 🔓 第一阶段测试完成 (鉴权关闭状态)                             ║");
        logger.info("║                                                            ║");
        logger.info("║ 📋 请按照以下步骤操作:                                        ║");
        logger.info("║ 1. 打开Nacos服务端的客户端鉴权功能                             ║");
        logger.info("║ 2. 确保鉴权配置生效                                           ║");
        logger.info("║ 3. 在聊天中回复 '已开启鉴权' 继续测试                          ║");
        logger.info("║                                                            ║");
        logger.info("║ ⚠️  重要: 请确保鉴权功能已完全开启后再继续                      ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");

        // 程序暂停，等待用户手动操作
        logger.info("⏸️  程序已暂停，等待用户确认鉴权状态切换...");
        logger.info("💡 提示: 请在聊天中回复确认信息以继续测试");
    }

    /**
     * 计算统计数据
     */
    private static Map<String, Map<String, Integer>> calculateStatistics() {
        Map<String, Map<String, Integer>> stats = new HashMap<>();

        for (String authState : Arrays.asList("关闭", "开启")) {
            Map<String, Integer> authStats = new HashMap<>();

            long total = testResults.stream()
                .filter(r -> authState.equals(r.authState))
                .count();

            long passed = testResults.stream()
                .filter(r -> authState.equals(r.authState) && r.passed)
                .count();

            authStats.put("total", (int) total);
            authStats.put("passed", (int) passed);
            stats.put(authState, authStats);
        }

        return stats;
    }

    /**
     * 查找测试结果
     */
    private static TestResult findTestResult(String authState, String namespace, String testCase) {
        return testResults.stream()
            .filter(r -> authState.equals(r.authState) && namespace.equals(r.namespace) && testCase.equals(r.testCase))
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取对比结果
     */
    private static String getComparisonResult(TestResult disabledResult, TestResult enabledResult, String namespace) {
        if (disabledResult == null && enabledResult == null) {
            return "⚪ 无数据";
        }

        if (disabledResult == null || enabledResult == null) {
            return "⚠️ 数据不完整";
        }

        String expectedPermission = NAMESPACE_PERMISSIONS.get(namespace);

        // 鉴权关闭时，所有操作都应该成功
        // 鉴权开启时，根据权限配置判断
        if ("关闭".equals(disabledResult.authState)) {
            if (disabledResult.passed && shouldPassWhenEnabled(enabledResult, expectedPermission)) {
                return "✅ 符合预期";
            } else if (disabledResult.passed && !enabledResult.passed) {
                return "🔒 权限生效";
            } else if (!disabledResult.passed && !enabledResult.passed) {
                return "⚠️ 都失败";
            } else {
                return "❌ 异常";
            }
        }

        return "🔍 需分析";
    }

    /**
     * 判断开启鉴权时是否应该通过
     */
    private static boolean shouldPassWhenEnabled(TestResult enabledResult, String expectedPermission) {
        if (enabledResult == null) return false;

        // 根据权限配置判断
        switch (expectedPermission) {
            case "读写权限":
                return enabledResult.passed; // 读写权限应该都能通过
            case "只读权限":
                // 只读权限：查询类操作应该通过，写操作应该失败
                return enabledResult.testCase.contains("查询") || enabledResult.testCase.contains("监听");
            case "无权限":
                return !enabledResult.passed; // 无权限时所有操作都应该失败
            default:
                return false;
        }
    }

    /**
     * 获取测试说明
     */
    private static String getTestExplanation(TestResult disabledResult, TestResult enabledResult,
                                           String namespace, String testCase) {
        String expectedPermission = NAMESPACE_PERMISSIONS.get(namespace);

        if (disabledResult != null && enabledResult != null) {
            if (disabledResult.passed && !enabledResult.passed) {
                return "鉴权生效，权限受限";
            } else if (disabledResult.passed && enabledResult.passed) {
                return "有权限执行操作";
            } else if (!disabledResult.passed && !enabledResult.passed) {
                return "可能是系统问题";
            } else {
                return "异常情况";
            }
        }

        return "数据不完整";
    }

    /**
     * 输出测试总结
     */
    private static void printTestSummary() {
        logger.info("");
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    测试执行完成                               ║");
        logger.info("║                Test Execution Complete                ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");

        Map<String, Map<String, Integer>> stats = calculateStatistics();

        for (String authState : Arrays.asList("关闭", "开启")) {
            Map<String, Integer> authStats = stats.get(authState);
            if (authStats != null) {
                int total = authStats.get("total");
                int passed = authStats.get("passed");
                double passRate = total > 0 ? (passed * 100.0 / total) : 0;

                logger.info("");
                logger.info("🔍 鉴权{} - 测试结果:", authState);
                logger.info("   总测试数: {}", total);
                logger.info("   通过测试: {} ({:.1f}%)", passed, passRate);
                logger.info("   失败测试: {} ({:.1f}%)", total - passed, 100 - passRate);
            }
        }

        logger.info("");
        logger.info("📊 详细测试报告已生成: 注册中心鉴权对比测试报告.md");
        logger.info("🎉 交互式注册中心测试完成！");
    }
}
