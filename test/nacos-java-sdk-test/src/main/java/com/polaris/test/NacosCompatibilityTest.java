package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Nacos兼容性测试 - 客户端鉴权关闭状态验证
 * 全面验证鉴权关闭后所有用户对所有命名空间的完全读写权限
 */
public class NacosCompatibilityTest {
    
    private static final Logger logger = LoggerFactory.getLogger(NacosCompatibilityTest.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间
    private static final String[] TEST_NAMESPACES = {"cf", "cf-ns", "cf-n12s"};
    
    // 测试统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    private static final List<TestResult> testResults = new ArrayList<>();
    private static final StringBuilder reportContent = new StringBuilder();
    
    // 测试结果类
    static class TestResult {
        String namespace;
        String testType;
        String testCase;
        boolean passed;
        String details;
        String errorMessage;
        
        TestResult(String namespace, String testType, String testCase, boolean passed, String details, String errorMessage) {
            this.namespace = namespace;
            this.testType = testType;
            this.testCase = testCase;
            this.passed = passed;
            this.details = details;
            this.errorMessage = errorMessage;
        }
    }
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║           Nacos兼容性测试 - 客户端鉴权关闭验证                  ║");
        logger.info("║        Nacos Compatibility Test - Auth Disabled          ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试目标: 验证鉴权关闭后所有用户对所有命名空间的完全读写权限        ║");
        logger.info("║ 测试时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        // 初始化报告
        initializeReport();
        
        try {
            // 执行所有测试
            for (String namespace : TEST_NAMESPACES) {
                logger.info("═══════════════════════════════════════════════════════════════");
                logger.info("🔍 开始测试命名空间: {} (鉴权已关闭)", namespace);
                logger.info("═══════════════════════════════════════════════════════════════");
                
                // 服务注册发现测试
                testServiceRegistryAndDiscovery(namespace);
                
                // 配置中心测试
                testConfigurationManagement(namespace);
                
                // 边界条件测试
                testBoundaryConditions(namespace);
                
                logger.info("");
            }
            
            // 生成测试报告
            generateTestReport();
            
            // 输出测试摘要
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("❌ 兼容性测试执行失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 初始化测试报告
     */
    private static void initializeReport() {
        reportContent.append("# Nacos兼容Polaris客户端鉴权（关闭）测试报告\n\n");
        reportContent.append("## 测试环境配置\n\n");
        reportContent.append("- **服务器地址**: ").append(SERVER_ADDR).append("\n");
        reportContent.append("- **测试用户**: ").append(USERNAME).append("\n");
        reportContent.append("- **鉴权状态**: 已关闭 🔓\n");
        reportContent.append("- **测试命名空间**: ").append(String.join(", ", TEST_NAMESPACES)).append("\n");
        reportContent.append("- **测试时间**: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        reportContent.append("- **Java SDK版本**: Nacos Java Client\n\n");
    }
    
    /**
     * 测试服务注册发现功能
     */
    private static void testServiceRegistryAndDiscovery(String namespace) {
        logger.info("🚀 开始服务注册发现测试...");
        logger.info("───────────────────────────────────────────────────────────────");
        
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            NamingService namingService = NacosFactory.createNamingService(properties);
            logger.info("✅ NamingService创建成功");
            
            // 测试1: 服务注册
            testServiceRegistration(namingService, namespace);
            
            // 测试2: 服务查询
            testServiceQuery(namingService, namespace);
            
            // 测试3: 服务注销
            testServiceDeregistration(namingService, namespace);
            
            // 测试4: 批量服务操作
            testBatchServiceOperations(namingService, namespace);
            
        } catch (Exception e) {
            logger.error("❌ 服务注册发现测试失败: {}", e.getMessage());
            recordTestResult(namespace, "服务注册发现", "连接创建", false, "", e.getMessage());
        }
    }
    
    /**
     * 测试服务注册
     */
    private static void testServiceRegistration(NamingService namingService, String namespace) {
        logger.info("🔍 测试: 服务注册 ({})", namespace);
        
        try {
            String serviceName = "compatibility-test-service-" + System.currentTimeMillis();
            Instance instance = new Instance();
            instance.setIp("*************");
            instance.setPort(8080);
            instance.setWeight(1.0);
            instance.setHealthy(true);
            instance.setMetadata(Map.of("version", "1.0.0", "env", "test"));
            
            namingService.registerInstance(serviceName, instance);
            
            // 验证注册成功
            Thread.sleep(1000); // 等待注册生效
            List<Instance> instances = namingService.getAllInstances(serviceName);
            
            if (instances != null && !instances.isEmpty()) {
                logger.info("✅ {} - 服务注册: 成功注册服务 {} ({}个实例)", namespace, serviceName, instances.size());
                recordTestResult(namespace, "服务注册发现", "服务注册", true, 
                    String.format("成功注册服务: %s (%d个实例)", serviceName, instances.size()), "");
            } else {
                logger.error("❌ {} - 服务注册: 注册后无法查询到实例", namespace);
                recordTestResult(namespace, "服务注册发现", "服务注册", false, "", "注册后无法查询到实例");
            }
            
        } catch (Exception e) {
            logger.error("❌ {} - 服务注册: 注册失败 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "服务注册发现", "服务注册", false, "", e.getMessage());
        }
    }
    
    /**
     * 测试服务查询
     */
    private static void testServiceQuery(NamingService namingService, String namespace) {
        logger.info("🔍 测试: 服务查询 ({})", namespace);
        
        try {
            // 先注册一个测试服务
            String serviceName = "query-test-service-" + System.currentTimeMillis();
            Instance instance = new Instance();
            instance.setIp("*************");
            instance.setPort(8081);
            
            namingService.registerInstance(serviceName, instance);
            Thread.sleep(1000);
            
            // 查询服务
            List<Instance> instances = namingService.getAllInstances(serviceName);
            
            if (instances != null && !instances.isEmpty()) {
                logger.info("✅ {} - 服务查询: 成功查询到 {} 个实例", namespace, instances.size());
                recordTestResult(namespace, "服务注册发现", "服务查询", true, 
                    String.format("成功查询到 %d 个实例", instances.size()), "");
            } else {
                logger.error("❌ {} - 服务查询: 查询失败或无实例", namespace);
                recordTestResult(namespace, "服务注册发现", "服务查询", false, "", "查询失败或无实例");
            }
            
        } catch (Exception e) {
            logger.error("❌ {} - 服务查询: 查询异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "服务注册发现", "服务查询", false, "", e.getMessage());
        }
    }
    
    /**
     * 测试服务注销
     */
    private static void testServiceDeregistration(NamingService namingService, String namespace) {
        logger.info("🔍 测试: 服务注销 ({})", namespace);
        
        try {
            // 先注册一个测试服务
            String serviceName = "deregister-test-service-" + System.currentTimeMillis();
            Instance instance = new Instance();
            instance.setIp("*************");
            instance.setPort(8082);
            
            namingService.registerInstance(serviceName, instance);
            Thread.sleep(1000);
            
            // 注销服务
            namingService.deregisterInstance(serviceName, instance);
            Thread.sleep(1000);
            
            // 验证注销成功
            List<Instance> instances = namingService.getAllInstances(serviceName);
            
            if (instances == null || instances.isEmpty()) {
                logger.info("✅ {} - 服务注销: 成功注销服务 {}", namespace, serviceName);
                recordTestResult(namespace, "服务注册发现", "服务注销", true, 
                    String.format("成功注销服务: %s", serviceName), "");
            } else {
                logger.error("❌ {} - 服务注销: 注销后仍能查询到实例", namespace);
                recordTestResult(namespace, "服务注册发现", "服务注销", false, "", "注销后仍能查询到实例");
            }
            
        } catch (Exception e) {
            logger.error("❌ {} - 服务注销: 注销异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "服务注册发现", "服务注销", false, "", e.getMessage());
        }
    }
    
    /**
     * 测试批量服务操作
     */
    private static void testBatchServiceOperations(NamingService namingService, String namespace) {
        logger.info("🔍 测试: 批量服务操作 ({})", namespace);
        
        try {
            String servicePrefix = "batch-test-service-" + System.currentTimeMillis();
            int serviceCount = 5;
            List<String> serviceNames = new ArrayList<>();
            
            // 批量注册服务
            for (int i = 0; i < serviceCount; i++) {
                String serviceName = servicePrefix + "-" + i;
                serviceNames.add(serviceName);
                
                Instance instance = new Instance();
                instance.setIp("192.168.1." + (110 + i));
                instance.setPort(8090 + i);
                
                namingService.registerInstance(serviceName, instance);
            }
            
            Thread.sleep(2000); // 等待所有服务注册生效
            
            // 验证批量注册
            int successCount = 0;
            for (String serviceName : serviceNames) {
                List<Instance> instances = namingService.getAllInstances(serviceName);
                if (instances != null && !instances.isEmpty()) {
                    successCount++;
                }
            }
            
            if (successCount == serviceCount) {
                logger.info("✅ {} - 批量服务操作: 成功注册 {} 个服务", namespace, successCount);
                recordTestResult(namespace, "服务注册发现", "批量服务操作", true, 
                    String.format("成功注册 %d 个服务", successCount), "");
            } else {
                logger.error("❌ {} - 批量服务操作: 只成功注册 {}/{} 个服务", namespace, successCount, serviceCount);
                recordTestResult(namespace, "服务注册发现", "批量服务操作", false, "", 
                    String.format("只成功注册 %d/%d 个服务", successCount, serviceCount));
            }
            
            // 清理测试服务
            for (String serviceName : serviceNames) {
                try {
                    List<Instance> instances = namingService.getAllInstances(serviceName);
                    if (instances != null) {
                        for (Instance instance : instances) {
                            namingService.deregisterInstance(serviceName, instance);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("⚠️ 清理服务 {} 失败: {}", serviceName, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ {} - 批量服务操作: 操作异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "服务注册发现", "批量服务操作", false, "", e.getMessage());
        }
    }
    
    /**
     * 测试配置中心功能
     */
    private static void testConfigurationManagement(String namespace) {
        logger.info("📝 开始配置中心测试...");
        logger.info("───────────────────────────────────────────────────────────────");

        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);

            ConfigService configService = NacosFactory.createConfigService(properties);
            logger.info("✅ ConfigService创建成功");

            // 测试1: 配置创建
            testConfigCreation(configService, namespace);

            // 测试2: 配置读取
            testConfigReading(configService, namespace);

            // 测试3: 配置更新
            testConfigUpdate(configService, namespace);

            // 测试4: 配置删除
            testConfigDeletion(configService, namespace);

            // 测试5: 批量配置操作
            testBatchConfigOperations(configService, namespace);

        } catch (Exception e) {
            logger.error("❌ 配置中心测试失败: {}", e.getMessage());
            recordTestResult(namespace, "配置中心", "连接创建", false, "", e.getMessage());
        }
    }

    /**
     * 测试配置创建
     */
    private static void testConfigCreation(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 配置创建 ({})", namespace);

        try {
            String dataId = "compatibility-test-config-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String content = "# 兼容性测试配置\n" +
                           "server.port=8080\n" +
                           "app.name=compatibility-test\n" +
                           "app.version=1.0.0\n" +
                           "test.timestamp=" + System.currentTimeMillis();

            boolean result = configService.publishConfig(dataId, group, content);

            if (result) {
                // 验证配置创建成功
                Thread.sleep(1000);
                String retrievedContent = configService.getConfig(dataId, group, 5000);

                if (content.equals(retrievedContent)) {
                    logger.info("✅ {} - 配置创建: 成功创建配置 {} ({}字符)", namespace, dataId, content.length());
                    recordTestResult(namespace, "配置中心", "配置创建", true,
                        String.format("成功创建配置: %s (%d字符)", dataId, content.length()), "");
                } else {
                    logger.error("❌ {} - 配置创建: 创建后内容不一致", namespace);
                    recordTestResult(namespace, "配置中心", "配置创建", false, "", "创建后内容不一致");
                }
            } else {
                logger.error("❌ {} - 配置创建: 创建失败", namespace);
                recordTestResult(namespace, "配置中心", "配置创建", false, "", "创建失败");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 配置创建: 创建异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "配置中心", "配置创建", false, "", e.getMessage());
        }
    }

    /**
     * 测试配置读取
     */
    private static void testConfigReading(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 配置读取 ({})", namespace);

        try {
            // 先创建一个测试配置
            String dataId = "read-test-config-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String content = "test.config.read=true\ntest.value=" + System.currentTimeMillis();

            configService.publishConfig(dataId, group, content);
            Thread.sleep(1000);

            // 读取配置
            String retrievedContent = configService.getConfig(dataId, group, 5000);

            if (content.equals(retrievedContent)) {
                logger.info("✅ {} - 配置读取: 成功读取配置 ({}字符)", namespace, retrievedContent.length());
                recordTestResult(namespace, "配置中心", "配置读取", true,
                    String.format("成功读取配置 (%d字符)", retrievedContent.length()), "");
            } else {
                logger.error("❌ {} - 配置读取: 读取内容不一致", namespace);
                recordTestResult(namespace, "配置中心", "配置读取", false, "", "读取内容不一致");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 配置读取: 读取异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "配置中心", "配置读取", false, "", e.getMessage());
        }
    }

    /**
     * 测试配置更新
     */
    private static void testConfigUpdate(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 配置更新 ({})", namespace);

        try {
            // 先创建一个测试配置
            String dataId = "update-test-config-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String originalContent = "version=1.0\nstatus=original";

            configService.publishConfig(dataId, group, originalContent);
            Thread.sleep(1000);

            // 更新配置
            String updatedContent = "version=2.0\nstatus=updated\nupdated.time=" + System.currentTimeMillis();
            boolean result = configService.publishConfig(dataId, group, updatedContent);

            if (result) {
                Thread.sleep(1000);
                String retrievedContent = configService.getConfig(dataId, group, 5000);

                if (updatedContent.equals(retrievedContent)) {
                    logger.info("✅ {} - 配置更新: 成功更新配置 {}", namespace, dataId);
                    recordTestResult(namespace, "配置中心", "配置更新", true,
                        String.format("成功更新配置: %s", dataId), "");
                } else {
                    logger.error("❌ {} - 配置更新: 更新后内容不一致", namespace);
                    recordTestResult(namespace, "配置中心", "配置更新", false, "", "更新后内容不一致");
                }
            } else {
                logger.error("❌ {} - 配置更新: 更新失败", namespace);
                recordTestResult(namespace, "配置中心", "配置更新", false, "", "更新失败");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 配置更新: 更新异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "配置中心", "配置更新", false, "", e.getMessage());
        }
    }

    /**
     * 测试配置删除
     */
    private static void testConfigDeletion(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 配置删除 ({})", namespace);

        try {
            // 先创建一个测试配置
            String dataId = "delete-test-config-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String content = "test.config.delete=true";

            configService.publishConfig(dataId, group, content);
            Thread.sleep(1000);

            // 删除配置
            boolean result = configService.removeConfig(dataId, group);

            if (result) {
                Thread.sleep(1000);
                String retrievedContent = configService.getConfig(dataId, group, 5000);

                if (retrievedContent == null || retrievedContent.trim().isEmpty()) {
                    logger.info("✅ {} - 配置删除: 成功删除配置 {}", namespace, dataId);
                    recordTestResult(namespace, "配置中心", "配置删除", true,
                        String.format("成功删除配置: %s", dataId), "");
                } else {
                    logger.error("❌ {} - 配置删除: 删除后仍能读取到内容", namespace);
                    recordTestResult(namespace, "配置中心", "配置删除", false, "", "删除后仍能读取到内容");
                }
            } else {
                logger.error("❌ {} - 配置删除: 删除失败", namespace);
                recordTestResult(namespace, "配置中心", "配置删除", false, "", "删除失败");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 配置删除: 删除异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "配置中心", "配置删除", false, "", e.getMessage());
        }
    }

    /**
     * 测试批量配置操作
     */
    private static void testBatchConfigOperations(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 批量配置操作 ({})", namespace);

        try {
            String configPrefix = "batch-config-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            int configCount = 5;
            List<String> dataIds = new ArrayList<>();

            // 批量创建配置
            for (int i = 0; i < configCount; i++) {
                String dataId = configPrefix + "-" + i;
                dataIds.add(dataId);
                String content = String.format("config.index=%d\nconfig.name=%s\nconfig.timestamp=%d",
                    i, dataId, System.currentTimeMillis());

                configService.publishConfig(dataId, group, content);
            }

            Thread.sleep(2000); // 等待所有配置创建生效

            // 验证批量创建
            int successCount = 0;
            for (String dataId : dataIds) {
                String content = configService.getConfig(dataId, group, 5000);
                if (content != null && !content.trim().isEmpty()) {
                    successCount++;
                }
            }

            if (successCount == configCount) {
                logger.info("✅ {} - 批量配置操作: 成功创建 {} 个配置", namespace, successCount);
                recordTestResult(namespace, "配置中心", "批量配置操作", true,
                    String.format("成功创建 %d 个配置", successCount), "");
            } else {
                logger.error("❌ {} - 批量配置操作: 只成功创建 {}/{} 个配置", namespace, successCount, configCount);
                recordTestResult(namespace, "配置中心", "批量配置操作", false, "",
                    String.format("只成功创建 %d/%d 个配置", successCount, configCount));
            }

            // 清理测试配置
            for (String dataId : dataIds) {
                try {
                    configService.removeConfig(dataId, group);
                } catch (Exception e) {
                    logger.warn("⚠️ 清理配置 {} 失败: {}", dataId, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("❌ {} - 批量配置操作: 操作异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "配置中心", "批量配置操作", false, "", e.getMessage());
        }
    }

    /**
     * 测试边界条件
     */
    private static void testBoundaryConditions(String namespace) {
        logger.info("🔬 开始边界条件测试...");
        logger.info("───────────────────────────────────────────────────────────────");

        try {
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);

            ConfigService configService = NacosFactory.createConfigService(properties);

            // 测试1: 空配置处理
            testEmptyConfigHandling(configService, namespace);

            // 测试2: 特殊字符处理
            testSpecialCharacterHandling(configService, namespace);

            // 测试3: 大文件处理
            testLargeFileHandling(configService, namespace);

            // 测试4: 并发操作
            testConcurrentOperations(configService, namespace);

        } catch (Exception e) {
            logger.error("❌ 边界条件测试失败: {}", e.getMessage());
            recordTestResult(namespace, "边界条件", "连接创建", false, "", e.getMessage());
        }
    }

    /**
     * 测试空配置处理
     */
    private static void testEmptyConfigHandling(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 空配置处理 ({})", namespace);

        try {
            String dataId = "empty-config-test-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String emptyContent = "";

            // 创建空配置
            boolean result = configService.publishConfig(dataId, group, emptyContent);

            if (result) {
                Thread.sleep(1000);
                String retrievedContent = configService.getConfig(dataId, group, 5000);

                if (retrievedContent != null && retrievedContent.equals(emptyContent)) {
                    logger.info("✅ {} - 空配置处理: 正确处理空配置", namespace);
                    recordTestResult(namespace, "边界条件", "空配置处理", true, "正确处理空配置", "");
                } else {
                    logger.error("❌ {} - 空配置处理: 空配置处理异常", namespace);
                    recordTestResult(namespace, "边界条件", "空配置处理", false, "", "空配置处理异常");
                }

                // 清理
                configService.removeConfig(dataId, group);
            } else {
                logger.error("❌ {} - 空配置处理: 无法创建空配置", namespace);
                recordTestResult(namespace, "边界条件", "空配置处理", false, "", "无法创建空配置");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 空配置处理: 处理异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "边界条件", "空配置处理", false, "", e.getMessage());
        }
    }

    /**
     * 测试特殊字符处理
     */
    private static void testSpecialCharacterHandling(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 特殊字符处理 ({})", namespace);

        try {
            String dataId = "special-char-test-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            String specialContent = "特殊字符测试: !@#$%^&*()_+-=[]{}|;':\",./<>?`~\n" +
                                  "中文字符: 你好世界\n" +
                                  "Unicode: \u4e2d\u6587\n" +
                                  "换行符测试\n" +
                                  "制表符:\t测试\n" +
                                  "引号测试: \"双引号\" '单引号'\n" +
                                  "反斜杠: \\\\ \\n \\t";

            boolean result = configService.publishConfig(dataId, group, specialContent);

            if (result) {
                Thread.sleep(1000);
                String retrievedContent = configService.getConfig(dataId, group, 5000);

                if (specialContent.equals(retrievedContent)) {
                    logger.info("✅ {} - 特殊字符处理: 特殊字符处理成功 ({}字符)", namespace, retrievedContent.length());
                    recordTestResult(namespace, "边界条件", "特殊字符处理", true,
                        String.format("特殊字符处理成功 (%d字符)", retrievedContent.length()), "");
                } else {
                    logger.error("❌ {} - 特殊字符处理: 特殊字符读取不一致", namespace);
                    recordTestResult(namespace, "边界条件", "特殊字符处理", false, "", "特殊字符读取不一致");
                }

                // 清理
                configService.removeConfig(dataId, group);
            } else {
                logger.error("❌ {} - 特殊字符处理: 无法创建特殊字符配置", namespace);
                recordTestResult(namespace, "边界条件", "特殊字符处理", false, "", "无法创建特殊字符配置");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 特殊字符处理: 处理异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "边界条件", "特殊字符处理", false, "", e.getMessage());
        }
    }

    /**
     * 测试大文件处理
     */
    private static void testLargeFileHandling(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 大文件处理 ({})", namespace);

        try {
            String dataId = "large-file-test-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";

            // 生成大文件内容 (约30KB)
            StringBuilder largeContent = new StringBuilder();
            largeContent.append("# 大文件配置测试\n");
            largeContent.append("# 生成时间: ").append(LocalDateTime.now()).append("\n\n");

            for (int i = 0; i < 1000; i++) {
                largeContent.append(String.format("config.item.%04d=这是第%d个配置项，包含中文和数字%d\n", i, i, i * 2));
            }

            String content = largeContent.toString();
            boolean result = configService.publishConfig(dataId, group, content);

            if (result) {
                Thread.sleep(2000);
                String retrievedContent = configService.getConfig(dataId, group, 10000);

                if (content.equals(retrievedContent)) {
                    logger.info("✅ {} - 大文件处理: 大文件处理成功 ({}字符)", namespace, retrievedContent.length());
                    recordTestResult(namespace, "边界条件", "大文件处理", true,
                        String.format("大文件处理成功 (%d字符)", retrievedContent.length()), "");
                } else {
                    logger.error("❌ {} - 大文件处理: 大文件读取不一致", namespace);
                    recordTestResult(namespace, "边界条件", "大文件处理", false, "", "大文件读取不一致");
                }

                // 清理
                configService.removeConfig(dataId, group);
            } else {
                logger.error("❌ {} - 大文件处理: 无法创建大文件配置", namespace);
                recordTestResult(namespace, "边界条件", "大文件处理", false, "", "无法创建大文件配置");
            }

        } catch (Exception e) {
            logger.error("❌ {} - 大文件处理: 处理异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "边界条件", "大文件处理", false, "", e.getMessage());
        }
    }

    /**
     * 测试并发操作
     */
    private static void testConcurrentOperations(ConfigService configService, String namespace) {
        logger.info("🔍 测试: 并发操作 ({})", namespace);

        try {
            String dataId = "concurrent-test-" + System.currentTimeMillis();
            String group = "DEFAULT_GROUP";
            int threadCount = 5;
            List<Thread> threads = new ArrayList<>();
            List<Boolean> results = Collections.synchronizedList(new ArrayList<>());

            // 并发创建配置
            for (int i = 0; i < threadCount; i++) {
                final int threadIndex = i;
                Thread thread = new Thread(() -> {
                    try {
                        String content = String.format("thread.%d.content=并发测试内容%d\nthread.%d.timestamp=%d",
                            threadIndex, threadIndex, threadIndex, System.currentTimeMillis());

                        boolean result = configService.publishConfig(dataId + "-" + threadIndex, group, content);
                        results.add(result);

                        if (result) {
                            Thread.sleep(500);
                            String retrieved = configService.getConfig(dataId + "-" + threadIndex, group, 5000);
                            results.add(content.equals(retrieved));
                        }

                    } catch (Exception e) {
                        logger.warn("并发操作线程{}异常: {}", threadIndex, e.getMessage());
                        results.add(false);
                    }
                });

                threads.add(thread);
                thread.start();
            }

            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join(10000); // 最多等待10秒
            }

            long successCount = results.stream().mapToLong(r -> r ? 1 : 0).sum();

            if (successCount >= threadCount) {
                logger.info("✅ {} - 并发操作: 并发操作成功 ({}/{})", namespace, successCount, threadCount * 2);
                recordTestResult(namespace, "边界条件", "并发操作", true,
                    String.format("并发操作成功 (%d/%d)", successCount, threadCount * 2), "");
            } else {
                logger.error("❌ {} - 并发操作: 部分并发操作失败 ({}/{})", namespace, successCount, threadCount * 2);
                recordTestResult(namespace, "边界条件", "并发操作", false, "",
                    String.format("部分并发操作失败 (%d/%d)", successCount, threadCount * 2));
            }

            // 清理并发测试配置
            for (int i = 0; i < threadCount; i++) {
                try {
                    configService.removeConfig(dataId + "-" + i, group);
                } catch (Exception e) {
                    logger.warn("⚠️ 清理并发测试配置失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("❌ {} - 并发操作: 操作异常 - {}", namespace, e.getMessage());
            recordTestResult(namespace, "边界条件", "并发操作", false, "", e.getMessage());
        }
    }

    /**
     * 生成测试报告
     */
    private static void generateTestReport() {
        logger.info("📊 生成测试报告...");

        try {
            // 添加测试执行摘要
            reportContent.append("## 测试执行摘要\n\n");
            reportContent.append("- **总测试数**: ").append(totalTests).append("\n");
            reportContent.append("- **通过测试**: ").append(passedTests).append(" (").append(String.format("%.1f", (double)passedTests/totalTests*100)).append("%)\n");
            reportContent.append("- **失败测试**: ").append(failedTests).append(" (").append(String.format("%.1f", (double)failedTests/totalTests*100)).append("%)\n");
            reportContent.append("- **测试通过率**: ").append(String.format("%.1f", (double)passedTests/totalTests*100)).append("%\n\n");

            // 添加详细测试结果
            reportContent.append("## 详细测试结果\n\n");

            // 按命名空间分组
            Map<String, List<TestResult>> resultsByNamespace = new HashMap<>();
            for (TestResult result : testResults) {
                resultsByNamespace.computeIfAbsent(result.namespace, k -> new ArrayList<>()).add(result);
            }

            for (String namespace : TEST_NAMESPACES) {
                List<TestResult> namespaceResults = resultsByNamespace.get(namespace);
                if (namespaceResults != null) {
                    generateNamespaceReport(namespace, namespaceResults);
                }
            }

            // 添加问题分析和建议
            generateAnalysisAndRecommendations();

            // 添加结论
            generateConclusion();

            // 写入报告文件
            String reportFileName = "nacos兼容polaris客户端鉴权（关闭）测试报告.md";
            try (FileWriter writer = new FileWriter(reportFileName)) {
                writer.write(reportContent.toString());
            }

            logger.info("✅ 测试报告已生成: {}", reportFileName);

        } catch (IOException e) {
            logger.error("❌ 生成测试报告失败: {}", e.getMessage());
        }
    }

    /**
     * 生成命名空间报告
     */
    private static void generateNamespaceReport(String namespace, List<TestResult> results) {
        reportContent.append("### ").append(namespace).append(" 命名空间测试结果\n\n");

        long passed = results.stream().mapToLong(r -> r.passed ? 1 : 0).sum();
        long failed = results.size() - passed;

        reportContent.append("- **测试总数**: ").append(results.size()).append("\n");
        reportContent.append("- **通过**: ").append(passed).append(" (").append(String.format("%.1f", (double)passed/results.size()*100)).append("%)\n");
        reportContent.append("- **失败**: ").append(failed).append(" (").append(String.format("%.1f", (double)failed/results.size()*100)).append("%)\n\n");

        reportContent.append("| 测试类型 | 测试用例 | 结果 | 详细信息 | 错误信息 |\n");
        reportContent.append("|---------|---------|------|----------|----------|\n");

        for (TestResult result : results) {
            reportContent.append("| ").append(result.testType)
                         .append(" | ").append(result.testCase)
                         .append(" | ").append(result.passed ? "✅ PASS" : "❌ FAIL")
                         .append(" | ").append(result.details != null ? result.details : "")
                         .append(" | ").append(result.errorMessage != null ? result.errorMessage : "")
                         .append(" |\n");
        }

        reportContent.append("\n");
    }

    /**
     * 生成问题分析和建议
     */
    private static void generateAnalysisAndRecommendations() {
        reportContent.append("## 问题分析和建议\n\n");

        List<TestResult> failedResults = testResults.stream()
            .filter(r -> !r.passed)
            .collect(java.util.stream.Collectors.toList());

        if (failedResults.isEmpty()) {
            reportContent.append("🎉 **所有测试都通过了！** 客户端鉴权关闭功能工作完全正常。\n\n");
            reportContent.append("### 验证结果\n\n");
            reportContent.append("- ✅ 所有命名空间都获得了完全的读写权限\n");
            reportContent.append("- ✅ 服务注册发现功能正常工作\n");
            reportContent.append("- ✅ 配置中心功能正常工作\n");
            reportContent.append("- ✅ 边界条件处理正确\n");
            reportContent.append("- ✅ 鉴权关闭后权限绕过有效\n\n");
        } else {
            reportContent.append("### 发现的问题\n\n");

            Map<String, Long> errorsByType = failedResults.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    r -> r.testType,
                    java.util.stream.Collectors.counting()));

            for (Map.Entry<String, Long> entry : errorsByType.entrySet()) {
                reportContent.append("- **").append(entry.getKey()).append("**: ").append(entry.getValue()).append(" 个失败\n");
            }

            reportContent.append("\n### 详细问题分析\n\n");

            for (TestResult result : failedResults) {
                reportContent.append("**").append(result.namespace).append(" - ").append(result.testCase).append("**\n");
                reportContent.append("- 错误信息: ").append(result.errorMessage).append("\n");
                reportContent.append("- 建议: 检查网络连接和服务状态\n\n");
            }
        }

        reportContent.append("### 建议\n\n");
        reportContent.append("1. **持续监控**: 定期执行兼容性测试以确保功能稳定\n");
        reportContent.append("2. **性能优化**: 关注大文件和并发操作的性能表现\n");
        reportContent.append("3. **安全考虑**: 鉴权关闭后需要通过其他方式保障安全\n");
        reportContent.append("4. **文档更新**: 更新相关文档说明鉴权关闭的影响\n\n");
    }

    /**
     * 生成结论
     */
    private static void generateConclusion() {
        reportContent.append("## 结论\n\n");

        double passRate = (double)passedTests/totalTests*100;

        if (passRate >= 95) {
            reportContent.append("🎉 **测试结论: 优秀**\n\n");
            reportContent.append("Nacos客户端鉴权关闭功能工作完全正常，所有核心功能都能正确执行。");
        } else if (passRate >= 80) {
            reportContent.append("✅ **测试结论: 良好**\n\n");
            reportContent.append("Nacos客户端鉴权关闭功能基本正常，少数功能存在问题需要关注。");
        } else {
            reportContent.append("⚠️ **测试结论: 需要改进**\n\n");
            reportContent.append("Nacos客户端鉴权关闭功能存在较多问题，需要进一步调查和修复。");
        }

        reportContent.append("通过率达到 ").append(String.format("%.1f", passRate)).append("%。\n\n");

        reportContent.append("### 核心验证结果\n\n");
        reportContent.append("- **权限绕过**: 鉴权关闭后，用户确实获得了对所有命名空间的完全访问权限\n");
        reportContent.append("- **功能完整性**: 服务注册发现和配置管理功能都能正常工作\n");
        reportContent.append("- **兼容性**: Nacos与Polaris客户端在鉴权关闭状态下兼容性良好\n");
        reportContent.append("- **稳定性**: 系统在无鉴权状态下运行稳定\n\n");

        reportContent.append("---\n\n");
        reportContent.append("*报告生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("*\n");
        reportContent.append("*测试工具: Nacos Java SDK 兼容性测试程序*\n");
    }

    /**
     * 输出测试摘要
     */
    private static void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                      测试执行完成                              ║");
        logger.info("║                   Test Execution Complete               ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}", String.format("%-51d", totalTests));
        logger.info("║ 通过测试: {} ({}%)", String.format("%-42d", passedTests), String.format("%.1f", (double)passedTests/totalTests*100));
        logger.info("║ 失败测试: {} ({}%)", String.format("%-42d", failedTests), String.format("%.1f", (double)failedTests/totalTests*100));
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");

        if (failedTests == 0) {
            logger.info("🎉 所有测试都通过了！客户端鉴权关闭功能工作完全正常！");
        } else {
            logger.warn("⚠️ 有 {} 个测试失败，请查看详细报告进行分析", failedTests);
        }

        logger.info("📊 详细测试报告已生成: nacos兼容polaris客户端鉴权（关闭）测试报告.md");
    }

    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testType, String testCase,
                                       boolean passed, String details, String errorMessage) {
        totalTests++;
        if (passed) {
            passedTests++;
        } else {
            failedTests++;
        }

        testResults.add(new TestResult(namespace, testType, testCase, passed, details, errorMessage));
    }
}
