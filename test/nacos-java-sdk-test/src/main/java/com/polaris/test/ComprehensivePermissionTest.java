package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 全面的Nacos权限验证测试
 * 
 * 测试场景：
 * - cf命名空间：读写权限 ✅
 * - cf-n12s命名空间：只读权限 📖
 * - cf-ns命名空间：无权限 ❌
 * 
 * 测试内容：
 * 1. 配置管理操作：读取、创建、更新、删除
 * 2. 服务注册发现：服务注册、注销、查询、健康检查
 * 3. 边界条件测试：空配置、特殊字符、大文件等
 * 4. 错误处理验证：权限拒绝错误码、错误信息准确性
 */
public class ComprehensivePermissionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ComprehensivePermissionTest.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间和权限（客户端鉴权已关闭）
    private static final Map<String, String> NAMESPACE_PERMISSIONS = new LinkedHashMap<>();
    static {
        NAMESPACE_PERMISSIONS.put("cf", "读写权限");
        NAMESPACE_PERMISSIONS.put("cf-ns", "读写权限");
        NAMESPACE_PERMISSIONS.put("cf-n12s", "读写权限");
    }
    
    // 测试统计
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    private static final List<String> testResults = new ArrayList<>();
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║              全面的Nacos权限验证测试                           ║");
        logger.info("║         Comprehensive Nacos Permission Test             ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 权限设置:                                                    ║");
        logger.info("║ - 客户端鉴权: 已关闭 🔓                                      ║");
        logger.info("║ - cf命名空间: 读写权限 ✅（鉴权关闭）                         ║");
        logger.info("║ - cf-ns命名空间: 读写权限 ✅（鉴权关闭）                     ║");
        logger.info("║ - cf-n12s命名空间: 读写权限 ✅（鉴权关闭）                   ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 为每个命名空间执行全面测试
            for (Map.Entry<String, String> entry : NAMESPACE_PERMISSIONS.entrySet()) {
                String namespace = entry.getKey();
                String permission = entry.getValue();
                
                logger.info("🔍 开始测试命名空间: {} ({})", namespace, permission);
                testNamespace(namespace, permission);
                logger.info("");
            }
            
            // 输出测试总结
            printTestSummary();
            
        } catch (Exception e) {
            logger.error("❌ 权限验证测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 测试指定命名空间
     */
    private static void testNamespace(String namespace, String permission) {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔍 命名空间: {} ({})", namespace, permission);
        logger.info("═══════════════════════════════════════════════════════════════");
        
        try {
            // 创建服务连接
            Properties properties = new Properties();
            properties.put("serverAddr", SERVER_ADDR);
            properties.put("username", USERNAME);
            properties.put("password", PASSWORD);
            properties.put("namespace", namespace);
            
            ConfigService configService = NacosFactory.createConfigService(properties);
            NamingService namingService = NacosFactory.createNamingService(properties);
            
            logger.info("✅ 服务连接创建成功");
            
            // 1. 配置管理测试
            logger.info("📝 开始配置管理测试...");
            testConfigOperations(configService, namespace, permission);
            
            // 2. 服务注册发现测试
            logger.info("🚀 开始服务注册发现测试...");
            testNamingOperations(namingService, namespace, permission);
            
            // 3. 边界条件测试
            logger.info("🔬 开始边界条件测试...");
            testBoundaryConditions(configService, namespace, permission);
            
        } catch (Exception e) {
            logger.error("❌ 命名空间 {} 测试失败: {}", namespace, e.getMessage());
            recordTestResult(namespace, "连接创建", false, "连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试配置管理操作
     */
    private static void testConfigOperations(ConfigService configService, String namespace, String permission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 配置管理测试 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        // 1. 读取配置测试
        testConfigRead(configService, namespace, permission);
        
        // 2. 创建配置测试
        testConfigCreate(configService, namespace, permission);
        
        // 3. 更新配置测试
        testConfigUpdate(configService, namespace, permission);
        
        // 4. 删除配置测试
        testConfigDelete(configService, namespace, permission);
    }
    
    /**
     * 测试配置读取
     */
    private static void testConfigRead(ConfigService configService, String namespace, String permission) {
        String testName = "配置读取";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        try {
            // 尝试读取已存在的配置
            String content = configService.getConfig("test-config-01", "DEFAULT_GROUP", 5000);
            
            if ("无权限".equals(permission)) {
                // 无权限应该失败
                if (content == null) {
                    recordTestResult(namespace, testName, true, "正确拒绝读取");
                } else {
                    recordTestResult(namespace, testName, false, "应该拒绝但成功读取: " + content);
                }
            } else {
                // 有权限应该成功
                if (content != null) {
                    recordTestResult(namespace, testName, true, "成功读取配置 (" + content.length() + " 字符)");
                } else {
                    recordTestResult(namespace, testName, false, "应该成功但读取失败");
                }
            }
            
        } catch (NacosException e) {
            if ("无权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if (!"无权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }
    
    /**
     * 测试配置创建
     */
    private static void testConfigCreate(ConfigService configService, String namespace, String permission) {
        String testName = "配置创建";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String dataId = "permission-test-create-" + System.currentTimeMillis();
        String content = "测试创建配置 - " + namespace + " - " + java.time.LocalDateTime.now();
        
        try {
            boolean result = configService.publishConfig(dataId, "TEST_GROUP", content);
            
            if ("读写权限".equals(permission)) {
                // 读写权限应该成功
                if (result) {
                    recordTestResult(namespace, testName, true, "成功创建配置: " + dataId);
                } else {
                    recordTestResult(namespace, testName, false, "应该成功但创建失败");
                }
            } else {
                // 只读权限或无权限应该失败
                if (result) {
                    recordTestResult(namespace, testName, false, "应该拒绝但创建成功: " + dataId);
                } else {
                    recordTestResult(namespace, testName, true, "正确拒绝配置创建");
                }
            }
            
        } catch (NacosException e) {
            if (!"读写权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if ("读写权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }
    
    /**
     * 测试配置更新
     */
    private static void testConfigUpdate(ConfigService configService, String namespace, String permission) {
        String testName = "配置更新";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String dataId = "test-config-01";
        String newContent = "更新的配置内容 - " + namespace + " - " + java.time.LocalDateTime.now();
        
        try {
            boolean result = configService.publishConfig(dataId, "DEFAULT_GROUP", newContent);
            
            if ("读写权限".equals(permission)) {
                // 读写权限应该成功
                if (result) {
                    recordTestResult(namespace, testName, true, "成功更新配置: " + dataId);
                } else {
                    recordTestResult(namespace, testName, false, "应该成功但更新失败");
                }
            } else {
                // 只读权限或无权限应该失败
                if (result) {
                    recordTestResult(namespace, testName, false, "应该拒绝但更新成功: " + dataId);
                } else {
                    recordTestResult(namespace, testName, true, "正确拒绝配置更新");
                }
            }
            
        } catch (NacosException e) {
            if (!"读写权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if ("读写权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }
    
    /**
     * 测试配置删除
     */
    private static void testConfigDelete(ConfigService configService, String namespace, String permission) {
        String testName = "配置删除";
        logger.info("🔍 测试: {} ({})", testName, namespace);
        
        String dataId = "test-config-10"; // 删除最后一个测试配置
        
        try {
            boolean result = configService.removeConfig(dataId, "BUSINESS_GROUP");
            
            if ("读写权限".equals(permission)) {
                // 读写权限应该成功
                if (result) {
                    recordTestResult(namespace, testName, true, "成功删除配置: " + dataId);
                } else {
                    recordTestResult(namespace, testName, false, "应该成功但删除失败");
                }
            } else {
                // 只读权限或无权限应该失败
                if (result) {
                    recordTestResult(namespace, testName, false, "应该拒绝但删除成功: " + dataId);
                } else {
                    recordTestResult(namespace, testName, true, "正确拒绝配置删除");
                }
            }
            
        } catch (NacosException e) {
            if (!"读写权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if ("读写权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }
    
    /**
     * 记录测试结果
     */
    private static void recordTestResult(String namespace, String testName, boolean passed, String message) {
        totalTests++;
        if (passed) {
            passedTests++;
            logger.info("✅ {} - {}: {}", namespace, testName, message);
        } else {
            failedTests++;
            logger.error("❌ {} - {}: {}", namespace, testName, message);
        }
        
        testResults.add(String.format("%s | %s | %s | %s", 
            namespace, testName, passed ? "PASS" : "FAIL", message));
    }
    
    /**
     * 测试服务注册发现操作
     */
    private static void testNamingOperations(NamingService namingService, String namespace, String permission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🚀 服务注册发现测试 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");

        // 1. 服务查询测试
        testServiceQuery(namingService, namespace, permission);

        // 2. 服务注册测试
        testServiceRegister(namingService, namespace, permission);

        // 3. 服务注销测试
        testServiceDeregister(namingService, namespace, permission);
    }

    /**
     * 测试服务查询
     */
    private static void testServiceQuery(NamingService namingService, String namespace, String permission) {
        String testName = "服务查询";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        try {
            List<Instance> instances = namingService.getAllInstances("user-service");

            if ("无权限".equals(permission)) {
                // 无权限应该失败或返回空
                if (instances == null || instances.isEmpty()) {
                    recordTestResult(namespace, testName, true, "正确拒绝服务查询");
                } else {
                    recordTestResult(namespace, testName, false, "应该拒绝但查询成功: " + instances.size() + " 个实例");
                }
            } else {
                // 有权限应该成功
                if (instances != null && !instances.isEmpty()) {
                    recordTestResult(namespace, testName, true, "成功查询服务 (" + instances.size() + " 个实例)");
                } else {
                    recordTestResult(namespace, testName, false, "应该成功但查询失败");
                }
            }

        } catch (NacosException e) {
            if ("无权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if (!"无权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }

    /**
     * 测试服务注册
     */
    private static void testServiceRegister(NamingService namingService, String namespace, String permission) {
        String testName = "服务注册";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        String serviceName = "permission-test-service-" + System.currentTimeMillis();

        try {
            Instance instance = new Instance();
            instance.setIp("*************");
            instance.setPort(9000);
            instance.setWeight(1.0);
            instance.setHealthy(true);
            instance.addMetadata("test", "permission-test");

            namingService.registerInstance(serviceName, instance);

            if ("读写权限".equals(permission)) {
                // 读写权限应该成功
                recordTestResult(namespace, testName, true, "成功注册服务: " + serviceName);
            } else {
                // 只读权限或无权限应该失败，但没有抛出异常说明可能成功了
                recordTestResult(namespace, testName, false, "应该拒绝但注册成功: " + serviceName);
            }

        } catch (NacosException e) {
            if (!"读写权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if ("读写权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }

    /**
     * 测试服务注销
     */
    private static void testServiceDeregister(NamingService namingService, String namespace, String permission) {
        String testName = "服务注销";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        try {
            namingService.deregisterInstance("log-service", "192.168.1.109", 8089);

            if ("读写权限".equals(permission)) {
                // 读写权限应该成功
                recordTestResult(namespace, testName, true, "成功注销服务: log-service");
            } else {
                // 只读权限或无权限应该失败，但没有抛出异常说明可能成功了
                recordTestResult(namespace, testName, false, "应该拒绝但注销成功: log-service");
            }

        } catch (NacosException e) {
            if (!"读写权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "正确返回权限拒绝错误: " + e.getErrCode());
            } else if ("读写权限".equals(permission)) {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode() + " - " + e.getErrMsg());
            } else {
                recordTestResult(namespace, testName, false, "错误码不正确: " + e.getErrCode() + " (期望401001)");
            }
        }
    }

    /**
     * 测试边界条件
     */
    private static void testBoundaryConditions(ConfigService configService, String namespace, String permission) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🔬 边界条件测试 ({})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");

        // 1. 空配置测试
        testEmptyConfig(configService, namespace, permission);

        // 2. 特殊字符测试
        testSpecialCharacters(configService, namespace, permission);

        // 3. 大文件测试
        testLargeConfig(configService, namespace, permission);
    }

    /**
     * 测试空配置
     */
    private static void testEmptyConfig(ConfigService configService, String namespace, String permission) {
        String testName = "空配置处理";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        try {
            String content = configService.getConfig("non-existent-config", "DEFAULT_GROUP", 3000);

            if ("无权限".equals(permission)) {
                recordTestResult(namespace, testName, true, "无权限正确处理空配置");
            } else {
                if (content == null) {
                    recordTestResult(namespace, testName, true, "正确返回空配置");
                } else {
                    recordTestResult(namespace, testName, false, "意外返回内容: " + content);
                }
            }

        } catch (NacosException e) {
            if ("无权限".equals(permission) && e.getErrCode() == 401001) {
                recordTestResult(namespace, testName, true, "无权限正确拒绝: " + e.getErrCode());
            } else {
                recordTestResult(namespace, testName, false, "意外错误: " + e.getErrCode());
            }
        }
    }

    /**
     * 测试特殊字符
     */
    private static void testSpecialCharacters(ConfigService configService, String namespace, String permission) {
        String testName = "特殊字符处理";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        if (!"读写权限".equals(permission)) {
            recordTestResult(namespace, testName, true, "跳过测试（无写权限）");
            return;
        }

        String dataId = "special-char-test-" + System.currentTimeMillis();
        String content = "特殊字符测试: !@#$%^&*()_+-=[]{}|;':\",./<>?`~中文测试";

        try {
            boolean result = configService.publishConfig(dataId, "SPECIAL_GROUP", content);

            if (result) {
                // 验证读取
                String readContent = configService.getConfig(dataId, "SPECIAL_GROUP", 3000);
                if (content.equals(readContent)) {
                    recordTestResult(namespace, testName, true, "特殊字符处理正确");
                } else {
                    recordTestResult(namespace, testName, false, "特殊字符读取不一致");
                }
            } else {
                recordTestResult(namespace, testName, false, "特殊字符配置创建失败");
            }

        } catch (Exception e) {
            recordTestResult(namespace, testName, false, "特殊字符测试异常: " + e.getMessage());
        }
    }

    /**
     * 测试大文件
     */
    private static void testLargeConfig(ConfigService configService, String namespace, String permission) {
        String testName = "大文件处理";
        logger.info("🔍 测试: {} ({})", testName, namespace);

        if (!"读写权限".equals(permission)) {
            recordTestResult(namespace, testName, true, "跳过测试（无写权限）");
            return;
        }

        String dataId = "large-config-test-" + System.currentTimeMillis();
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("这是大文件测试的第").append(i + 1).append("行内容，包含一些中文和数字123456789\n");
        }

        try {
            boolean result = configService.publishConfig(dataId, "LARGE_GROUP", largeContent.toString());

            if (result) {
                recordTestResult(namespace, testName, true, "大文件处理成功 (" + largeContent.length() + " 字符)");
            } else {
                recordTestResult(namespace, testName, false, "大文件配置创建失败");
            }

        } catch (Exception e) {
            recordTestResult(namespace, testName, false, "大文件测试异常: " + e.getMessage());
        }
    }

    /**
     * 输出测试总结
     */
    private static void printTestSummary() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                      测试总结报告                              ║");
        logger.info("║                   Test Summary Report                    ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 总测试数: {}", totalTests);
        logger.info("║ 通过测试: {} ({}%)", passedTests, totalTests > 0 ? (passedTests * 100 / totalTests) : 0);
        logger.info("║ 失败测试: {} ({}%)", failedTests, totalTests > 0 ? (failedTests * 100 / totalTests) : 0);
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");

        logger.info("📊 详细测试结果:");
        logger.info("命名空间 | 测试项目 | 结果 | 详细信息");
        logger.info("─".repeat(80));
        for (String result : testResults) {
            logger.info(result);
        }

        logger.info("");
        if (failedTests == 0) {
            logger.info("🎉 所有测试通过！权限验证系统工作正常！");
        } else {
            logger.warn("⚠️ 有 {} 个测试失败，请检查权限配置或系统实现", failedTests);
        }
    }
}
