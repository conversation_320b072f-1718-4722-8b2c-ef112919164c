package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * 全面的Nacos权限验证测试 - 测试环境准备
 * 
 * 任务：在cf、cf-ns、cf-n12s三个命名空间下分别创建：
 * 1. 10个配置文件（使用不同分组）
 * 2. 10个服务实例（使用不同服务名）
 */
public class ComprehensiveTestDataSetup {
    
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveTestDataSetup.class);
    
    // 服务器配置
    private static final String SERVER_ADDR = "180.76.109.137:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间
    private static final List<String> NAMESPACES = Arrays.asList("cf", "cf-ns", "cf-n12s");
    
    // 配置文件分组
    private static final List<String> CONFIG_GROUPS = Arrays.asList(
        "DEFAULT_GROUP", "APP_GROUP", "CACHE_GROUP", "DATABASE_GROUP", "SECURITY_GROUP",
        "MONITOR_GROUP", "API_GROUP", "SERVICE_GROUP", "MIDDLEWARE_GROUP", "BUSINESS_GROUP"
    );
    
    // 服务名称
    private static final List<String> SERVICE_NAMES = Arrays.asList(
        "user-service", "order-service", "payment-service", "inventory-service", "notification-service",
        "auth-service", "gateway-service", "config-service", "monitor-service", "log-service"
    );
    
    public static void main(String[] args) {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║          全面的Nacos权限验证测试 - 测试环境准备                 ║");
        logger.info("║        Comprehensive Nacos Permission Test - Setup        ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 任务: 在3个命名空间下分别创建10个配置文件和10个服务实例         ║");
        logger.info("║ 命名空间: cf, cf-ns, cf-n12s                                ║");
        logger.info("║ 测试时间: {}", java.time.LocalDateTime.now());
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
        
        try {
            // 为每个命名空间创建测试数据
            for (String namespace : NAMESPACES) {
                logger.info("🔧 开始为命名空间 '{}' 创建测试数据", namespace);
                setupTestDataForNamespace(namespace);
                logger.info("✅ 命名空间 '{}' 测试数据创建完成", namespace);
                logger.info("");
            }
            
            logger.info("🎉 所有测试数据创建完成！");
            logger.info("");
            logger.info("📊 创建统计:");
            logger.info("  - 命名空间数量: {}", NAMESPACES.size());
            logger.info("  - 每个命名空间配置文件数量: {}", CONFIG_GROUPS.size());
            logger.info("  - 每个命名空间服务实例数量: {}", SERVICE_NAMES.size());
            logger.info("  - 总配置文件数量: {}", NAMESPACES.size() * CONFIG_GROUPS.size());
            logger.info("  - 总服务实例数量: {}", NAMESPACES.size() * SERVICE_NAMES.size());
            
        } catch (Exception e) {
            logger.error("❌ 测试环境准备失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 为指定命名空间创建测试数据
     */
    private static void setupTestDataForNamespace(String namespace) throws NacosException {
        logger.info("═══════════════════════════════════════════════════════════════");
        logger.info("🔧 命名空间: {}", namespace);
        logger.info("═══════════════════════════════════════════════════════════════");
        
        // 创建ConfigService和NamingService
        Properties properties = new Properties();
        properties.put("serverAddr", SERVER_ADDR);
        properties.put("username", USERNAME);
        properties.put("password", PASSWORD);
        properties.put("namespace", namespace);
        
        ConfigService configService = NacosFactory.createConfigService(properties);
        NamingService namingService = NacosFactory.createNamingService(properties);
        
        logger.info("✅ 服务连接创建成功");
        
        // 1. 创建配置文件
        logger.info("📝 开始创建配置文件...");
        createConfigFiles(configService, namespace);
        
        // 2. 注册服务实例
        logger.info("🚀 开始注册服务实例...");
        registerServiceInstances(namingService, namespace);
        
        logger.info("✅ 命名空间 '{}' 数据创建完成", namespace);
    }
    
    /**
     * 创建配置文件
     */
    private static void createConfigFiles(ConfigService configService, String namespace) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("📝 创建配置文件 (命名空间: {})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < CONFIG_GROUPS.size(); i++) {
            String group = CONFIG_GROUPS.get(i);
            String dataId = String.format("test-config-%02d", i + 1);
            String content = generateConfigContent(namespace, group, dataId, i + 1);
            
            try {
                boolean result = configService.publishConfig(dataId, group, content);
                
                if (result) {
                    successCount++;
                    logger.info("✅ 配置创建成功: {} / {} ({})", dataId, group, namespace);
                    logger.debug("  - 内容长度: {} 字符", content.length());
                } else {
                    failCount++;
                    logger.error("❌ 配置创建失败: {} / {} ({})", dataId, group, namespace);
                }
                
                // 短暂延迟，避免请求过快
                Thread.sleep(100);
                
            } catch (Exception e) {
                failCount++;
                logger.error("❌ 配置创建异常: {} / {} ({}) - {}", dataId, group, namespace, e.getMessage());
            }
        }
        
        logger.info("📊 配置文件创建统计 ({}): 成功 {}, 失败 {}", namespace, successCount, failCount);
    }
    
    /**
     * 注册服务实例
     */
    private static void registerServiceInstances(NamingService namingService, String namespace) {
        logger.info("───────────────────────────────────────────────────────────────");
        logger.info("🚀 注册服务实例 (命名空间: {})", namespace);
        logger.info("───────────────────────────────────────────────────────────────");
        
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < SERVICE_NAMES.size(); i++) {
            String serviceName = SERVICE_NAMES.get(i);
            
            try {
                // 创建服务实例
                Instance instance = new Instance();
                instance.setIp("192.168.1." + (100 + i)); // 模拟不同IP
                instance.setPort(8080 + i); // 模拟不同端口
                instance.setWeight(1.0);
                instance.setHealthy(true);
                instance.setEnabled(true);
                
                // 添加元数据
                instance.addMetadata("version", "1.0." + i);
                instance.addMetadata("environment", "test");
                instance.addMetadata("namespace", namespace);
                instance.addMetadata("created-by", "comprehensive-test");
                instance.addMetadata("created-time", String.valueOf(System.currentTimeMillis()));
                
                // 注册服务实例
                namingService.registerInstance(serviceName, instance);
                
                successCount++;
                logger.info("✅ 服务注册成功: {} ({}:{})", serviceName, instance.getIp(), instance.getPort());
                logger.debug("  - 命名空间: {}", namespace);
                logger.debug("  - 元数据: {}", instance.getMetadata());
                
                // 短暂延迟，避免请求过快
                Thread.sleep(100);
                
            } catch (Exception e) {
                failCount++;
                logger.error("❌ 服务注册异常: {} ({}) - {}", serviceName, namespace, e.getMessage());
            }
        }
        
        logger.info("📊 服务实例注册统计 ({}): 成功 {}, 失败 {}", namespace, successCount, failCount);
    }
    
    /**
     * 生成配置内容
     */
    private static String generateConfigContent(String namespace, String group, String dataId, int index) {
        StringBuilder content = new StringBuilder();
        
        content.append("# Nacos配置文件\n");
        content.append("# 命名空间: ").append(namespace).append("\n");
        content.append("# 分组: ").append(group).append("\n");
        content.append("# 配置ID: ").append(dataId).append("\n");
        content.append("# 创建时间: ").append(java.time.LocalDateTime.now()).append("\n");
        content.append("# 序号: ").append(index).append("\n");
        content.append("\n");
        
        // 根据分组生成不同类型的配置内容
        switch (group) {
            case "DEFAULT_GROUP":
                content.append("# 默认配置\n");
                content.append("app.name=test-application-").append(index).append("\n");
                content.append("app.version=1.0.").append(index).append("\n");
                break;
                
            case "APP_GROUP":
                content.append("# 应用配置\n");
                content.append("server.port=").append(8080 + index).append("\n");
                content.append("server.servlet.context-path=/app").append(index).append("\n");
                break;
                
            case "CACHE_GROUP":
                content.append("# 缓存配置\n");
                content.append("redis.host=192.168.1.").append(100 + index).append("\n");
                content.append("redis.port=").append(6379 + index).append("\n");
                content.append("redis.database=").append(index).append("\n");
                break;
                
            case "DATABASE_GROUP":
                content.append("# 数据库配置\n");
                content.append("datasource.url=**********************.").append(200 + index).append(":3306/test").append(index).append("\n");
                content.append("datasource.username=user").append(index).append("\n");
                content.append("datasource.password=password").append(index).append("\n");
                break;
                
            case "SECURITY_GROUP":
                content.append("# 安全配置\n");
                content.append("jwt.secret=secret-key-").append(index).append("\n");
                content.append("jwt.expiration=").append(3600 * index).append("\n");
                break;
                
            default:
                content.append("# ").append(group).append(" 配置\n");
                content.append("config.key").append(index).append("=value").append(index).append("\n");
                content.append("config.enabled=true\n");
                break;
        }
        
        content.append("\n");
        content.append("# 测试数据标识\n");
        content.append("test.namespace=").append(namespace).append("\n");
        content.append("test.group=").append(group).append("\n");
        content.append("test.dataId=").append(dataId).append("\n");
        content.append("test.index=").append(index).append("\n");
        content.append("test.created=").append(System.currentTimeMillis()).append("\n");
        
        return content.toString();
    }
}
