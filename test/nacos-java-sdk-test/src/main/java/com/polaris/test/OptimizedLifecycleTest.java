package com.polaris.test;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 优化的完整生命周期测试用例
 * 
 * 解决问题：
 * - 查询不存在服务导致误导性权限错误
 * - 区分权限错误和资源不存在错误
 * - 完整的服务和配置生命周期测试
 * 
 * 测试策略：
 * - 先注册服务/配置，再查询验证
 * - 每个操作后都进行相应的验证查询
 * - 使用唯一的服务名和配置ID避免冲突
 */
public class OptimizedLifecycleTest {
    
    private static final Logger logger = LoggerFactory.getLogger(OptimizedLifecycleTest.class);
    
    // 测试配置
    private static final String SERVER_ADDR = "**************:8848";
    private static final String USERNAME = "cf";
    private static final String PASSWORD = "RDSY6EClAZQjkT/pW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV/+A/Y1WLgGY3a0bhFzPSDHP7jmHGjB5DY=";
    
    // 测试命名空间配置
    private static final String[] NAMESPACES = {"cf", "cf-n12s", "cf-ns"};
    private static final String[] NAMESPACE_DESCRIPTIONS = {
        "cf命名空间（读写权限）",
        "cf-n12s命名空间（只读权限）", 
        "cf-ns命名空间（无权限）"
    };
    
    // 权限预期结果：[读权限, 写权限]
    private static final boolean[][] EXPECTED_PERMISSIONS = {
        {true, true},   // cf: 读写权限
        {true, false},  // cf-n12s: 只读权限
        {false, false}  // cf-ns: 无权限
    };
    
    // 测试统计
    private final AtomicInteger totalTests = new AtomicInteger(0);
    private final AtomicInteger passedTests = new AtomicInteger(0);
    private final AtomicInteger failedTests = new AtomicInteger(0);
    
    // 测试结果记录
    private final StringBuilder testReport = new StringBuilder();
    
    public static void main(String[] args) {
        OptimizedLifecycleTest test = new OptimizedLifecycleTest();
        test.runOptimizedLifecycleTest();
    }
    
    public void runOptimizedLifecycleTest() {
        printTestHeader();
        
        // 对每个命名空间进行完整的生命周期测试
        for (int i = 0; i < NAMESPACES.length; i++) {
            String namespace = NAMESPACES[i];
            String description = NAMESPACE_DESCRIPTIONS[i];
            boolean[] permissions = EXPECTED_PERMISSIONS[i];
            
            logger.info("┌─────────────────────────────────────────────────────────────┐");
            logger.info("│ 测试命名空间: {}", description);
            logger.info("│ 权限配置: 读权限={}, 写权限={}", permissions[0], permissions[1]);
            logger.info("└─────────────────────────────────────────────────────────────┘");
            
            testNamespaceLifecycle(namespace, permissions, i);
            
            logger.info("");
            logger.info("═══════════════════════════════════════════════════════════════");
            logger.info("");
        }
        
        printTestSummary();
        printDetailedReport();
    }
    
    /**
     * 测试单个命名空间的完整生命周期
     */
    private void testNamespaceLifecycle(String namespace, boolean[] permissions, int namespaceIndex) {
        boolean hasReadPermission = permissions[0];
        boolean hasWritePermission = permissions[1];
        
        // 生成唯一的测试标识符
        long timestamp = System.currentTimeMillis();
        String testServiceName = "lifecycle-test-service-" + namespace + "-" + timestamp;
        String testConfigDataId = "lifecycle-test-config-" + namespace + "-" + timestamp;
        String testConfigGroup = "LIFECYCLE_TEST_GROUP";
        
        logger.info("🔧 测试标识符:");
        logger.info("  - 服务名: {}", testServiceName);
        logger.info("  - 配置DataId: {}", testConfigDataId);
        logger.info("  - 配置Group: {}", testConfigGroup);
        logger.info("");
        
        // 测试注册中心生命周期
        testNamingServiceLifecycle(namespace, testServiceName, hasReadPermission, hasWritePermission, namespaceIndex);
        
        logger.info("");
        
        // 测试配置中心生命周期
        testConfigServiceLifecycle(namespace, testConfigDataId, testConfigGroup, hasReadPermission, hasWritePermission);
    }
    
    /**
     * 测试注册中心完整生命周期
     */
    private void testNamingServiceLifecycle(String namespace, String serviceName, 
                                          boolean hasReadPermission, boolean hasWritePermission, int namespaceIndex) {
        logger.info("🚀 开始注册中心生命周期测试");
        logger.info("⏰ 测试时间: {}", getCurrentTime());
        
        NamingService namingService = null;
        boolean serviceRegistered = false;
        
        try {
            // 创建NamingService
            namingService = createNamingService(namespace);
            logger.info("✅ NamingService创建成功");
            
            // 1. 服务注册测试
            serviceRegistered = testServiceRegistration(namingService, namespace, serviceName, 
                                                      hasWritePermission, namespaceIndex);
            
            if (serviceRegistered) {
                // 2. 查询服务列表测试
                testServiceListQuery(namingService, namespace, serviceName, hasReadPermission);
                
                // 3. 查询服务详情测试
                testServiceInstanceQuery(namingService, namespace, serviceName, hasReadPermission);
                
                // 4. 查询实例详情测试
                testInstanceDetailQuery(namingService, namespace, serviceName, hasReadPermission);
                
                // 5. 实例更新测试
                boolean instanceUpdated = testInstanceUpdate(namingService, namespace, serviceName, 
                                                           hasWritePermission, namespaceIndex);
                
                if (instanceUpdated) {
                    // 6. 更新后查询测试
                    testQueryAfterUpdate(namingService, namespace, serviceName, hasReadPermission);
                }
                
                // 7. 服务注销测试
                boolean serviceDeregistered = testServiceDeregistration(namingService, namespace, serviceName, 
                                                                       hasWritePermission, namespaceIndex);
                
                if (serviceDeregistered) {
                    // 8. 注销后查询测试
                    testQueryAfterDeregistration(namingService, namespace, serviceName, hasReadPermission);
                }
            } else {
                // 如果注册失败，测试读操作（使用已知存在的服务）
                if (hasReadPermission) {
                    testReadOperationsWithExistingService(namingService, namespace);
                } else {
                    testReadOperationsWithoutPermission(namingService, namespace);
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ 注册中心生命周期测试异常: {}", e.getMessage());
            recordTestResult("注册中心生命周期测试", namespace, false, "异常: " + e.getMessage());
        } finally {
            if (namingService != null) {
                try {
                    namingService.shutDown();
                    logger.info("🔒 NamingService已关闭");
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭NamingService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 测试配置中心完整生命周期
     */
    private void testConfigServiceLifecycle(String namespace, String dataId, String group,
                                          boolean hasReadPermission, boolean hasWritePermission) {
        logger.info("🚀 开始配置中心生命周期测试");
        logger.info("⏰ 测试时间: {}", getCurrentTime());
        
        ConfigService configService = null;
        boolean configPublished = false;
        AtomicReference<String> listenerContent = new AtomicReference<>();
        CountDownLatch listenerLatch = new CountDownLatch(1);
        
        try {
            // 创建ConfigService
            configService = createConfigService(namespace);
            logger.info("✅ ConfigService创建成功");
            
            // 1. 配置发布测试
            configPublished = testConfigPublish(configService, namespace, dataId, group, hasWritePermission);
            
            if (configPublished) {
                // 2. 配置查询测试
                testConfigQuery(configService, namespace, dataId, group, hasReadPermission);
                
                // 3. 配置监听器测试
                Listener listener = testConfigListener(configService, namespace, dataId, group, 
                                                     hasReadPermission, listenerContent, listenerLatch);
                
                // 4. 配置更新测试
                boolean configUpdated = testConfigUpdate(configService, namespace, dataId, group, hasWritePermission);
                
                if (configUpdated && listener != null) {
                    // 5. 监听验证测试
                    testListenerVerification(listenerLatch, listenerContent, hasReadPermission);
                }
                
                if (configUpdated) {
                    // 6. 更新后查询测试
                    testConfigQueryAfterUpdate(configService, namespace, dataId, group, hasReadPermission);
                }
                
                // 清理监听器
                if (listener != null) {
                    try {
                        configService.removeListener(dataId, group, listener);
                        logger.info("🔧 配置监听器已移除");
                    } catch (Exception e) {
                        logger.warn("⚠️ 移除监听器异常: {}", e.getMessage());
                    }
                }
                
                // 7. 配置删除测试
                boolean configDeleted = testConfigDelete(configService, namespace, dataId, group, hasWritePermission);
                
                if (configDeleted) {
                    // 8. 删除后查询测试
                    testConfigQueryAfterDelete(configService, namespace, dataId, group, hasReadPermission);
                }
            } else {
                // 如果发布失败，测试读操作
                if (hasReadPermission) {
                    testConfigReadOperationsWithoutConfig(configService, namespace, dataId, group);
                } else {
                    testConfigReadOperationsWithoutPermission(configService, namespace, dataId, group);
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ 配置中心生命周期测试异常: {}", e.getMessage());
            recordTestResult("配置中心生命周期测试", namespace, false, "异常: " + e.getMessage());
        } finally {
            if (configService != null) {
                try {
                    configService.shutDown();
                    logger.info("🔒 ConfigService已关闭");
                } catch (Exception e) {
                    logger.warn("⚠️ 关闭ConfigService异常: {}", e.getMessage());
                }
            }
        }
    }
    
    // ==================== 辅助方法 ====================
    
    private NamingService createNamingService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createNamingService(properties);
    }
    
    private ConfigService createConfigService(String namespace) throws NacosException {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", SERVER_ADDR);
        properties.setProperty("username", USERNAME);
        properties.setProperty("password", PASSWORD);
        properties.setProperty("namespace", namespace);
        
        return NacosFactory.createConfigService(properties);
    }
    
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    private void recordTestResult(String testName, String namespace, boolean passed, String details) {
        testReport.append(String.format("[%s] %s - %s: %s - %s%n", 
                         getCurrentTime(), namespace, testName, 
                         passed ? "✅ 通过" : "❌ 失败", details));
    }
    
    /**
     * 测试服务注册
     */
    private boolean testServiceRegistration(NamingService namingService, String namespace, String serviceName,
                                          boolean expectedSuccess, int namespaceIndex) {
        String testName = "服务注册";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤1: {} (registerInstance)", testName);
            logger.info("🔍 注册服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            Instance instance = new Instance();
            instance.setIp("*************");
            instance.setPort(8080 + namespaceIndex);
            instance.setWeight(1.0);
            instance.setHealthy(true);
            instance.setEnabled(true);
            instance.addMetadata("test-type", "lifecycle-test");
            instance.addMetadata("namespace", namespace);
            instance.addMetadata("timestamp", String.valueOf(System.currentTimeMillis()));

            logger.info("📋 实例信息: IP={}, Port={}, Weight={}",
                      instance.getIp(), instance.getPort(), instance.getWeight());

            namingService.registerInstance(serviceName, instance);

            // 等待注册生效
            Thread.sleep(2000);

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "注册成功");
                return true;
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("⚠️ 等待过程中被中断");
            return false;
        }
    }

    /**
     * 测试查询服务列表
     */
    private void testServiceListQuery(NamingService namingService, String namespace, String serviceName,
                                    boolean expectedSuccess) {
        String testName = "查询服务列表";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤2: {} (getServicesOfServer)", testName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            ListView<String> serviceList = namingService.getServicesOfServer(1, 20);

            logger.info("📊 服务列表查询结果: 总数={}, 当前页数量={}",
                      serviceList.getCount(), serviceList.getData().size());

            // 检查我们刚注册的服务是否在列表中
            boolean serviceFound = serviceList.getData().contains(serviceName);
            logger.info("🔍 目标服务 {} 是否在列表中: {}", serviceName, serviceFound ? "是" : "否");

            if (expectedSuccess) {
                if (serviceFound) {
                    logger.info("✅ {} 成功 - 符合预期，且找到目标服务", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功找到目标服务");
                } else {
                    logger.warn("⚠️ {} 成功但未找到目标服务 - 可能需要更多时间同步", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功但未找到目标服务");
                }
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试查询服务实例
     */
    private void testServiceInstanceQuery(NamingService namingService, String namespace, String serviceName,
                                        boolean expectedSuccess) {
        String testName = "查询服务实例";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤3: {} (getAllInstances)", testName);
            logger.info("🔍 查询服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            List<Instance> instances = namingService.getAllInstances(serviceName);

            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());

            if (!instances.isEmpty()) {
                Instance instance = instances.get(0);
                logger.info("📋 实例详情: IP={}, Port={}, Weight={}, Healthy={}, Enabled={}",
                          instance.getIp(), instance.getPort(), instance.getWeight(),
                          instance.isHealthy(), instance.isEnabled());
            }

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期，实例数量: {}", testName, instances.size());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "成功，实例数量: " + instances.size());
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试查询实例详情
     */
    private void testInstanceDetailQuery(NamingService namingService, String namespace, String serviceName,
                                       boolean expectedSuccess) {
        String testName = "查询实例详情";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤4: {} (selectInstances)", testName);
            logger.info("🔍 查询服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            // 查询健康实例
            List<Instance> healthyInstances = namingService.selectInstances(serviceName, true);
            // 查询所有实例
            List<Instance> allInstances = namingService.selectInstances(serviceName, false);

            logger.info("📊 健康实例数量: {}", healthyInstances.size());
            logger.info("📊 所有实例数量: {}", allInstances.size());

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true,
                               String.format("成功，健康实例: %d, 所有实例: %d",
                                           healthyInstances.size(), allInstances.size()));
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试实例更新
     */
    private boolean testInstanceUpdate(NamingService namingService, String namespace, String serviceName,
                                     boolean expectedSuccess, int namespaceIndex) {
        String testName = "实例更新";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤5: {} (registerInstance with update)", testName);
            logger.info("🔍 更新服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            Instance updatedInstance = new Instance();
            updatedInstance.setIp("*************");
            updatedInstance.setPort(8080 + namespaceIndex);
            updatedInstance.setWeight(2.0); // 更新权重
            updatedInstance.setHealthy(true);
            updatedInstance.setEnabled(true);
            updatedInstance.addMetadata("test-type", "lifecycle-test-updated");
            updatedInstance.addMetadata("namespace", namespace);
            updatedInstance.addMetadata("update-timestamp", String.valueOf(System.currentTimeMillis()));

            logger.info("📋 更新实例信息: IP={}, Port={}, Weight={} (权重已更新)",
                      updatedInstance.getIp(), updatedInstance.getPort(), updatedInstance.getWeight());

            namingService.registerInstance(serviceName, updatedInstance);

            // 等待更新生效
            Thread.sleep(2000);

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "更新成功");
                return true;
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("⚠️ 等待过程中被中断");
            return false;
        }
    }

    /**
     * 测试更新后查询
     */
    private void testQueryAfterUpdate(NamingService namingService, String namespace, String serviceName,
                                    boolean expectedSuccess) {
        String testName = "更新后查询";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤6: {} (getAllInstances)", testName);
            logger.info("🔍 查询更新后的服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            List<Instance> instances = namingService.getAllInstances(serviceName);

            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());

            if (!instances.isEmpty()) {
                Instance instance = instances.get(0);
                logger.info("📋 实例详情: IP={}, Port={}, Weight={}, Healthy={}, Enabled={}",
                          instance.getIp(), instance.getPort(), instance.getWeight(),
                          instance.isHealthy(), instance.isEnabled());

                // 验证权重是否已更新
                if (Math.abs(instance.getWeight() - 2.0) < 0.001) {
                    logger.info("✅ 权重更新验证成功: 权重已更新为 2.0");
                } else {
                    logger.warn("⚠️ 权重更新验证失败: 期望 2.0, 实际 {}", instance.getWeight());
                }
            }

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "成功，实例数量: " + instances.size());
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试服务注销
     */
    private boolean testServiceDeregistration(NamingService namingService, String namespace, String serviceName,
                                            boolean expectedSuccess, int namespaceIndex) {
        String testName = "服务注销";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤7: {} (deregisterInstance)", testName);
            logger.info("🔍 注销服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            logger.info("📋 注销实例信息: IP=*************, Port={}", 8080 + namespaceIndex);

            namingService.deregisterInstance(serviceName, "*************", 8080 + namespaceIndex);

            // 等待注销生效
            Thread.sleep(2000);

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "注销成功");
                return true;
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("⚠️ 等待过程中被中断");
            return false;
        }
    }

    /**
     * 测试注销后查询
     */
    private void testQueryAfterDeregistration(NamingService namingService, String namespace, String serviceName,
                                            boolean expectedSuccess) {
        String testName = "注销后查询";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤8: {} (getAllInstances)", testName);
            logger.info("🔍 查询注销后的服务: {}", serviceName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功（实例数量应为0）" : "失败");

            List<Instance> instances = namingService.getAllInstances(serviceName);

            logger.info("📊 查询结果: 共找到 {} 个实例", instances.size());

            if (expectedSuccess) {
                if (instances.isEmpty()) {
                    logger.info("✅ {} 成功 - 符合预期，实例已被完全注销", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功，实例已完全注销");
                } else {
                    logger.warn("⚠️ {} 成功但仍有实例存在 - 可能需要更多时间同步", testName);
                    logger.info("📋 剩余实例详情:");
                    for (int i = 0; i < instances.size(); i++) {
                        Instance instance = instances.get(i);
                        logger.info("  实例[{}]: IP={}, Port={}, Healthy={}",
                                  i + 1, instance.getIp(), instance.getPort(), instance.isHealthy());
                    }
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功但仍有 " + instances.size() + " 个实例");
                }
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    // ==================== 配置中心测试方法 ====================

    /**
     * 测试配置发布
     */
    private boolean testConfigPublish(ConfigService configService, String namespace, String dataId, String group,
                                    boolean expectedSuccess) {
        String testName = "配置发布";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤1: {} (publishConfig)", testName);
            logger.info("🔍 发布配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            String configContent = generateConfigContent("初始配置", namespace);
            logger.info("📋 配置内容长度: {} 字符", configContent.length());

            boolean result = configService.publishConfig(dataId, group, configContent);

            if (result && expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "发布成功");
                return true;
            } else if (!result && !expectedSuccess) {
                logger.info("✅ {} 正确失败 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确失败");
                return false;
            } else if (result && !expectedSuccess) {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        }
    }

    /**
     * 测试配置查询
     */
    private void testConfigQuery(ConfigService configService, String namespace, String dataId, String group,
                               boolean expectedSuccess) {
        String testName = "配置查询";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤2: {} (getConfig)", testName);
            logger.info("🔍 查询配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            String content = configService.getConfig(dataId, group, 5000);

            if (expectedSuccess) {
                if (content != null) {
                    logger.info("✅ {} 成功 - 符合预期", testName);
                    logger.info("📋 配置内容长度: {} 字符", content.length());
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功，内容长度: " + content.length());
                } else {
                    logger.warn("⚠️ {} 成功但内容为空 - 可能配置不存在", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功但内容为空");
                }
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试配置监听器
     */
    private Listener testConfigListener(ConfigService configService, String namespace, String dataId, String group,
                                      boolean expectedSuccess, AtomicReference<String> listenerContent,
                                      CountDownLatch listenerLatch) {
        String testName = "配置监听器";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤3: {} (addListener)", testName);
            logger.info("🔍 添加监听器: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            Listener listener = new Listener() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    logger.info("🔔 配置监听器收到变更通知");
                    logger.info("📋 变更内容长度: {} 字符", configInfo != null ? configInfo.length() : 0);
                    listenerContent.set(configInfo);
                    listenerLatch.countDown();
                }

                @Override
                public Executor getExecutor() {
                    return null;
                }
            };

            configService.addListener(dataId, group, listener);

            if (expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "监听器添加成功");
                return listener;
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return listener;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return null;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return null;
            }
        }
    }

    /**
     * 生成配置内容
     */
    private String generateConfigContent(String type, String namespace) {
        StringBuilder sb = new StringBuilder();
        sb.append("# 优化生命周期测试 - ").append(type).append("\n");
        sb.append("# 命名空间: ").append(namespace).append("\n");
        sb.append("# 生成时间: ").append(getCurrentTime()).append("\n");
        sb.append("# 时间戳: ").append(System.currentTimeMillis()).append("\n");
        sb.append("\n");
        sb.append("# 应用配置\n");
        sb.append("app.name=optimized-lifecycle-test\n");
        sb.append("app.version=").append(type.equals("初始配置") ? "1.0.0" : "2.0.0").append("\n");
        sb.append("app.environment=").append(type.equals("初始配置") ? "test" : "production").append("\n");
        sb.append("app.namespace=").append(namespace).append("\n");
        sb.append("\n");
        sb.append("# 数据库配置\n");
        sb.append("database.url=***************************/").append(namespace).append("_db\n");
        sb.append("database.username=").append(namespace).append("_user\n");
        sb.append("database.pool.size=").append(type.equals("初始配置") ? "10" : "20").append("\n");
        sb.append("\n");
        sb.append("# 缓存配置\n");
        sb.append("cache.enabled=true\n");
        sb.append("cache.ttl=").append(type.equals("初始配置") ? "3600" : "7200").append("\n");

        if (!type.equals("初始配置")) {
            sb.append("\n");
            sb.append("# 新增配置项\n");
            sb.append("app.debug=false\n");
            sb.append("logging.level=INFO\n");
            sb.append("monitoring.enabled=true\n");
        }

        return sb.toString();
    }

    // ==================== 剩余的配置中心测试方法 ====================

    /**
     * 测试配置更新
     */
    private boolean testConfigUpdate(ConfigService configService, String namespace, String dataId, String group,
                                   boolean expectedSuccess) {
        String testName = "配置更新";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤4: {} (publishConfig with new content)", testName);
            logger.info("🔍 更新配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            String updatedContent = generateConfigContent("更新配置", namespace);
            logger.info("📋 更新配置内容长度: {} 字符", updatedContent.length());

            boolean result = configService.publishConfig(dataId, group, updatedContent);

            if (result && expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "更新成功");
                return true;
            } else if (!result && !expectedSuccess) {
                logger.info("✅ {} 正确失败 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确失败");
                return false;
            } else if (result && !expectedSuccess) {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        }
    }

    /**
     * 测试监听验证
     */
    private void testListenerVerification(CountDownLatch listenerLatch, AtomicReference<String> listenerContent,
                                        boolean expectedSuccess) {
        String testName = "监听验证";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤5: {} - 等待监听器通知", testName);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "收到通知" : "不收到通知");

            boolean received = listenerLatch.await(10, TimeUnit.SECONDS);

            if (received && expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期，收到配置变更通知", testName);
                String content = listenerContent.get();
                if (content != null) {
                    logger.info("📋 监听器收到内容长度: {} 字符", content.length());
                }
                passedTests.incrementAndGet();
                recordTestResult(testName, "监听器", true, "成功收到通知");
            } else if (!received && !expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期，未收到通知", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, "监听器", true, "正确未收到通知");
            } else if (received && !expectedSuccess) {
                logger.error("❌ {} 意外收到通知 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, "监听器", false, "意外收到通知");
            } else {
                logger.warn("⚠️ {} 未收到通知 - 可能是网络延迟", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, "监听器", true, "未收到通知（可能是延迟）");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("⚠️ 等待监听器通知时被中断");
            failedTests.incrementAndGet();
            recordTestResult(testName, "监听器", false, "等待被中断");
        }
    }

    // ==================== 辅助测试方法 ====================

    /**
     * 测试使用已知存在服务的读操作
     */
    private void testReadOperationsWithExistingService(NamingService namingService, String namespace) {
        logger.info("📋 使用已知存在服务测试读操作");

        try {
            // 先获取服务列表
            ListView<String> serviceList = namingService.getServicesOfServer(1, 10);
            if (!serviceList.getData().isEmpty()) {
                String existingService = serviceList.getData().get(0);
                logger.info("🔍 使用已知服务进行测试: {}", existingService);

                // 测试查询该服务的实例
                testServiceInstanceQuery(namingService, namespace, existingService, true);
                testInstanceDetailQuery(namingService, namespace, existingService, true);
            } else {
                logger.warn("⚠️ 未找到已知存在的服务");
            }
        } catch (Exception e) {
            logger.warn("⚠️ 测试已知服务时异常: {}", e.getMessage());
        }
    }

    /**
     * 测试无权限的读操作
     */
    private void testReadOperationsWithoutPermission(NamingService namingService, String namespace) {
        logger.info("📋 测试无权限的读操作");

        // 测试服务列表查询
        testServiceListQuery(namingService, namespace, "dummy-service", false);
    }

    /**
     * 测试配置读操作（无配置情况）
     */
    private void testConfigReadOperationsWithoutConfig(ConfigService configService, String namespace,
                                                     String dataId, String group) {
        logger.info("📋 测试配置读操作（配置不存在）");
        testConfigQuery(configService, namespace, dataId, group, true);
    }

    /**
     * 测试配置读操作（无权限）
     */
    private void testConfigReadOperationsWithoutPermission(ConfigService configService, String namespace,
                                                         String dataId, String group) {
        logger.info("📋 测试配置读操作（无权限）");
        testConfigQuery(configService, namespace, dataId, group, false);
    }

    /**
     * 测试更新后配置查询
     */
    private void testConfigQueryAfterUpdate(ConfigService configService, String namespace, String dataId, String group,
                                          boolean expectedSuccess) {
        String testName = "更新后配置查询";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤6: {} (getConfig)", testName);
            logger.info("🔍 查询更新后的配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            String content = configService.getConfig(dataId, group, 5000);

            if (expectedSuccess) {
                if (content != null) {
                    logger.info("✅ {} 成功 - 符合预期", testName);
                    logger.info("📋 更新后配置内容长度: {} 字符", content.length());

                    // 验证是否包含更新后的内容
                    if (content.contains("app.version=2.0.0") && content.contains("app.environment=production")) {
                        logger.info("✅ 配置更新验证成功: 包含预期的更新内容");
                    } else {
                        logger.warn("⚠️ 配置更新验证失败: 未包含预期的更新内容");
                    }

                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功，内容长度: " + content.length());
                } else {
                    logger.warn("⚠️ {} 成功但内容为空", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功但内容为空");
                }
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    /**
     * 测试配置删除
     */
    private boolean testConfigDelete(ConfigService configService, String namespace, String dataId, String group,
                                   boolean expectedSuccess) {
        String testName = "配置删除";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤7: {} (removeConfig)", testName);
            logger.info("🔍 删除配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功" : "失败");

            boolean result = configService.removeConfig(dataId, group);

            if (result && expectedSuccess) {
                logger.info("✅ {} 成功 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "删除成功");
                return true;
            } else if (!result && !expectedSuccess) {
                logger.info("✅ {} 正确失败 - 符合预期", testName);
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确失败");
                return false;
            } else if (result && !expectedSuccess) {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败");
                return false;
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
                return false;
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
                return false;
            }
        }
    }

    /**
     * 测试删除后配置查询
     */
    private void testConfigQueryAfterDelete(ConfigService configService, String namespace, String dataId, String group,
                                          boolean expectedSuccess) {
        String testName = "删除后配置查询";
        totalTests.incrementAndGet();

        try {
            logger.info("📋 步骤8: {} (getConfig)", testName);
            logger.info("🔍 查询删除后的配置: dataId={}, group={}", dataId, group);
            logger.info("🎯 预期结果: {}", expectedSuccess ? "成功（内容应为空）" : "失败");

            String content = configService.getConfig(dataId, group, 5000);

            if (expectedSuccess) {
                if (content == null || content.trim().isEmpty()) {
                    logger.info("✅ {} 成功 - 符合预期，配置已被完全删除", testName);
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功，配置已完全删除");
                } else {
                    logger.warn("⚠️ {} 成功但配置仍存在 - 可能需要更多时间同步", testName);
                    logger.info("📋 剩余配置内容长度: {} 字符", content.length());
                    passedTests.incrementAndGet();
                    recordTestResult(testName, namespace, true, "成功但配置仍存在");
                }
            } else {
                logger.error("❌ {} 意外成功 - 不符合预期", testName);
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外成功");
            }

        } catch (NacosException e) {
            if (!expectedSuccess) {
                logger.info("✅ {} 正确被拒绝 - 符合预期", testName);
                logger.info("🔒 权限错误详情: {}", e.getErrMsg());
                passedTests.incrementAndGet();
                recordTestResult(testName, namespace, true, "正确被拒绝: " + e.getErrMsg());
            } else {
                logger.error("❌ {} 失败 - 不符合预期: {}", testName, e.getErrMsg());
                failedTests.incrementAndGet();
                recordTestResult(testName, namespace, false, "意外失败: " + e.getErrMsg());
            }
        }
    }

    // ==================== 总结方法 ====================

    private void printTestSummary() {
        int total = totalTests.get();
        int passed = passedTests.get();
        int failed = failedTests.get();
        double successRate = total > 0 ? (double) passed / total * 100 : 0;

        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                    优化生命周期测试报告                         ║");
        logger.info("║              Optimized Lifecycle Test Report              ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 测试完成时间: {}", getCurrentTime());
        logger.info("║                                                              ║");
        logger.info("║ 测试统计:                                                    ║");
        logger.info("║   - 总测试数: {} 个", total);
        logger.info("║   - 通过测试: {} 个", passed);
        logger.info("║   - 失败测试: {} 个", failed);
        logger.info("║   - 成功率: {:.1f}%", successRate);
        logger.info("║                                                              ║");
        if (successRate >= 95) {
            logger.info("║ 🎉 测试结果: 优秀 (成功率 >= 95%)                            ║");
        } else if (successRate >= 85) {
            logger.info("║ ✅ 测试结果: 良好 (成功率 >= 85%)                            ║");
        } else if (successRate >= 70) {
            logger.info("║ ⚠️  测试结果: 一般 (成功率 >= 70%)                            ║");
        } else {
            logger.info("║ ❌ 测试结果: 需要改进 (成功率 < 70%)                         ║");
        }
        logger.info("╚══════════════════════════════════════════════════════════════╝");
    }

    private void printDetailedReport() {
        logger.info("");
        logger.info("📋 详细测试报告:");
        logger.info("─────────────────────────────────────────────────────────────");
        logger.info(testReport.toString());
        logger.info("─────────────────────────────────────────────────────────────");
        logger.info("感谢您使用优化的完整生命周期测试用例！");
    }

    private void printTestHeader() {
        logger.info("╔══════════════════════════════════════════════════════════════╗");
        logger.info("║                优化的完整生命周期测试用例                       ║");
        logger.info("║            Optimized Complete Lifecycle Test Suite         ║");
        logger.info("╠══════════════════════════════════════════════════════════════╣");
        logger.info("║ 目标: 解决查询不存在服务导致的误导性权限错误                    ║");
        logger.info("║ 策略: 先注册服务/配置，再查询验证                              ║");
        logger.info("║ 测试时间: {}", getCurrentTime());
        logger.info("║                                                              ║");
        logger.info("║ 测试范围:                                                    ║");
        logger.info("║   注册中心: 注册→查询→更新→注销 完整生命周期                   ║");
        logger.info("║   配置中心: 发布→查询→监听→更新→删除 完整生命周期               ║");
        logger.info("╚══════════════════════════════════════════════════════════════╝");
        logger.info("");
    }
}
