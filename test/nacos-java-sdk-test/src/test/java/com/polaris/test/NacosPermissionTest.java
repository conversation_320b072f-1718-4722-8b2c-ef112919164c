package com.polaris.test;

import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.Assert.*;

/**
 * Nacos 权限测试的 JUnit 测试类
 * 可以通过 Maven 运行：mvn test
 */
public class NacosPermissionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(NacosPermissionTest.class);
    
    @Before
    public void setUp() {
        logger.info("开始执行 Nacos 权限测试");
        TestConfig.printConfig();
    }
    
    @After
    public void tearDown() {
        logger.info("Nacos 权限测试执行完成");
    }
    
    /**
     * 测试有权限用户的服务注册操作
     */
    @Test
    public void testAuthorizedServiceRegistration() {
        logger.info("执行测试: 有权限用户服务注册");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testAuthorizedServiceRegistration();
        
        logger.info("测试结果: {}", result);
        assertTrue("有权限用户的服务注册应该成功", result.isSuccess());
    }
    
    /**
     * 测试无权限用户的服务注册操作
     */
    @Test
    public void testUnauthorizedServiceRegistration() {
        logger.info("执行测试: 无权限用户服务注册");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testUnauthorizedServiceRegistration();
        
        logger.info("测试结果: {}", result);
        assertTrue("无权限用户的服务注册应该被拒绝", result.isSuccess());
        
        // 如果测试失败，检查是否是权限绕过问题
        if (!result.isSuccess()) {
            String message = result.getMessage().toLowerCase();
            if (message.contains("权限绕过") || message.contains("成功注册")) {
                fail("发现权限绕过漏洞：" + result.getMessage());
            }
        }
    }
    
    /**
     * 测试有权限用户的服务注销操作
     */
    @Test
    public void testAuthorizedServiceDeregistration() {
        logger.info("执行测试: 有权限用户服务注销");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testAuthorizedServiceDeregistration();
        
        logger.info("测试结果: {}", result);
        assertTrue("有权限用户的服务注销应该成功", result.isSuccess());
    }
    
    /**
     * 测试无权限用户的服务注销操作
     */
    @Test
    public void testUnauthorizedServiceDeregistration() {
        logger.info("执行测试: 无权限用户服务注销");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testUnauthorizedServiceDeregistration();
        
        logger.info("测试结果: {}", result);
        assertTrue("无权限用户的服务注销应该被拒绝", result.isSuccess());
        
        // 如果测试失败，检查是否是权限绕过问题
        if (!result.isSuccess()) {
            String message = result.getMessage().toLowerCase();
            if (message.contains("权限绕过") || message.contains("成功注销")) {
                fail("发现权限绕过漏洞：" + result.getMessage());
            }
        }
    }
    
    /**
     * 测试服务发现功能
     */
    @Test
    public void testServiceDiscovery() {
        logger.info("执行测试: 服务发现功能");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testServiceDiscovery();
        
        logger.info("测试结果: {}", result);
        assertTrue("服务发现功能应该正常工作", result.isSuccess());
    }
    
    /**
     * 测试跨命名空间访问
     */
    @Test
    public void testCrossNamespaceAccess() {
        logger.info("执行测试: 跨命名空间服务访问");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testCrossNamespaceAccess();
        
        logger.info("测试结果: {}", result);
        assertTrue("跨命名空间访问应该正确隔离", result.isSuccess());
    }
    
    /**
     * 测试批量操作权限控制
     */
    @Test
    public void testBatchOperationPermission() {
        logger.info("执行测试: 批量操作权限控制");
        
        PermissionTestCase.TestResult result = PermissionTestCase.testBatchOperationPermission();
        
        logger.info("测试结果: {}", result);
        assertTrue("批量操作的权限控制应该正常", result.isSuccess());
        
        // 如果测试失败，检查是否是权限绕过问题
        if (!result.isSuccess()) {
            String message = result.getMessage().toLowerCase();
            if (message.contains("权限绕过") || message.contains("成功执行")) {
                fail("发现批量操作权限绕过漏洞：" + result.getMessage());
            }
        }
    }
    
    /**
     * 综合权限测试
     * 验证修复后的权限控制系统是否完全正常
     */
    @Test
    public void testOverallPermissionControl() {
        logger.info("执行综合权限测试");
        
        int totalTests = 0;
        int passedTests = 0;
        
        // 执行所有权限相关测试
        PermissionTestCase.TestResult[] results = {
            PermissionTestCase.testAuthorizedServiceRegistration(),
            PermissionTestCase.testUnauthorizedServiceRegistration(),
            PermissionTestCase.testAuthorizedServiceDeregistration(),
            PermissionTestCase.testUnauthorizedServiceDeregistration(),
            PermissionTestCase.testServiceDiscovery(),
            PermissionTestCase.testCrossNamespaceAccess(),
            PermissionTestCase.testBatchOperationPermission()
        };
        
        StringBuilder failureMessages = new StringBuilder();
        boolean hasPermissionBypass = false;
        
        for (PermissionTestCase.TestResult result : results) {
            totalTests++;
            if (result.isSuccess()) {
                passedTests++;
            } else {
                failureMessages.append("\n- ").append(result.toString());
                
                // 检查是否是权限绕过问题
                String message = result.getMessage().toLowerCase();
                if (message.contains("权限绕过") || 
                    (message.contains("成功") && result.getTestName().contains("无权限"))) {
                    hasPermissionBypass = true;
                }
            }
        }
        
        logger.info("综合测试结果: {}/{} 通过", passedTests, totalTests);
        
        // 如果发现权限绕过，立即失败
        if (hasPermissionBypass) {
            fail("发现权限绕过漏洞，权限控制系统存在安全问题！" + failureMessages.toString());
        }
        
        // 如果有其他失败，也要报告
        if (passedTests < totalTests) {
            fail(String.format("综合权限测试失败 (%d/%d 通过)：%s", 
                             passedTests, totalTests, failureMessages.toString()));
        }
        
        logger.info("🎉 综合权限测试全部通过，权限控制系统工作正常！");
    }
}
