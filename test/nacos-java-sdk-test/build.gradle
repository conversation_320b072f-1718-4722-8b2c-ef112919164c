plugins {
    id 'java'
    id 'application'
}

group = 'com.polaris.test'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
    maven {
        url 'https://maven.aliyun.com/repository/public'
    }
}

dependencies {
    // Nacos Client
    implementation 'com.alibaba.nacos:nacos-client:2.2.4'
    
    // Logging
    implementation 'org.slf4j:slf4j-api:1.7.36'
    implementation 'ch.qos.logback:logback-classic:1.2.12'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
}

application {
    mainClass = 'com.polaris.test.NacosPermissionTestRunner'
}

test {
    useJUnit()
    
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
    
    // 设置测试超时
    timeout = Duration.ofMinutes(10)
}

// 创建日志目录
task createLogDir {
    doLast {
        mkdir 'logs'
    }
}

// 运行前创建日志目录
run.dependsOn createLogDir
test.dependsOn createLogDir

// 自定义任务：运行权限测试
task runPermissionTests(type: JavaExec) {
    group = 'verification'
    description = 'Run Nacos permission tests'
    classpath = sourceSets.main.runtimeClasspath
    mainClass = 'com.polaris.test.NacosPermissionTestRunner'
    dependsOn createLogDir
}

// 自定义任务：运行特定的权限测试
task runUnauthorizedTests(type: Test) {
    group = 'verification'
    description = 'Run unauthorized access tests'
    include '**/NacosPermissionTest.class'
    useJUnit {
        includeCategories 'com.polaris.test.UnauthorizedTest'
    }
    dependsOn createLogDir
}

// 编译时的编码设置
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

// JAR 文件配置
jar {
    manifest {
        attributes(
            'Main-Class': 'com.polaris.test.NacosPermissionTestRunner',
            'Implementation-Title': 'Nacos Java SDK Permission Test',
            'Implementation-Version': project.version
        )
    }
    
    // 包含所有依赖（创建 fat jar）
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

// 清理任务
clean {
    delete 'logs'
    delete 'compile.log'
    delete 'junit.log'
}
