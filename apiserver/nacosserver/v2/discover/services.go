/**
 * <PERSON><PERSON> is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package discover

import (
	"context"

	"go.uber.org/zap"

	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"github.com/polarismesh/polaris/common/utils"
	nacosmodel "github.com/polarismesh/polaris/apiserver/nacosserver/model"
	nacospb "github.com/polarismesh/polaris/apiserver/nacosserver/v2/pb"
	"github.com/polarismesh/polaris/apiserver/nacosserver/v2/remote"
)

func (h *DiscoverServer) handleServiceListRequest(ctx context.Context, req nacospb.BaseRequest,
	meta nacospb.RequestMeta) (nacospb.BaseResponse, error) {
	svcListReq, ok := req.(*nacospb.ServiceListRequest)
	if !ok {
		return nil, remote.ErrorInvalidRequestBodyType
	}

	// 添加调试日志：标识Nacos v2 gRPC服务列表查询 - 现在包含权限验证
	nacoslog.Info("[NACOS-V2][gRPC][ServiceList] Processing Nacos v2 gRPC service list query with permission check",
		zap.String("namespace", svcListReq.Namespace),
		zap.String("group", svcListReq.GroupName),
		zap.Int("pageNo", int(svcListReq.PageNo)),
		zap.Int("pageSize", int(svcListReq.PageSize)),
		zap.String("connection_id", remote.ValueConnID(ctx)),
		zap.String("operation", "SERVICE_LIST_QUERY"))

	// 🔧 修复权限绕过漏洞：添加服务列表查询权限验证
	if err := h.checkServiceListPermission(ctx, svcListReq.Namespace); err != nil {
		nacoslog.Warn("[SERVICE-LIST-DEBUG] Service list permission denied",
			zap.String("namespace", svcListReq.Namespace),
			zap.String("connection_id", remote.ValueConnID(ctx)),
			zap.String("operation", "SERVICE_LIST_PERMISSION_DENIED"),
			zap.Error(err))

		// 权限验证失败，返回错误响应
		if nacosErr, ok := err.(*nacosmodel.NacosError); ok {
			return &nacospb.ServiceListResponse{
				Response: &nacospb.Response{
					ResultCode: int(nacosErr.ErrCode),
					Success:    false,
					Message:    nacosErr.ErrMsg,
				},
				Count:        0,
				ServiceNames: []string{},
			}, nil
		}

		// 其他类型的错误
		return &nacospb.ServiceListResponse{
			Response: &nacospb.Response{
				ResultCode: int(nacosmodel.Response_Fail.Code),
				Success:    false,
				Message:    "Permission denied: " + err.Error(),
			},
			Count:        0,
			ServiceNames: []string{},
		}, nil
	}

	nacoslog.Info("[SERVICE-LIST-DEBUG] Service list permission granted",
		zap.String("namespace", svcListReq.Namespace),
		zap.String("connection_id", remote.ValueConnID(ctx)),
		zap.String("operation", "SERVICE_LIST_PERMISSION_GRANTED"))

	resp := &nacospb.ServiceListResponse{
		Response: &nacospb.Response{
			ResultCode: int(nacosmodel.Response_Success.Code),
			Success:    true,
			Message:    "success",
		},
		Count:        0,
		ServiceNames: []string{},
	}

	namespace := nacosmodel.ToPolarisNamespace(svcListReq.Namespace)
	viewList, count := nacosmodel.HandleServiceListRequest(h.discoverSvr, namespace, svcListReq.GroupName,
		svcListReq.PageNo, svcListReq.PageSize)
	resp.ServiceNames = viewList
	resp.Count = count
	return resp, nil
}

// checkServiceListPermission 检查用户是否有权限查询指定命名空间的服务列表
// 通过调用带有权限验证拦截器的GetServiceWithCache方法来进行真正的权限验证
func (h *DiscoverServer) checkServiceListPermission(ctx context.Context, namespace string) error {
	// 转换命名空间格式
	namespace = nacosmodel.ToPolarisNamespace(namespace)

	// 添加调试日志：检查context中的token信息
	token := ctx.Value(utils.ContextAuthTokenKey)
	nacoslog.Info("[SERVICE-LIST-DEBUG] Checking service list permission (service-based check)",
		zap.String("namespace", namespace),
		zap.Any("token", token),
		zap.Bool("has_token", token != nil))

	// 创建一个虚拟服务对象用于权限验证
	// 对于服务列表查询，我们创建一个特殊的服务名来表示这是服务列表查询操作
	svc := &apiservice.Service{
		Name:      utils.NewStringValue("*"), // 使用通配符表示查询所有服务
		Namespace: utils.NewStringValue(namespace),
	}

	// 调用带有权限验证拦截器的GetServiceWithCache方法
	// 这个方法会通过权限验证拦截器检查用户是否有权限访问该命名空间的服务
	resp := h.discoverSvr.GetServiceWithCache(ctx, svc)

	// 添加调试日志：权限验证结果
	nacoslog.Info("[SERVICE-LIST-DEBUG] Service-based permission check result",
		zap.String("namespace", namespace),
		zap.Uint32("response_code", resp.GetCode().GetValue()),
		zap.String("response_info", resp.GetInfo().GetValue()),
		zap.Bool("permission_granted", resp.GetCode().GetValue() == uint32(apimodel.Code_ExecuteSuccess)))

	// 检查响应结果
	if resp.GetCode().GetValue() != uint32(apimodel.Code_ExecuteSuccess) {
		// 权限验证失败，返回错误
		return &nacosmodel.NacosError{
			ErrCode: int32(resp.GetCode().GetValue()),
			ErrMsg:  "No permission to list services in namespace: " + namespace + ", error: " + resp.GetInfo().GetValue(),
		}
	}

	nacoslog.Info("[SERVICE-LIST-DEBUG] Service-based permission check passed",
		zap.String("namespace", namespace))

	return nil // 权限验证成功
}
