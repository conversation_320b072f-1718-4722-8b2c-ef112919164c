/**
 * <PERSON><PERSON> is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package discover

import (
	"context"
	"strings"
	"time"

	"go.uber.org/zap"

	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"github.com/polarismesh/polaris/apiserver/nacosserver/core"
	nacosmodel "github.com/polarismesh/polaris/apiserver/nacosserver/model"
	nacospb "github.com/polarismesh/polaris/apiserver/nacosserver/v2/pb"
	"github.com/polarismesh/polaris/apiserver/nacosserver/v2/remote"
	"github.com/polarismesh/polaris/common/metrics"
	commontime "github.com/polarismesh/polaris/common/time"
	"github.com/polarismesh/polaris/common/utils"
	"github.com/polarismesh/polaris/plugin"
)

func (h *DiscoverServer) handleSubscribeServiceReques(ctx context.Context, req nacospb.BaseRequest,
	meta nacospb.RequestMeta) (nacospb.BaseResponse, error) {
	subReq, ok := req.(*nacospb.SubscribeServiceRequest)
	if !ok {
		return nil, remote.ErrorInvalidRequestBodyType
	}
	namespace := subReq.Namespace
	service := subReq.ServiceName
	group := subReq.GroupName

	// 添加详细调试日志：订阅服务请求处理
	nacoslog.Info("[SUBSCRIPTION-DEBUG] Processing service subscription request",
		zap.String("namespace", namespace),
		zap.String("service", service),
		zap.String("group", group),
		zap.String("clusters", subReq.Clusters),
		zap.Bool("subscribe", subReq.Subscribe),
		zap.String("connection_id", remote.ValueConnID(ctx)),
		zap.String("client_ip", meta.ClientIP),
		zap.String("client_version", meta.ClientVersion),
		zap.String("operation", "SUBSCRIBE_SERVICE_REQUEST"))

	// 权限验证：检查用户是否有权限订阅该命名空间的服务
	if err := h.checkSubscriptionPermission(ctx, namespace, service, group); err != nil {
		nacoslog.Warn("[SUBSCRIPTION-DEBUG] Subscription permission denied",
			zap.String("namespace", namespace),
			zap.String("service", service),
			zap.String("group", group),
			zap.String("connection_id", remote.ValueConnID(ctx)),
			zap.String("client_ip", meta.ClientIP),
			zap.String("operation", "SUBSCRIPTION_PERMISSION_DENIED"),
			zap.Error(err))
		return nil, err
	}

	nacoslog.Info("[SUBSCRIPTION-DEBUG] Subscription permission granted",
		zap.String("namespace", namespace),
		zap.String("service", service),
		zap.String("group", group),
		zap.String("connection_id", remote.ValueConnID(ctx)),
		zap.String("operation", "SUBSCRIPTION_PERMISSION_GRANTED"))
	subscriber := core.Subscriber{
		Key:         remote.ValueConnID(ctx),
		AddrStr:     meta.ClientIP,
		Agent:       meta.ClientVersion,
		App:         utils.DefaultString(req.GetHeaders()["app"], "unknown"),
		Ip:          meta.ClientIP,
		NamespaceId: namespace,
		Group:       group,
		Service:     service,
		Cluster:     subReq.Clusters,
		Type:        core.GRPCPush,
	}
	if subReq.Subscribe {
		// 添加详细调试日志：添加订阅者 - 权限绕过漏洞关键点
		nacoslog.Info("[SUBSCRIPTION-DEBUG] Adding subscriber - POTENTIAL PERMISSION BYPASS",
			zap.String("namespace", namespace),
			zap.String("service", service),
			zap.String("group", group),
			zap.String("connection_id", remote.ValueConnID(ctx)),
			zap.String("subscriber_key", subscriber.Key),
			zap.String("operation", "ADD_SUBSCRIBER"),
			zap.String("resource", namespace+"/"+service))
		h.pushCenter.AddSubscriber(subscriber)
	} else {
		// 添加详细调试日志：移除订阅者
		nacoslog.Info("[SUBSCRIPTION-DEBUG] Removing subscriber",
			zap.String("namespace", namespace),
			zap.String("service", service),
			zap.String("group", group),
			zap.String("connection_id", remote.ValueConnID(ctx)),
			zap.String("subscriber_key", subscriber.Key),
			zap.String("operation", "REMOVE_SUBSCRIBER"),
			zap.String("resource", namespace+"/"+service))
		h.pushCenter.RemoveSubscriber(subscriber)
	}

	filterCtx := &core.FilterContext{
		Service:     core.ToNacosService(h.discoverSvr.Cache(), namespace, service, group),
		EnableOnly:  true,
		HealthyOnly: false,
	}
	if len(subReq.Clusters) == 0 {
		filterCtx.Clusters = strings.Split(subReq.Clusters, ",")
	}
	// 默认只下发 enable 的实例
	result := h.store.ListInstances(filterCtx, core.SelectInstancesWithHealthyProtection)

	// 添加详细调试日志：返回订阅数据 - 权限绕过漏洞关键点
	nacoslog.Info("[SUBSCRIPTION-DEBUG] Returning subscription data - POTENTIAL PERMISSION BYPASS",
		zap.String("namespace", namespace),
		zap.String("service", service),
		zap.String("group", group),
		zap.String("connection_id", remote.ValueConnID(ctx)),
		zap.Int("instance_count", len(result.Hosts)),
		zap.String("checksum", result.Checksum),
		zap.String("operation", "RETURN_SUBSCRIPTION_DATA"),
		zap.String("resource", namespace+"/"+service))

	return &nacospb.SubscribeServiceResponse{
		Response: &nacospb.Response{
			ResultCode: int(nacosmodel.Response_Success.Code),
			Message:    "success",
		},
		ServiceInfo: *result,
	}, nil
}

func (h *DiscoverServer) sendPushData(sub core.Subscriber, data *core.PushData) error {
	// 添加详细调试日志：推送数据给订阅者 - 权限绕过漏洞关键点
	nacoslog.Info("[PUSH-DEBUG] Sending push data to subscriber - POTENTIAL PERMISSION BYPASS",
		zap.String("namespace", data.Service.Namespace),
		zap.String("service", data.Service.Name),
		zap.String("group", data.Service.Group),
		zap.String("connection_id", sub.Key),
		zap.String("subscriber_ip", sub.Ip),
		zap.Int("instance_count", len(data.ServiceInfo.Hosts)),
		zap.String("checksum", data.ServiceInfo.Checksum),
		zap.String("operation", "PUSH_DATA_TO_SUBSCRIBER"),
		zap.String("resource", data.Service.Namespace+"/"+data.Service.Name))

	client, ok := h.connMgr.GetClient(sub.Key)
	if !ok {
		nacoslog.Error("[NACOS-V2][PushCenter] notify subscriber client not found", zap.String("conn-id", sub.Key))
		return nil
	}
	stream, ok := client.LoadStream()
	if !ok {
		nacoslog.Error("[NACOS-V2][PushCenter] notify subscriber not register gRPC stream",
			zap.String("conn-id", sub.Key))
		return nil
	}
	namespace := nacosmodel.ToNacosNamespace(data.ServiceInfo.Namespace)
	watcher := sub
	svr := stream
	req := &nacospb.NotifySubscriberRequest{
		NamingRequest: nacospb.NewBasicNamingRequest(utils.NewUUID(), namespace, data.ServiceInfo.Name,
			data.ServiceInfo.GroupName),
		ServiceInfo: data.ServiceInfo,
	}

	connCtx := context.WithValue(context.TODO(), remote.ConnIDKey{}, watcher.Key)
	callback := func(attachment map[string]interface{}, resp nacospb.BaseResponse, err error) {
		if err != nil {
			nacoslog.Error("[NACOS-V2][PushCenter] receive client push error",
				zap.String("req-id", req.RequestId),
				zap.String("namespace", data.Service.Namespace), zap.String("svc", data.Service.Name),
				zap.Error(err))
		} else {
			// 刷新连接的存活时间
			h.connMgr.RefreshClient(connCtx)
			nacoslog.Info("[NACOS-V2][PushCenter] receive client push ack", zap.String("req-id", req.RequestId),
				zap.String("namespace", data.Service.Namespace), zap.String("svc", data.Service.Name),
				zap.Any("resp", resp))
		}
		plugin.GetStatis().ReportDiscoverCall(metrics.ClientDiscoverMetric{
			ClientIP:  client.Addr.String(),
			Action:    attachment["action"].(string),
			Namespace: attachment["namespace"].(string),
			Resource:  attachment["resource"].(string),
			Revision:  attachment["revision"].(string),
			Timestamp: commontime.CurrentMillisecond(),
			CostTime:  commontime.CurrentMillisecond() - attachment["start"].(int64),
			Success:   err == nil,
		})
	}
	clientResp, err := remote.MarshalPayload(req)
	if err != nil {
		return err
	}
	// add inflight first
	if err := h.connMgr.InFlights().AddInFlight(&remote.InFlight{
		ConnID:     watcher.Key,
		RequestID:  req.RequestId,
		Callback:   callback,
		ExpireTime: time.Now().Add(5 * time.Second),
		Attachment: map[string]interface{}{
			"start":     commontime.CurrentMillisecond(),
			"action":    "NACOS_SERVICE_PUSH",
			"namespace": namespace,
			"resource":  "INSTANCE:" + data.Service.Group + "/" + data.Service.Name,
			"revision":  data.ServiceInfo.Checksum,
		},
	}); err != nil {
		nacoslog.Error("[NACOS-V2][PushCenter] add inflight client error", zap.String("conn-id", watcher.Key),
			zap.String("req-id", req.RequestId),
			zap.String("namespace", data.Service.Namespace), zap.String("svc", data.Service.Name),
			zap.Error(err))
	}
	// 发送通知失败，直接触发 Inflight 结束
	if err = svr.SendMsg(clientResp); err != nil {
		h.connMgr.InFlights().NotifyInFlight(client.ID, &nacospb.NotifySubscriberResponse{
			Response: &nacospb.Response{
				ResultCode: int(nacosmodel.Response_Fail.Code),
				Message:    err.Error(),
				RequestId:  req.RequestId,
			},
		})
	}
	return err
}

// checkSubscriptionPermission 检查用户是否有权限订阅指定命名空间的服务
// 通过调用带有权限验证拦截器的ServiceInstancesCache方法来进行真正的权限验证
func (h *DiscoverServer) checkSubscriptionPermission(ctx context.Context, namespace, service, group string) error {
	// 转换命名空间格式
	namespace = nacosmodel.ToPolarisNamespace(namespace)
	svcName := nacosmodel.BuildServiceName(service, group)

	// 添加调试日志：检查context中的token信息
	token := ctx.Value(utils.ContextAuthTokenKey)
	nacoslog.Info("[SUBSCRIPTION-DEBUG] Checking subscription permission (real permission check)",
		zap.String("namespace", namespace),
		zap.String("service", svcName),
		zap.String("group", group),
		zap.Any("token", token),
		zap.Bool("has_token", token != nil))

	// 创建服务对象用于权限验证
	svc := &apiservice.Service{
		Name:      utils.NewStringValue(svcName),
		Namespace: utils.NewStringValue(namespace),
	}

	// 调用带有权限验证拦截器的ServiceInstancesCache方法
	// 这个方法会通过权限验证拦截器检查用户是否有权限访问该命名空间的服务
	resp := h.discoverSvr.ServiceInstancesCache(ctx, &apiservice.DiscoverFilter{}, svc)

	// 添加调试日志：权限验证结果
	nacoslog.Info("[SUBSCRIPTION-DEBUG] Real permission check result",
		zap.String("namespace", namespace),
		zap.String("service", svcName),
		zap.Uint32("response_code", resp.GetCode().GetValue()),
		zap.String("response_info", resp.GetInfo().GetValue()),
		zap.Bool("permission_granted", resp.GetCode().GetValue() == uint32(apimodel.Code_ExecuteSuccess)))

	// 检查响应结果
	if resp.GetCode().GetValue() != uint32(apimodel.Code_ExecuteSuccess) {
		// 权限验证失败，返回错误
		return &nacosmodel.NacosError{
			ErrCode: int32(resp.GetCode().GetValue()),
			ErrMsg:  "No permission to subscribe service in namespace: " + namespace + ", error: " + resp.GetInfo().GetValue(),
		}
	}

	nacoslog.Info("[SUBSCRIPTION-DEBUG] Real permission check passed",
		zap.String("namespace", namespace),
		zap.String("service", svcName))

	return nil // 权限验证成功
}
