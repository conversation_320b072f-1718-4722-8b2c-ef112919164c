协议兼容（推荐）
提示

本章节仅适用于北极星服务端版本 >= 1.18.0

概述
如果你希望使用 Polaris 代替 Nacos 作为新的注册中心，Polaris 提供了协议兼容的方式，用户仅需要更改程序中的 nacos-client 的服务端接入地址即可。

准备Polaris服务端
需要预先安装好Polaris服务端，安装方式可参考：集群版安装

北极星服务端参数配置
apiservers:
- name: service-nacos
  option:
  listenIP: "0.0.0.0"
  listenPort: 8848
  defaultNamespace: default
  connLimit:
  openConnLimit: false
  maxConnPerHost: 128
  maxConnLimit: 10240
  在北极星的服务端配置文件 polaris-server.yaml 中，找到 apiservers 的配置，开启 service-nacos 的插件即可

参数描述

listenPort: 设置 nacos 协议兼容的 http 端口地址，grpc 端口会在该端口的基础上 + 1000
defaultNamespace: 设置 nacos 默认命名空间对应 Polaris 命名空间信息；默认情况下 polaris 的 default 命名空间 == nacos 的默认命名空间（public ）；即在默认 nacos 默认命名空间下配置下，如果用户连接的是 nacos-server，服务注册到的是 nacos 的 public 命名空间，如果连接的是 polaris，那么根据配置 defaultNamespace，则服务注册到的是 polaris 的 default 命名空间，配置中心同理。
注册发现
Nacos 服务名和北极星服务名映射关系
Nacos 字段	Nacos 字段值	北极星字段	北极星字段值描述
namespace	默认命名空间/非默认命名空间ID	namespace	default/命名空间名称
group	DEFAULT_GROUP	service	作为服务名的前缀
service	DEFAULT_GROUP	service	作为服务名的后缀，${group}__${service} 为最终的北极星服务名, 如果 group == DEFAULT_GROUP，则服务名为 ${service}
cluster	DEFAULT	instance.metadata	作为实例标签的一部份, 实例标签 key 为 internal-nacos-cluster
原生 Nacos-Client
Properties properties = new Properties();
properties.put(PropertyKeyConst.SERVER_ADDR, "北极星服务端IP:8848");
properties.put(PropertyKeyConst.NAMESPACE, "北极星命名空间名称");
properties.put(PropertyKeyConst.USERNAME, "可任意值");
properties.put(PropertyKeyConst.PASSWORD, "北极星用户/用户组的资源访问凭据 Token");

// 创建注册发现客户端
NamingService namingService = NacosFactory.createNamingService(properties);
Spring Cloud Alibaba
spring.cloud.nacos.username="可任意值"
spring.cloud.nacos.password="北极星用户/用户组的资源访问凭据 Token"
spring.cloud.nacos.discovery.server-addr="北极星服务端IP:8848"
spring.cloud.nacos.discovery.namespace="北极星命名空间名称"
Dubbo
dubbo
registry
address: nacos://北极星服务端IP:8848?username=可任意值&password={北极星用户/用户组的资源访问凭据 Token}
parameters.namespace: 北极星命名空间名称
metadata-report
address: nacos://北极星服务端IP:8848
配置管理
Nacos 配置信息和北极星配置信息映射关系
Nacos 字段	Nacos 字段值	北极星字段	北极星字段值描述
namespace	默认命名空间/非默认命名空间ID	namespace	default/命名空间名称
group	DEFAULT_GROUP	group	北极星配置分组名称
dataId	application.yaml	file_name	北极星配置文件名称
原生 Nacos-Client
Properties properties = new Properties();
properties.put(PropertyKeyConst.SERVER_ADDR, "北极星服务端IP:8848");
properties.put(PropertyKeyConst.NAMESPACE, "北极星命名空间名称");
properties.put(PropertyKeyConst.USERNAME, "可任意值");
properties.put(PropertyKeyConst.PASSWORD, "北极星用户/用户组的资源访问凭据 Token");

// 注册配置客户端
ConfigService configService = new NacosConfigService(properties);
Spring Cloud Alibaba
spring.cloud.nacos.username="可任意值"
spring.cloud.nacos.password="北极星用户/用户组的资源访问凭据 Token"
spring.cloud.nacos.config.namespace="北极星命名空间名称"
spring.cloud.nacos.config.server-addr="北极星服务端IP:8848"
spring.cloud.nacos.config.group="北极星配置分组名称"
Dubbo
dubbo
config-center
address: nacos://北极星服务端IP:8848