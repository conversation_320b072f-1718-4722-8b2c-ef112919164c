/**
 * <PERSON><PERSON> is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package v1

import (
	"github.com/emicklei/go-restful/v3"

	nacoshttp "github.com/polarismesh/polaris/apiserver/nacosserver/v1/http"
	"github.com/polarismesh/polaris/common/log"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func (n *NacosV1Server) GetAuthServer() (*restful.WebService, error) {
	ws := new(restful.WebService)
	ws.Route(ws.POST("/v1/auth/login").To(n.Login))
	ws.Route(ws.POST("/v1/auth/users/login").To(n.Login))
	// 🔧 修复：添加Java SDK使用的登录路由
	ws.Route(ws.POST("/nacos/v1/auth/users/login").To(n.Login))
	return ws, nil
}

func (n *NacosV1Server) addSystemAccess(ws *restful.WebService) {
	ws.Route(ws.GET("/operator/metrics").To(n.ServerHealthStatus))
}

func (n *NacosV1Server) Login(req *restful.Request, rsp *restful.Response) {
	handler := nacoshttp.Handler{
		Request:  req,
		Response: rsp,
	}

	// 📝 添加调试日志：记录Login方法被调用
	log.Infof("[Nacos][Login] Login method called, URL: %s", req.Request.URL.String())
	log.Infof("[Nacos][Login] Request method: %s", req.Request.Method)
	log.Infof("[Nacos][Login] Content-Type: %s", req.Request.Header.Get("Content-Type"))
	log.Infof("[Nacos][Login] User-Agent: %s", req.Request.Header.Get("User-Agent"))

	ctx := handler.ParseHeaderContext()

	// 🔧 修复：正确解析POST body参数
	// ParseQueryParams只解析URL查询参数，不解析POST body
	// 我们需要手动解析POST body中的form参数
	params := make(map[string]string)

	// 📝 详细记录URL查询参数
	log.Infof("[Nacos][Login] URL查询参数:")
	urlParams := req.Request.URL.Query()
	if len(urlParams) == 0 {
		log.Infof("[Nacos][Login]   - 无URL查询参数")
	} else {
		for key, values := range urlParams {
			for _, value := range values {
				params[key] = value
				if key == "password" || key == "token" {
					log.Infof("[Nacos][Login]   - %s: %s...", key, value[:min(20, len(value))])
				} else {
					log.Infof("[Nacos][Login]   - %s: %s", key, value)
				}
			}
		}
	}

	// 📝 详细记录POST body参数
	log.Infof("[Nacos][Login] POST body参数:")
	if req.Request.Method == "POST" {
		if err := req.Request.ParseForm(); err != nil {
			log.Errorf("[Nacos][Login] Failed to parse form data: %v", err)
		} else {
			if len(req.Request.PostForm) == 0 {
				log.Infof("[Nacos][Login]   - 无POST body参数")
			} else {
				for key, values := range req.Request.PostForm {
					for _, value := range values {
						params[key] = value
						if key == "password" || key == "token" {
							log.Infof("[Nacos][Login]   - %s: %s...", key, value[:min(20, len(value))])
						} else {
							log.Infof("[Nacos][Login]   - %s: %s", key, value)
						}
					}
				}
			}
		}
	} else {
		log.Infof("[Nacos][Login]   - 非POST请求，跳过body解析")
	}

	// 📝 添加调试日志：记录最终解析的参数
	log.Infof("[Nacos][Login] 最终解析的参数总数: %d", len(params))
	for key, value := range params {
		if key == "password" || key == "token" {
			log.Infof("[Nacos][Login] 参数 %s: %s...", key, value[:min(20, len(value))])
		} else {
			log.Infof("[Nacos][Login] 参数 %s: %s", key, value)
		}
	}

	data, err := n.handleLogin(ctx, params)
	if err != nil {
		log.Errorf("[Nacos][Login] handleLogin failed: %v", err)
		nacoshttp.WrirteNacosErrorResponse(err, rsp)
		return
	}

	log.Infof("[Nacos][Login] Login successful, sending response")
	nacoshttp.WrirteNacosResponse(data, rsp)
}

func (n *NacosV1Server) ServerHealthStatus(req *restful.Request, rsp *restful.Response) {
	nacoshttp.WrirteNacosResponse(map[string]interface{}{
		"status": "UP",
	}, rsp)
}
