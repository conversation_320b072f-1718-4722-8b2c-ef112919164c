/**
 * <PERSON>cent is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package v1

import (
	"context"

	"github.com/polarismesh/polaris/common/log"
)

func (n *NacosV1Server) handleLogin(ctx context.Context, params map[string]string) (map[string]interface{}, error) {
	username := params["username"]
	password := params["password"]

	// 📝 详细调试日志：记录登录请求参数
	log.Infof("[Nacos][Login] === handleLogin 方法调用 ===")
	log.Infof("[Nacos][Login] 接收到的参数总数: %d", len(params))
	log.Infof("[Nacos][Login] 参数详情:")
	for key, value := range params {
		if key == "password" || key == "token" {
			log.Infof("[Nacos][Login]   - %s: %s... (长度: %d)", key, value[:min(20, len(value))], len(value))
		} else {
			log.Infof("[Nacos][Login]   - %s: %s", key, value)
		}
	}

	log.Infof("[Nacos][Login] username 参数: '%s'", username)
	log.Infof("[Nacos][Login] password 参数: '%s'", password)

	if password != "" {
		tokenPreview := password
		if len(password) > 20 {
			tokenPreview = password[:20] + "..."
		}
		log.Infof("[Nacos][Login] ✅ 成功接收到 password/token: %s", tokenPreview)
		log.Infof("[Nacos][Login] Token 完整长度: %d 字符", len(password))
	} else {
		log.Errorf("[Nacos][Login] ❌ 关键问题：没有接收到 password/token 参数！")
		log.Errorf("[Nacos][Login] 这可能是Java SDK没有正确传递认证信息的原因")
	}

	// 📝 添加min函数的实现
	min := func(a, b int) int {
		if a < b {
			return a
		}
		return b
	}
	_ = min // 避免未使用变量警告

	// 🎯 原始设计：这是一个"透传"式的登录接口
	// 根据Polaris官方文档，这个接口的作用是：
	// 1. 接收Java SDK传入的username和password（password实际上是Polaris访问token）
	// 2. 直接返回token给客户端作为accessToken，不进行额外验证
	// 3. 满足Nacos Java SDK必须先登录的要求

	response := map[string]interface{}{
		"accessToken": password, // 直接返回password字段中的token
		"tokenTtl":    120,
		"globalAdmin": false,
		"username":    username,
	}

	// 📝 详细调试日志：记录登录响应内容
	log.Infof("[Nacos][Login] Login response prepared for user: %s", username)
	if password != "" {
		tokenPreview := password
		if len(password) > 20 {
			tokenPreview = password[:20] + "..."
		}
		log.Infof("[Nacos][Login] Returning accessToken: %s", tokenPreview)
	}
	log.Infof("[Nacos][Login] Login processing completed successfully")

	return response, nil
}
