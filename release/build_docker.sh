#!/bin/bash

if [ $# != 1 ]; then
    echo "e.g.: bash $0 v1.0"
    exit 1
fi

docker_tag=$1

export docker_repository="registry.baidubce.com/csm-offline"

echo "docker repository : ${docker_repository}/registry-server, tag : ${docker_tag}"

make build VERSION=${docker_tag}

if [ $? != 0 ]; then
    echo "build polaris-server failed"
    exit 1
fi
platforms="linux/amd64"

docker buildx build -f ./release/Dockerfile -t ${docker_repository}/registry-server:${docker_tag} --platform ${platforms} --push ./