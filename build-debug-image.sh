#!/bin/bash

# Polaris 服务注销鉴权调试镜像构建脚本
# 该脚本用于构建包含详细日志输出的 Polaris 镜像，用于调试服务注销鉴权问题

set -e

# 配置变量
IMAGE_NAME="polaris-debug"
IMAGE_TAG="auth-deregister-$(date +%Y%m%d-%H%M%S)"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "========================================="
echo "构建 Polaris 鉴权调试镜像"
echo "镜像名称: ${FULL_IMAGE_NAME}"
echo "========================================="

# 检查是否在正确的目录
if [ ! -f "go.mod" ] || [ ! -d "service" ]; then
    echo "错误: 请在 Polaris 项目根目录下运行此脚本"
    exit 1
fi

# 显示修改的文件
echo "已修改的文件列表:"
echo "1. service/interceptor/auth/client_v1_authability.go - 添加客户端鉴权详细日志"
echo "2. auth/defaultauth/auth_checker.go - 添加权限检查详细日志"
echo "3. apiserver/nacosserver/v1/discover/instance.go - 添加服务注销处理日志"
echo "4. common/utils/common.go - 添加 Token 前缀解析工具函数"
echo ""

# 构建镜像
echo "开始构建镜像..."
docker build -t "${FULL_IMAGE_NAME}" .

if [ $? -eq 0 ]; then
    echo ""
    echo "========================================="
    echo "镜像构建成功!"
    echo "镜像名称: ${FULL_IMAGE_NAME}"
    echo "========================================="
    echo ""
    echo "部署命令:"
    echo "kubectl set image deployment/registry-server registry-server=${FULL_IMAGE_NAME} -n mse-zdu1rgey"
    echo ""
    echo "验证命令:"
    echo "kubectl get pods -n mse-zdu1rgey"
    echo "kubectl logs -f registry-server-0 -n mse-zdu1rgey"
    echo ""
    echo "测试命令:"
    echo "# 查询服务实例"
    echo 'curl -X GET "http://**************:8848/nacos/v1/ns/instance/list?accessToken=RDSY6EClAZQjkT%2FpW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV%2F%2BA%2FY1WLgGY3a0bhFzPSDHP7jmHGjB5DY%3D&serviceName=test-service-autocreate&namespaceId=auto-created-ns-final"'
    echo ""
    echo "# 删除服务实例（应该被权限检查拦截）"
    echo 'curl -X DELETE "http://**************:8848/nacos/v1/ns/instance?accessToken=RDSY6EClAZQjkT%2FpW9Ap8DenOmaF5EqVyFLm2kYKgS3ImrogNV%2F%2BA%2FY1WLgGY3a0bhFzPSDHP7jmHGjB5DY%3D&serviceName=test-service-autocreate&ip=*************&port=8084&namespaceId=auto-created-ns-final"'
    echo ""
    echo "预期结果:"
    echo "- 日志中应该显示详细的鉴权流程"
    echo "- 删除操作应该被权限检查拦截并返回权限不足错误"
    echo "- 如果仍然成功删除，说明存在其他问题需要进一步调查"
else
    echo ""
    echo "========================================="
    echo "镜像构建失败!"
    echo "请检查构建日志并修复错误"
    echo "========================================="
    exit 1
fi
