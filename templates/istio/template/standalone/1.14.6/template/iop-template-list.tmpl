apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
spec:
  profile: default
  tag: {{ .Tag }}
  hub: {{ .Hub }}
  namespace: {{ .Namespace }}
  meshConfig:
    accessLogFile: /dev/stdout
    accessLogEncoding: JSON
    accessLogFormat: ""
  components:
    ingressGateways:
  {{- range .IngressGateways }}
      - name: {{ .Name }}
        enabled: true
        label:
          internet-type: {{ .TypeLabel }}
        k8s:
          service:
            type: LoadBalancer
          hpaSpec:
            minReplicas: {{ .Replicas }}
          overlays:
            - kind: Service
              name: {{ .Name }}
              patches:
                - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-internal-vpc
                  value: "{{ .BindVpc }}"
                - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-allocate-vip
                  value: "true"
{{- end }}