apiVersion: v1
kind: ConfigMap
metadata:
  name: vmagent-cluster-config
  namespace: monitoring
  uid: f4dc6c58-d44c-4a49-af15-82bd44f40ed8
  resourceVersion: '35264740'
  creationTimestamp: '2025-06-05T03:23:27Z'
  labels:
    k8slens-edit-resource-version: v1
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: >
      {"apiVersion":"v1","data":{"prometheus.yaml":"global:\n  scrape_interval:
      30s\nscrape_configs:\n  - job_name: \"stats-prometheus-0623-1\"\n   
      metrics_path: /stats/prometheus\n    kubernetes_sd_configs:\n    - role:
      pod\n    metric_relabel_configs:\n      - source_labels:
      [destination_address]\n        regex: '([^:]+):\\d+'\n       
      target_label: 'destination_address'\n        replacement: '$1'\n       
      action: replace\n      - source_labels: [__name_] \n        regex:
      '(go_.+|process_.+|scrape_.+|__meta_.+)'\n        action: drop\n   
      relabel_configs:\n      # 通用标签：添加命名空间信息（后续用于 remote_write 过滤）\n      -
      source_labels: [__meta_kubernetes_namespace]\n        target_label:
      user_namespace\n      - source_labels:
      [__meta_kubernetes_pod_container_port_name]\n        action: keep\n       
      regex: '.*-envoy-prom'\n  - job_name: 'metrics-cadvisor-0623'\n   
      metrics_path: '/metrics/cadvisor'\n    kubernetes_sd_configs:\n    - role:
      node\n    scheme: https\n    bearer_token_file:
      /var/run/secrets/kubernetes.io/serviceaccount/token\n   
      tls_config:\n      ca_file:
      /var/run/secrets/kubernetes.io/serviceaccount/ca.crt\n     
      insecure_skip_verify: true\n    relabel_configs:\n      - source_labels:
      [__meta_kubernetes_namespace]\n        target_label: user_namespace\n     
      - separator: ;\n        regex: __meta_kubernetes_node_label_(.+)\n       
      replacement: $1\n        action: labelmap\n      - source_labels:
      [instance]\n        separator: ;\n        regex: (.*)\n       
      target_label: node\n        replacement: $1\n        action: replace\n   
      metric_relabel_configs:\n      - source_labels: [namespace]  # 假设原始指标有
      namespace 标签\n        target_label: user_namespace\n        action:
      replace\n      - separator: ;\n        regex: feature_node.*\n       
      replacement: $1\n        action: labeldrop\n      - source_labels:
      [__name__]\n        separator: ;\n        regex:
      (container_memory_working_set_bytes|container_fs_writes_total|container_memory_rss|\n         
      |container_sockets|container_network_receive_errors_total|container_fs_reads_total|\n         
      |container_cpu_usage_seconds_total|container_fs_reads_bytes_total|container_spec_memory_limit_bytes|\n         
      |container_network_receive_bytes_total|container_network_transmit_bytes_total|container_fs_writes_bytes_total|\n         
      |container_network_transmit_errors_total|container_cpu_system_seconds_total|container_cpu_user_seconds_total)\n       
      replacement: $1\n        action: keep\n  - job_name:
      higress-service-metrics-0623\n    kubernetes_sd_configs:\n    - role:
      endpoints\n    relabel_configs:\n      - source_labels:
      [__meta_kubernetes_namespace]\n        target_label: user_namespace\n     
      - source_labels: [__meta_kubernetes_service_name]\n        regex:
      \"higress-controller|istiod\"  # 根据实际 Service 名称调整\n        action:
      keep\n      - source_labels: [__address__]\n        target_label:
      __address__\n        regex: \"([^:]+):\\\\d+\"\n        replacement:
      \"${1}:15014\"\n        action: replace\nremote_write:\n  - url:
      \"https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write\"\n   
      bearer_token:
      \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoyNjg2NDY1MjAwLCJpc3MiOiJjcHJvbSJ9.emIrEPv4S6xZk8Eoi-0b3wEFS0rvrMi_P9npDiLNsDs\"\n   
      headers:\n      InstanceId: \"cprom-x35s7xaq7ezx7\"\n   
      tls_config:\n      insecure_skip_verify: true\n   
      write_relabel_configs:\n      - action: replace\n        target_label:
      \"region\"\n        replacement: \"bj\"\n      - source_labels:
      [user_namespace]\n        action: keep\n        regex:
      istio-system-aigw-o454uaq5\n  - url:
      \"https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write\"\n   
      bearer_token:
      \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoyNjg2NDY1MjAwLCJpc3MiOiJjcHJvbSJ9.emIrEPv4S6xZk8Eoi-0b3wEFS0rvrMi_P9npDiLNsDs\"\n   
      headers:\n      InstanceId: \"cprom-x35s7xaq7ezx7\"\n   
      tls_config:\n      insecure_skip_verify: true\n   
      write_relabel_configs:\n      - action: replace\n        target_label:
      \"region\"\n        replacement: \"bj\"\n      - source_labels:
      [user_namespace]\n        action: keep\n        regex:
      istio-system-aigw-w6hpxrbl"},"kind":"ConfigMap","metadata":{"annotations":{},"name":"vmagent-cluster-config","namespace":"monitoring"}}
  selfLink: /api/v1/namespaces/monitoring/configmaps/vmagent-cluster-config
spec: {}
data:
  prometheus.yaml: |-
    global:
      scrape_interval: 30s
    scrape_configs:
      - job_name: stats-prometheus
        metrics_path: /stats/prometheus
        kubernetes_sd_configs:
          - role: pod
        metric_relabel_configs:
          - source_labels: [destination_address]
            regex: '([^:]+):\d+'
            target_label: 'destination_address'
            replacement: '$1'
            action: replace
          - source_labels: [__name_]
            regex: '(go_.+|process_.+|scrape_.+|__meta_.+)'
            action: drop
        relabel_configs:
          # 通用标签：添加命名空间信息（后续用于 remote_write 过滤）
          - source_labels: [__meta_kubernetes_namespace]
            target_label: user_namespace
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            action: keep
            regex: '.*-envoy-prom'
      - job_name: metrics-cadvisor
        metrics_path: '/metrics/cadvisor'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace]
            target_label: user_namespace
          - separator: ;
            regex: __meta_kubernetes_node_label_(.+)
            replacement: $1
            action: labelmap
          - source_labels: [instance]
            separator: ;
            regex: (.*)
            target_label: node
            replacement: $1
            action: replace
        metric_relabel_configs:
          - source_labels: [namespace]  # 假设原始指标有 namespace 标签
            target_label: user_namespace
            action: replace
          - separator: ;
            regex: feature_node.*
            replacement: $1
            action: labeldrop
          - source_labels: [__name__]
            separator: ;
            regex: (container_memory_working_set_bytes|container_fs_writes_total|container_memory_rss|
              |container_sockets|container_network_receive_errors_total|container_fs_reads_total|
              |container_cpu_usage_seconds_total|container_fs_reads_bytes_total|container_spec_memory_limit_bytes|
              |container_network_receive_bytes_total|container_network_transmit_bytes_total|container_fs_writes_bytes_total|
              |container_network_transmit_errors_total|container_cpu_system_seconds_total|container_cpu_user_seconds_total)
            replacement: $1
            action: keep
      - job_name: higress-service-metrics
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace]
            target_label: user_namespace
          - source_labels: [__meta_kubernetes_service_name]
            regex: "higress-controller|istiod"  # 根据实际 Service 名称调整
            action: keep
          - source_labels: [__address__]
            target_label: __address__
            regex: "([^:]+):\\d+"
            replacement: "${1}:15014"
            action: replace
    remote_write:
      - url: "https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write"
        bearer_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoyNjgyMzM2NTI2LCJpc3MiOiJjcHJvbSJ9.yFfiDA5Da_JZo7-jxTy-XvqUrR9ixWPBahTSo0QKIGc"
        headers:
          InstanceId: "cprom-x35s7xaq7ezx7"
        tls_config:
          insecure_skip_verify: true
        metadata_config:
          send: false  # 关闭元数据推送
          send_interval: 0s  # 禁用周期性元数据同步
        write_relabel_configs:
          - action: replace
            target_label: "region"
            replacement: "bj"
          - source_labels: [user_namespace]
            action: keep
            regex: istio-system-aigw-o454uaq5
      - url: "https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write"
        bearer_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoyNjgyMzM2NTI2LCJpc3MiOiJjcHJvbSJ9.yFfiDA5Da_JZo7-jxTy-XvqUrR9ixWPBahTSo0QKIGc"
        headers:
          InstanceId: "cprom-x35s7xaq7ezx7"
        tls_config:
          insecure_skip_verify: true
        metadata_config:
          send: false  # 关闭元数据推送
          send_interval: 0s  # 禁用周期性元数据同步
        write_relabel_configs:
          - action: replace
            target_label: "region"
            replacement: "bj"
          - source_labels: [user_namespace]
            action: keep
            regex: istio-system-aigw-w6hpxrbl
      - url: "https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write"
        bearer_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoyNjgyMzM2NTI2LCJpc3MiOiJjcHJvbSJ9.yFfiDA5Da_JZo7-jxTy-XvqUrR9ixWPBahTSo0QKIGc"
        #bearer_token:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14MzVzN3hhcTdleng3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjo0OTA1ODIzMTA2LCJpc3MiOiJjcHJvbSJ9.Oqxatpx5K_35RoRSzw31Y4JkSKFED1Nr82kNST-2QmQ"
        headers:
          InstanceId: "cprom-x35s7xaq7ezx7"
        tls_config:
          insecure_skip_verify: true
        metadata_config:
          send: false  # 关闭元数据推送
          send_interval: 0s  # 禁用周期性元数据同步
        write_relabel_configs:
          - action: replace
            target_label: "region"
            replacement: "bj"
          - source_labels: [user_namespace]
            action: keep
            regex: istio-system-aigw-wp1bg3mg