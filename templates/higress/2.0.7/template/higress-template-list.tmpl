apiVersion: apps/v1
kind: Deployment
metadata:
  name: higress-controller
  namespace: istio-system-ai
  labels:
    helm.sh/chart: higress-core-2.0.7
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: higress-controller
      higress: higress-controller
  template:
    metadata:
      labels:
        app: higress-controller
        higress: higress-controller
    spec:
      serviceAccountName: higress-controller
      securityContext: {}
      containers:
        - name: higress-core
          securityContext: {}
          image: "registry.baidubce.com/csm-offline/higress/higress:2.0.7"
          args:
            - "serve"
            - --gatewaySelectorKey=higress
            - --gatewaySelectorValue=istio-system-ai-higress-gateway
            - --gatewayHttpPort=80
            - --gatewayHttpsPort=443
            - --ingressClass=higress
            - --enableAutomaticHttps=true
            - --automaticHttpsEmail=
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: DOMAIN_SUFFIX
              value: cluster.local
            - name: GATEWAY_NAME
              value: higress-gateway
            - name: PILOT_ENABLE_GATEWAY_API
              value: "true"
            - name: PILOT_ENABLE_ALPHA_GATEWAY_API
              value: "true"
          ports:
            - name: http
              containerPort: 8888
              protocol: TCP
            - name: http-solver
              containerPort: 8889
              protocol: TCP
            - name: grpc
              containerPort: 15051
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8888
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          volumeMounts:
            - name: log
              mountPath: /var/log
        - name: discovery
          image: "registry.baidubce.com/csm-offline/higress/pilot:2.0.7"
          args:
            - "discovery"
            - --monitoringAddr=:15014
            - --log_output_level=default:info
            - --domain
            - cluster.local
            - --keepaliveMaxServerConnectionAge
            - "30m"
          ports:
            - containerPort: 8080
              protocol: TCP
            - containerPort: 15010
              protocol: TCP
            - containerPort: 15017
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          env:
            - name: PILOT_ENABLE_QUIC_LISTENERS
              value: "true"
            - name: VALIDATION_WEBHOOK_CONFIG_NAME
              value: ""
            - name: ISTIO_DUAL_STACK
              value: "false"
            - name: PILOT_ENABLE_HEADLESS_SERVICE_POD_LISTENERS
              value: "false"
            - name: PILOT_ENABLE_ALPN_FILTER
              value: "false"
            - name: ENABLE_OPTIMIZED_CONFIG_REBUILD
              value: "false"
            - name: PILOT_ENABLE_K8S_SELECT_WORKLOAD_ENTRIES
              value: "false"
            - name: HIGRESS_SYSTEM_NS
              value: "istio-system-ai"
            - name: DEFAULT_UPSTREAM_CONCURRENCY_THRESHOLD
              value: "10000"
            - name: ISTIO_GPRC_MAXRECVMSGSIZE
              value: "104857600"
            - name: ENBALE_SCOPED_RDS
              value: "true"
            - name: ON_DEMAND_RDS
              value: "false"
            - name: HOST_RDS_MERGE_SUBSET
              value: "false"
            - name: PILOT_FILTER_GATEWAY_CLUSTER_CONFIG
              value: "true"
            - name: HIGRESS_CONTROLLER_SVC
              value: "127.0.0.1"
            - name: HIGRESS_CONTROLLER_PORT
              value: "15051"
            - name: REVISION
              value: "default"
            - name: JWT_POLICY
              value: third-party-jwt
            - name: PILOT_CERT_PROVIDER
              value: "istiod"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: KUBECONFIG
              value: /var/run/secrets/remote/config
            - name: PRIORITIZED_LEADER_ELECTION
              value: "false"
            - name: INJECT_ENABLED
              value: "false"
            - name: PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY
              value: "false"
            - name: PILOT_ENABLE_METADATA_EXCHANGE
              value: "false"
            - name: PILOT_SCOPE_GATEWAY_TO_NAMESPACE
              value: "false"
            - name: VALIDATION_ENABLED
              value: "false"
            - name: PILOT_TRACE_SAMPLING
              value: "1"
            - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_OUTBOUND
              value: "true"
            - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_INBOUND
              value: "true"
            - name: ISTIOD_ADDR
              value: istiod.istio-system-ai.svc:15012
            - name: PILOT_ENABLE_ANALYSIS
              value: "false"
            - name: CLUSTER_ID
              value: "Kubernetes"
            # HIGRESS_ENABLE_ISTIO_API is only used to restart the controller pod after the config change
            - name: HIGRESS_ENABLE_ISTIO_API
              value: "true"
            - name: PILOT_ENABLE_GATEWAY_API
              value: "false"
            - name: PILOT_ENABLE_ALPHA_GATEWAY_API
              value: "false"
            - name: PILOT_ENABLE_GATEWAY_API_STATUS
              value: "false"
            - name: PILOT_ENABLE_GATEWAY_API_DEPLOYMENT_CONTROLLER
              value: "false"
            - name: CUSTOM_CA_CERT_NAME
              value: "higress-ca-root-cert"
          securityContext:
            readOnlyRootFilesystem: true
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
            capabilities:
              drop:
                - ALL
          volumeMounts:
            - name: config
              mountPath: /etc/istio/config
            - name: istio-token
              mountPath: /var/run/secrets/tokens
              readOnly: true
            - name: local-certs
              mountPath: /var/run/secrets/istio-dns
            - name: cacerts
              mountPath: /etc/cacerts
              readOnly: true
            - name: istio-kubeconfig
              mountPath: /var/run/secrets/remote
              readOnly: true
      volumes:
        - name: log
          emptyDir: {}
        - name: config
          configMap:
            name: higress-config
        # Technically not needed on this pod - but it helps debugging/testing SDS
        # Should be removed after everything works.
        - emptyDir:
            medium: Memory
          name: local-certs
        - name: istio-token
          projected:
            sources:
              - serviceAccountToken:
                  audience: istio-ca
                  expirationSeconds: 43200
                  path: istio-token
        # Optional: user-generated root
        - name: cacerts
          secret:
            secretName: cacerts
            optional: true
        - name: istio-kubeconfig
          secret:
            secretName: istio-kubeconfig
            optional: true
---
apiVersion: v1
kind: Service
metadata:
  name: higress-controller
  namespace: istio-system-ai
  labels:
    helm.sh/chart: higress-core-2.0.7
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8888
      protocol: TCP
      targetPort: 8888
    - name: http-solver
      port: 8889
      protocol: TCP
      targetPort: 8889
    - name: grpc
      port: 15051
      protocol: TCP
      targetPort: 15051
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: higress-controller
    higress: higress-controller
---
{{- range .IngressGateways }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Name }}
  namespace: istio-system-ai
  labels:
    app: istio-ingressgateway
    internet-type: {{ .TypeLabel }}
    higress: istio-system-ai-higress-gateway
    istio: ingressgateway
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "{{ .BindVpc }}"
    service.beta.kubernetes.io/cce-load-balancer-allocate-vip: "true"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  type: LoadBalancer
  ports:
    - name: http2
      port: 80
      protocol: TCP
      targetPort: {{ .BindPort }}
    - name: https
      port: 443
      protocol: TCP
      targetPort: 443
  selector:
    app: istio-ingressgateway
    higress: istio-system-ai-higress-gateway
    istio: ingressgateway
    internet-type: {{ .TypeLabel }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Name }}
  namespace: istio-system-ai
  labels:
    helm.sh/chart: higress-core-2.0.7
    app: istio-ingressgateway
    higress: istio-system-ai-higress-gateway
    istio: ingressgateway
    internet-type: {{ .TypeLabel }}
    app.kubernetes.io/version: "2.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-gateway
  annotations: {}
spec:
  replicas: {{ .Replicas }}
  selector:
    matchLabels:
      app: istio-ingressgateway
      higress: istio-system-ai-higress-gateway
      istio: ingressgateway
      internet-type: {{ .TypeLabel }}
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 100%
  template:
    metadata:
      labels:
        sidecar.istio.io/inject: "false"
        app: istio-ingressgateway
        higress: istio-system-ai-higress-gateway
        istio: ingressgateway
        internet-type: {{ .TypeLabel }}
    spec:
      serviceAccountName: higress-gateway
      securityContext: {}
      containers:
        - name: {{ .Name }}
          image: "registry.baidubce.com/csm-offline/higress/gateway:2.0.7"
          args:
            - proxy
            - router
            - --domain
            - $(POD_NAMESPACE).svc.cluster.local
            - --proxyLogLevel=warning
            - --proxyComponentLogLevel=misc:error
            - --log_output_level=all:info
            - --serviceCluster={{ .Name }}
          securityContext:
            capabilities:
              drop:
                - ALL
              add:
                - NET_BIND_SERVICE
            runAsUser: 0
            runAsGroup: 1337
            runAsNonRoot: false
            allowPrivilegeEscalation: true
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: PROXY_XDS_VIA_AGENT
              value: "true"
            - name: ENABLE_INGRESS_GATEWAY_SDS
              value: "false"
            - name: JWT_POLICY
              value: third-party-jwt
            - name: ISTIO_META_HTTP10
              value: "1"
            - name: ISTIO_META_CLUSTER_ID
              value: "Kubernetes"
            - name: INSTANCE_NAME
              value: {{ .Name }}
            - name: LITE_METRICS
              value: "on"
          ports:
            - containerPort: 15020
              protocol: TCP
              name: istio-prom
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            - containerPort: 80
              hostPort: 80
              name: http
              protocol: TCP
            - containerPort: 443
              hostPort: 443
              name: https
              protocol: TCP
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 3
          volumeMounts:
            - mountPath: /var/run/secrets/workload-spiffe-uds
              name: workload-socket
            - mountPath: /var/run/secrets/credential-uds
              name: credential-socket
            - mountPath: /var/run/secrets/workload-spiffe-credentials
              name: workload-certs
            - name: istio-token
              mountPath: /var/run/secrets/tokens
              readOnly: true
            - name: config
              mountPath: /etc/istio/config
            - name: higress-ca-root-cert
              mountPath: /var/run/secrets/istio
            - name: istio-data
              mountPath: /var/lib/istio/data
            - name: podinfo
              mountPath: /etc/istio/pod
            - name: proxy-socket
              mountPath: /etc/istio/proxy
      volumes:
        - emptyDir: {}
          name: workload-socket
        - emptyDir: {}
          name: credential-socket
        - emptyDir: {}
          name: workload-certs
        - name: istio-token
          projected:
            sources:
              - serviceAccountToken:
                  audience: istio-ca
                  expirationSeconds: 43200
                  path: istio-token
        - name: higress-ca-root-cert
          configMap:
            name: higress-ca-root-cert
        - name: config
          configMap:
            name: higress-config
        - name: istio-data
          emptyDir: {}
        - name: proxy-socket
          emptyDir: {}
        - name: podinfo
          downwardAPI:
            defaultMode: 420
            items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.labels
                path: labels
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.annotations
                path: annotations
              - path: cpu-request
                resourceFieldRef:
                  containerName: {{ .Name }}
                  divisor: 1m
                  resource: requests.cpu
              - path: cpu-limit
                resourceFieldRef:
                  containerName: {{ .Name }}
                  divisor: 1m
                  resource: limits.cpu
---
{{- end }}