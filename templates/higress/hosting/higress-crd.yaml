# DO NOT EDIT - Generated by Cue OpenAPI generator based on Istio APIs.
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  name: wasmplugins.extensions.higress.io
spec:
  group: extensions.higress.io
  names:
    categories:
      - higress-io
      - extensions-higress-io
    kind: WasmPlugin
    listKind: WasmPluginList
    plural: wasmplugins
    singular: wasmplugin
  scope: Namespaced
  versions:
    - additionalPrinterColumns:
        - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
          jsonPath: .metadata.creationTimestamp
          name: Age
          type: date
      name: v1alpha1
      schema:
        openAPIV3Schema:
          properties:
            spec:
              properties:
                defaultConfig:
                  type: object
                  x-kubernetes-preserve-unknown-fields: true
                defaultConfigDisable:
                  type: boolean
                failStrategy:
                  description: Specifies the failure behavior for the plugin due to
                    fatal errors.
                  enum:
                    - FAIL_CLOSE
                    - FAIL_OPEN
                  type: string
                imagePullPolicy:
                  description: The pull behaviour to be applied when fetching an OCI
                    image.
                  enum:
                    - UNSPECIFIED_POLICY
                    - IfNotPresent
                    - Always
                  type: string
                imagePullSecret:
                  description: Credentials to use for OCI image pulling.
                  type: string
                matchRules:
                  items:
                    properties:
                      config:
                        type: object
                        x-kubernetes-preserve-unknown-fields: true
                      configDisable:
                        type: boolean
                      domain:
                        items:
                          type: string
                        type: array
                      ingress:
                        items:
                          type: string
                        type: array
                      service:
                        items:
                          type: string
                        type: array
                    type: object
                  type: array
                phase:
                  description: Determines where in the filter chain this `WasmPlugin`
                    is to be injected.
                  enum:
                    - UNSPECIFIED_PHASE
                    - AUTHN
                    - AUTHZ
                    - STATS
                  type: string
                pluginConfig:
                  description: The configuration that will be passed on to the plugin.
                  type: object
                  x-kubernetes-preserve-unknown-fields: true
                pluginName:
                  type: string
                priority:
                  description: Determines ordering of `WasmPlugins` in the same `phase`.
                  nullable: true
                  type: integer
                sha256:
                  description: SHA256 checksum that will be used to verify Wasm module
                    or OCI container.
                  type: string
                url:
                  description: URL of a Wasm module or OCI container.
                  type: string
                verificationKey:
                  type: string
                vmConfig:
                  description: Configuration for a Wasm VM.
                  properties:
                    env:
                      description: Specifies environment variables to be injected to
                        this VM.
                      items:
                        properties:
                          name:
                            type: string
                          value:
                            description: Value for the environment variable.
                            type: string
                          valueFrom:
                            enum:
                              - INLINE
                              - HOST
                            type: string
                        type: object
                      type: array
                  type: object
              type: object
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
          type: object
      served: true
      storage: true
      subresources:
        status: {}

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  name: http2rpcs.networking.higress.io
spec:
  group: networking.higress.io
  names:
    categories:
      - higress-io
    kind: Http2Rpc
    listKind: Http2RpcList
    plural: http2rpcs
    singular: http2rpc
  scope: Namespaced
  versions:
    - name: v1
      schema:
        openAPIV3Schema:
          properties:
            spec:
              oneOf:
                - not:
                    anyOf:
                      - required:
                          - dubbo
                      - required:
                          - grpc
                - required:
                    - dubbo
                - required:
                    - grpc
              properties:
                dubbo:
                  properties:
                    group:
                      type: string
                    methods:
                      items:
                        properties:
                          headersAttach:
                            type: string
                          httpMethods:
                            items:
                              type: string
                            type: array
                          httpPath:
                            type: string
                          paramFromEntireBody:
                            properties:
                              paramType:
                                type: string
                            type: object
                          params:
                            items:
                              properties:
                                paramKey:
                                  type: string
                                paramSource:
                                  type: string
                                paramType:
                                  type: string
                              type: object
                            type: array
                          serviceMethod:
                            type: string
                        type: object
                      type: array
                    service:
                      type: string
                    version:
                      type: string
                  type: object
                grpc:
                  type: object
              type: object
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
          type: object
      served: true
      storage: true
      subresources:
        status: {}

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  name: mcpbridges.networking.higress.io
spec:
  group: networking.higress.io
  names:
    categories:
      - higress-io
    kind: McpBridge
    listKind: McpBridgeList
    plural: mcpbridges
    singular: mcpbridge
  scope: Namespaced
  versions:
    - name: v1
      schema:
        openAPIV3Schema:
          properties:
            spec:
              properties:
                registries:
                  items:
                    properties:
                      authSecretName:
                        type: string
                      consulDatacenter:
                        type: string
                      consulNamespace:
                        type: string
                      consulRefreshInterval:
                        format: int64
                        type: integer
                      consulServiceTag:
                        type: string
                      domain:
                        type: string
                      nacosAccessKey:
                        type: string
                      nacosAddressServer:
                        type: string
                      nacosGroups:
                        items:
                          type: string
                        type: array
                      nacosNamespace:
                        type: string
                      nacosNamespaceId:
                        type: string
                      nacosRefreshInterval:
                        format: int64
                        type: integer
                      nacosSecretKey:
                        type: string
                      name:
                        type: string
                      port:
                        type: integer
                      protocol:
                        type: string
                      sni:
                        type: string
                      type:
                        type: string
                      zkServicesPath:
                        items:
                          type: string
                        type: array
                    type: object
                  type: array
              type: object
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
          type: object
      served: true
      storage: true
      subresources:
        status: {}

---
