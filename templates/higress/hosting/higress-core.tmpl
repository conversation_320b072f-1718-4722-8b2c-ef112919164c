apiVersion: apps/v1
kind: Deployment
metadata:
  name: higress-core
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: higress-controller
      higress: higress-controller
  template:
    metadata:
      annotations:
        cross-vpc-eni.cce.io/userID: "{{.AccountId}}"
        cross-vpc-eni.cce.io/subnetID: "{{.SubnetId}}"
        cross-vpc-eni.cce.io/securityGroupIDs: "{{.SecurityGroupIds}}"
        cross-vpc-eni.cce.io/vpcCidr: "{{.VpcCidr}}"
      labels:
        app: higress-controller
        higress: higress-controller
    spec:
      serviceAccountName: higress-controller
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: higress-core
          securityContext:
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
            allowPrivilegeEscalation: false
          image: "registry.baidubce.com/csm-offline/higress/higress:2.1.0"
          args:
            - "serve"
            - --gatewaySelectorKey=higress
            - --gatewaySelectorValue={{.Namespace}}-higress-gateway
            - --gatewayHttpPort=80
            - --gatewayHttpsPort=443
            - --ingressClass={{.IngressClass}}
            - --watchNamespace={{.WatchNamespace}}
            - --enableAutomaticHttps=true
            - --automaticHttpsEmail=
            - --kubeconfig=/etc/kubeconfig/{{.Namespace}}/kubeconfig
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: DOMAIN_SUFFIX
              value: cluster.local
            - name: GATEWAY_NAME
              value: higress-gateway
            - name: PILOT_ENABLE_GATEWAY_API
              value: "true"
            - name: PILOT_ENABLE_ALPHA_GATEWAY_API
              value: "true"
          ports:
            - name: http
              containerPort: 8888
              protocol: TCP
            - name: http-solver
              containerPort: 8889
              protocol: TCP
            - name: grpc
              containerPort: 15051
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8888
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          resources:
            limits:
              cpu: '500m'
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 1Gi
          volumeMounts:
            - name: kubeconfig-volume
              mountPath: /etc/kubeconfig/{{.Namespace}}  # 挂载路径
              readOnly: true
            - name: log
              mountPath: /var/log
      volumes:
        - name: kubeconfig-volume
          configMap:
            name: kubeconfig-configmap
        - name: log
          emptyDir: {}
        - name: config
          configMap:
            name: higress-config
        # Technically not needed on this pod - but it helps debugging/testing SDS
        # Should be removed after everything works.
        - emptyDir:
            medium: Memory
          name: local-certs
        - name: istio-token
          projected:
            sources:
              - serviceAccountToken:
                  audience: istio-ca
                  expirationSeconds: 43200
                  path: istio-token
        # Optional: user-generated root
        - name: cacerts
          secret:
            secretName: cacerts
            optional: true
        - name: istio-kubeconfig
          secret:
            secretName: istio-kubeconfig
            optional: true
---
apiVersion: v1
kind: Service
metadata:
  name: higress-core
  namespace: {{.Namespace}}
  labels:
    helm.sh/chart: higress-core-2.1.0
    app: higress-controller
    higress: higress-controller
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: higress-controller
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8888
      protocol: TCP
      targetPort: 8888
    - name: http-solver
      port: 8889
      protocol: TCP
      targetPort: 8889
    - name: grpc
      port: 15051
      protocol: TCP
      targetPort: 15051
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: higress-controller
    higress: higress-controller