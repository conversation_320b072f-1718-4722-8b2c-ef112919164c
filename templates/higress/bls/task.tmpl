apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:
  name: task-{{ .InstanceUUID }}
  namespace: {{ .Namespace }}
spec:
  srcConfig:
    logType: stdout
    srcDir: /var/log
    srcType: container
    matchedPattern: "^.*$"
    ignore_pattern: ""
    matchLabels:
    - key: io.kubernetes.pod.name
      value: higress-gateway-.*
    - key: io.kubernetes.pod.namespace
      value: "{{ .Namespace }}"
    logTime: system
    useMultiline: false
    ttl: 3
  dstConfig:
    dstType: BLS
    logStore: "{{ .LogStore }}"
    accountId: "{{ .AccountID }}"
    rateLimit: 10
    retention: 10
