apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
  labels:
    aigw.plugin.type: "ip-restriction"
  annotations:
    aigw.rule.name: "{{ .RuleName }}"
    aigw.rule.description: "{{ .RuleDescription }}"
    aigw.rule.type: "{{ .Type }}"
    aigw.rule.scope: "{{ .Scope }}"
spec:
  defaultConfig:
    ip_source_type: {{ .IPSourceType }}
    {{- if eq .Type "blacklist" }}
    deny:
    {{- range .IPAddresses }}
      - "{{ . }}"
    {{- end }}
    {{- else if eq .Type "whitelist" }}
    allow:
    {{- range .IPAddresses }}
      - "{{ . }}"
    {{- end }}
    {{- end }}
    status: {{ .Status }}
    message: "{{ .Message }}"
  defaultConfigDisable: {{ if .Enabled }}false{{ else }}true{{ end }}
  url: {{ .PluginURL }} 