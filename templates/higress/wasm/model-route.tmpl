apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: ai-model-route
  namespace: {{ .Namespace }}
spec:
  workloadSelector:
    labels:
      higress: {{ .Namespace }}-higress-gateway
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: envoy.filters.http.lua
          typed_config:
            "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
            inline_code: |
              function envoy_on_request(request_handle)
                  request_handle:logInfo("Request received, preparing to read body.")
                  local body_data = request_handle:body()

                  if body_data then
                      local body_string = body_data:getBytes(0, body_data:length())
                      request_handle:logInfo("Full request body: " .. body_string)

                      local model = extract_model_prefix(body_string)
                      request_handle:logInfo("Extracted model prefix: " .. tostring(model))

                      if model then
                          request_handle:headers():add("x-model-header", model)
                      end
                  end
              end

              function extract_model_prefix(body)
                  local full_model = body:match('"model"%s*:%s*"(.-)"')
                  if full_model then
                      local prefix = full_model:match("^([^:]+)")
                      return prefix
                  end
                  return nil
              end

              function envoy_on_response(response_handle)
              end

