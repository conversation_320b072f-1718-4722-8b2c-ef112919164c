apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
spec:
  defaultConfigDisable: true
  failStrategy: FAIL_OPEN
  imagePullPolicy: UNSPECIFIED_POLICY
  matchRules:
    - config:
        admin_consumer: admin # 固定的不能修改
        admin_path: /quota   # 固定的不能修改
        redis:
          service_name: {{ .RedisServiceName }}
          service_port: {{ .RedisServicePort }}
          password: {{ .RedisPassword }}
          timeout: 2000
        redis_key_prefix: '{{ .RedisKeyPrefix }}'
      configDisable: false
      ingress:
      {{- if .RouteNames }}
      {{- range .RouteNames }}
        - {{ . }}
      {{- end }}
      {{- else }}
        # 如果没有指定RouteNames,则为空规则
        []
      {{- end }}
  phase: UNSPECIFIED_PHASE
  priority: 750
  url: {{ .PluginURL }} 