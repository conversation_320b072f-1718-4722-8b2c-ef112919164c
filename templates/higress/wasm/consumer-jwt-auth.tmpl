apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  labels:
    metadata.name: key-auth
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
spec:
  defaultConfig:
    consumers:
    {{- if .ConsumerName }}
      - credential: {{ .Credential }}
        name: {{ .ConsumerName }}
        id: {{ .ConsumerID }}
        description: {{ .Description }}
        authType: {{ .AuthType }}
        createTime: {{ .CreateTime }}
        unlimitedQuota: {{ .UnlimitedQuota }}
        {{- if .TotalQuota }}
        totalQuota: {{ .TotalQuota }}
        {{- end }}
    {{- else }}
      []
    {{- end }}
    global_auth: false
    in_header: true
    keys:
      - Authorization
  defaultConfigDisable: false
  failStrategy: FAIL_OPEN
  matchRules:
  {{- if .RouteNames }}
  {{- range .RouteNames }}
    - config:
        allow:
        {{- if $.ConsumerName }}
          - {{ $.ConsumerName }}
        {{- else }}
          []
        {{- end }}
      configDisable: false
      ingress:
        - {{ . }}
  {{- end }}
  {{- else }}
    []
  {{- end }}
  phase: AUTHN
  priority: 310
  url: {{ .PluginURL }}