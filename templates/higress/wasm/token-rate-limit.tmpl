apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
spec:
  defaultConfigDisable: true
  failStrategy: FAIL_OPEN
  matchRules:
    - config:
        redis:
          service_name: {{ .RedisServiceName }}
          service_port: {{ .RedisServicePort }}
          password: {{ .RedisPassword }}
        rule_name: {{ .RuleName }}
        rule_items:
        {{- range .RuleItems }}
          - {{ if eq .MatchCondition.Type "consumer" -}}
            limit_by_consumer: ""
            {{- else if eq .MatchCondition.Type "header" -}}
            limit_by_header: {{ .MatchCondition.Key }}
            {{- else if eq .MatchCondition.Type "query_param" -}}
            limit_by_param: {{ .MatchCondition.Key }}
            {{- end }}
            limit_keys:
              - key: {{ .MatchCondition.Value }}
                {{- if eq .LimitConfig.TimeUnit "second" }}
                token_per_second: {{ .LimitConfig.TokenAmount }}
                {{- else if eq .LimitConfig.TimeUnit "minute" }}
                token_per_minute: {{ .LimitConfig.TokenAmount }}
                {{- else if eq .LimitConfig.TimeUnit "hour" }}
                token_per_hour: {{ .LimitConfig.TokenAmount }}
                {{- else if eq .LimitConfig.TimeUnit "day" }}
                token_per_day: {{ .LimitConfig.TokenAmount }}
                {{- else }}
                token_per_minute: {{ .LimitConfig.TokenAmount }}
                {{- end }}
        {{- end }}
      configDisable: false
      ingress:
        - {{ .RouteName }}
  phase: UNSPECIFIED_PHASE
  priority: 600 # 固定值
  url: {{ .PluginURL }}
