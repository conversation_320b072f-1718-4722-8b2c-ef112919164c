apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-test-4
  namespace: istio-system-aigw-ysruglc1
spec:
  exportTo:
    - .
  gateways:
    - istio-system-aigw-ysruglc1/gateway-internal
  hosts:
    - '*'
  http:
    # deepseek-r1 路由到 ollama-service-v2
    - match:
        - method:
            regex: ^(GET|POST|PUT|DELETE|OPTIONS|HEAD|PATCH)$
          uri:
            prefix: /api/test4/
          headers:
            x-model-header:
              prefix: deepseek-r1
      name: route-deepseek
      rewrite:
        uri: /
      route:
        - destination:
            host: ollama-service-v2.ollama.svc.cluster.local
            port:
              number: 80
    - match:
        - method:
            regex: ^(GET|POST|PUT|DELETE|OPTIONS|HEAD|PATCH)$
          uri:
            prefix: /api/test4/
          headers:
            x-model-header:
              prefix: qwen2.5
      name: route-qwen
      rewrite:
        uri: /
      route:
        - destination:
            host: ollama-service-v1.ollama.svc.cluster.local
            port:
              number: 80