apiVersion: extensions.higress.io/v1alpha1
kind: WasmPlugin
metadata:
  name: {{ .PluginName }}
  namespace: {{ .Namespace }}
  labels:
    aigw.plugin.type: "ext-auth"
    aigw.plugin.tag: "plugin-market"
  annotations:
    aigw.rule.name: "{{ .RuleName }}"
    aigw.rule.description: "{{ .RuleDescription }}"
    aigw.rule.scope: "{{ .Scope }}"
    {{- if eq .Scope "route" }}
    aigw.rule.match.type: "{{ .MatchType }}"
    aigw.rule.route.list: "{{ range $i, $route := .RouteList }}{{if $i}},{{end}}{{ $route }}{{ end }}"
    {{- end }}
spec:
  defaultConfig:
    {{- if eq .Scope "route" }}
    match_type: {{ .MatchType }}
    match_list:
    {{- range .MatchList }}
      - match_rule_path: {{ .Path }}
        match_rule_type: {{ .Type }}
    {{- end }}
    {{- end }}
{{ indent 4 .HTTPService }}
    status_on_error: {{ .StatusOnError }}
  defaultConfigDisable: {{ if .Enabled }}false{{ else }}true{{ end }}
  priority: 100
  url: {{ .PluginURL }}
