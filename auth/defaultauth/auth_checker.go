/**
 * <PERSON>cent is pleased to support the open source community by making Polaris available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package defaultauth

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	apisecurity "github.com/polarismesh/specification/source/go/api/v1/security"
	"go.uber.org/zap"

	"github.com/polarismesh/polaris/auth"
	cachetypes "github.com/polarismesh/polaris/cache/api"
	api "github.com/polarismesh/polaris/common/api/v1"
	"github.com/polarismesh/polaris/common/model"
	"github.com/polarismesh/polaris/common/utils"
	"github.com/polarismesh/polaris/store"
)

var (
	// ErrorNotAllowedAccess 鉴权失败
	ErrorNotAllowedAccess error = errors.New(api.Code2Info(api.NotAllowedAccess))
	// ErrorInvalidParameter 不合法的参数
	ErrorInvalidParameter error = errors.New(api.Code2Info(api.InvalidParameter))
	// ErrorNotPermission .
	ErrorNotPermission = errors.New("no permission")
)

// resourceOperationToString 将 ResourceOperation 转换为字符串
func resourceOperationToString(op model.ResourceOperation) string {
	switch op {
	case model.Read:
		return "Read"
	case model.Create:
		return "Create"
	case model.Modify:
		return "Modify"
	case model.Delete:
		return "Delete"
	default:
		return "Unknown"
	}
}

// DefaultAuthChecker 北极星自带的默认鉴权中心
type DefaultAuthChecker struct {
	cacheMgn cachetypes.CacheManager
}

func (d *DefaultAuthChecker) SetCacheMgr(mgr cachetypes.CacheManager) {
	d.cacheMgn = mgr
}

// Initialize 执行初始化动作
func (d *DefaultAuthChecker) Initialize(options *auth.Config, s store.Store, cacheMgr cachetypes.CacheManager) error {
	// 新版本鉴权策略配置均从auth.Option中迁移至auth.user.option及auth.strategy.option中
	var (
		strategyContentBytes []byte
		userContentBytes     []byte
		authContentBytes     []byte
		err                  error
	)

	cfg := DefaultAuthConfig()

	// 一旦设置了auth.user.option或auth.strategy.option，将不会继续读取auth.option
	if len(options.Strategy.Option) > 0 || len(options.User.Option) > 0 {
		// 判断auth.option是否还有值，有则不兼容
		if len(options.Option) > 0 {
			log.Warn("auth.user.option or auth.strategy.option has set, auth.option will ignore")
		}
		strategyContentBytes, err = json.Marshal(options.Strategy.Option)
		if err != nil {
			return err
		}
		if err := json.Unmarshal(strategyContentBytes, cfg); err != nil {
			return err
		}
		userContentBytes, err = json.Marshal(options.User.Option)
		if err != nil {
			return err
		}
		if err := json.Unmarshal(userContentBytes, cfg); err != nil {
			return err
		}
	} else {
		log.Warn("[Auth][Checker] auth.option has deprecated, use auth.user.option and auth.strategy.option instead.")
		authContentBytes, err = json.Marshal(options.Option)
		if err != nil {
			return err
		}
		if err := json.Unmarshal(authContentBytes, cfg); err != nil {
			return err
		}
	}

	if err := cfg.Verify(); err != nil {
		return err
	}
	// 兼容原本老的配置逻辑
	if cfg.Strict {
		cfg.ConsoleOpen = cfg.Strict
	}
	AuthOption = cfg
	d.cacheMgn = cacheMgr
	return nil
}

// Cache 获取缓存统一管理
func (d *DefaultAuthChecker) Cache() cachetypes.CacheManager {
	return d.cacheMgn
}

// IsOpenConsoleAuth 针对控制台是否开启了操作鉴权
func (d *DefaultAuthChecker) IsOpenConsoleAuth() bool {
	return AuthOption.ConsoleOpen
}

// IsOpenClientAuth 针对客户端是否开启了操作鉴权
func (d *DefaultAuthChecker) IsOpenClientAuth() bool {
	return AuthOption.ClientOpen
}

// IsOpenAuth 返回对于控制台/客户端任意其中的一个是否开启了操作鉴权
func (d *DefaultAuthChecker) IsOpenAuth() bool {
	return d.IsOpenConsoleAuth() || d.IsOpenClientAuth()
}

// CheckClientPermission 执行检查客户端动作判断是否有权限，并且对 RequestContext 注入操作者数据
func (d *DefaultAuthChecker) CheckClientPermission(preCtx *model.AcquireContext) (bool, error) {
	reqId := utils.ParseRequestID(preCtx.GetRequestContext())
	authToken := utils.ParseAuthToken(preCtx.GetRequestContext())

	log.Info("[Auth][CheckClientPermission] Start client permission check",
		utils.ZapRequestID(reqId),
		zap.String("operation", resourceOperationToString(preCtx.GetOperation())),
		zap.String("method", preCtx.GetMethod()),
		zap.String("token-prefix", utils.ParseTokenPrefix(authToken)),
		zap.Bool("from-client", preCtx.IsFromClient()))

	preCtx.SetFromClient()

	if !d.IsOpenClientAuth() {
		log.Info("[Auth][CheckClientPermission] Client auth is disabled, allowing access",
			utils.ZapRequestID(reqId))
		return true, nil
	}

	log.Info("[Auth][CheckClientPermission] Client auth is enabled, checking permission",
		utils.ZapRequestID(reqId))

	result, err := d.CheckPermission(preCtx)

	log.Info("[Auth][CheckClientPermission] Permission check result",
		utils.ZapRequestID(reqId),
		zap.Bool("passed", result),
		zap.Error(err))

	return result, err
}

// CheckConsolePermission 执行检查控制台动作判断是否有权限，并且对 RequestContext 注入操作者数据
func (d *DefaultAuthChecker) CheckConsolePermission(preCtx *model.AcquireContext) (bool, error) {
	preCtx.SetFromConsole()
	if !d.IsOpenConsoleAuth() {
		return true, nil
	}
	if preCtx.GetModule() == model.MaintainModule {
		return d.checkMaintainPermission(preCtx)
	}
	return d.CheckPermission(preCtx)
}

// CheckMaintainPermission 执行检查运维动作判断是否有权限
func (d *DefaultAuthChecker) checkMaintainPermission(preCtx *model.AcquireContext) (bool, error) {
	if err := d.VerifyCredential(preCtx); err != nil {
		return false, err
	}
	if preCtx.GetOperation() == model.Read {
		return true, nil
	}

	attachVal, ok := preCtx.GetAttachment(model.TokenDetailInfoKey)
	if !ok {
		return false, model.ErrorTokenNotExist
	}
	tokenInfo, ok := attachVal.(OperatorInfo)
	if !ok {
		return false, model.ErrorTokenNotExist
	}

	if tokenInfo.Disable {
		return false, model.ErrorTokenDisabled
	}
	if !tokenInfo.IsUserToken {
		return false, errors.New("only user role can access maintain API")
	}
	if tokenInfo.Role != model.OwnerUserRole {
		return false, errors.New("only owner account can access maintain API")
	}
	return true, nil
}

// CheckPermission 执行检查动作判断是否有权限
//
//	step 1. 判断是否开启了鉴权
//	step 2. 对token进行检查判断
//		case 1. 如果 token 被禁用
//				a. 读操作，直接放通
//				b. 写操作，快速失败
//	step 3. 拉取token对应的操作者相关信息，注入到请求上下文中
//	step 4. 进行权限检查
func (d *DefaultAuthChecker) CheckPermission(authCtx *model.AcquireContext) (bool, error) {
	reqId := utils.ParseRequestID(authCtx.GetRequestContext())
	authToken := utils.ParseAuthToken(authCtx.GetRequestContext())

	log.Info("[Auth][CheckPermission] Start permission check",
		utils.ZapRequestID(reqId),
		zap.String("operation", resourceOperationToString(authCtx.GetOperation())),
		zap.String("method", authCtx.GetMethod()),
		zap.String("token-prefix", utils.ParseTokenPrefix(authToken)))

	// Token 验证
	log.Info("[Auth][Debug] Starting credential verification",
		utils.ZapRequestID(reqId))
	if err := d.VerifyCredential(authCtx); err != nil {
		log.Error("[Auth][CheckPermission] Credential verification failed",
			utils.ZapRequestID(reqId),
			zap.Error(err))
		log.Error("[Auth][Debug] Credential verification failed with details",
			utils.ZapRequestID(reqId),
			zap.Error(err),
			zap.String("error-type", fmt.Sprintf("%T", err)))
		return false, err
	}

	log.Info("[Auth][CheckPermission] Credential verification passed",
		utils.ZapRequestID(reqId))
	log.Info("[Auth][Debug] Credential verification passed successfully",
		utils.ZapRequestID(reqId))

	// 读操作特殊处理检查 - 修复权限绕过漏洞
	operation := authCtx.GetOperation()
	method := authCtx.GetMethod()
	log.Info("[Auth][Debug] Checking operation type",
		utils.ZapRequestID(reqId),
		zap.String("operation", resourceOperationToString(operation)),
		zap.String("method", method),
		zap.Bool("is-read", operation == model.Read))

	// 修复权限绕过漏洞：区分Nacos客户端请求和内网控制台请求
	// 检查是否是Nacos客户端请求
	isNacosClient := d.isNacosClientRequest(authCtx)
	log.Info("[Auth][Debug] Request source analysis",
		utils.ZapRequestID(reqId),
		zap.String("method", method),
		zap.String("operation", resourceOperationToString(operation)),
		zap.Bool("is-nacos-client", isNacosClient))

	if !isNacosClient {
		// 对内网控制台请求保持原有逻辑（绕过权限检查）
		log.Info("[Auth][CheckPermission] Internal console request, allowing access",
			utils.ZapRequestID(reqId),
			zap.String("method", method),
			zap.String("operation", resourceOperationToString(operation)))
		log.Info("[Auth][Debug] Internal console request detected, bypassing permission check",
			utils.ZapRequestID(reqId))
		return true, nil
	}

	// 对所有Nacos客户端请求执行统一的命名空间权限验证
	// 实现权限继承模型：如果用户有命名空间权限，则自动获得该命名空间下所有资源的相应权限
	log.Info("[Auth][Debug] Nacos client request detected, performing namespace-only permission check",
		utils.ZapRequestID(reqId),
		zap.String("method", method),
		zap.String("operation", resourceOperationToString(operation)))

	// 对于GetNamespaces操作，需要特殊处理，因为它不针对特定命名空间
	if method == "GetNamespaces" {
		log.Info("[Auth][Debug] Nacos client namespace list query detected, performing full permission check",
			utils.ZapRequestID(reqId),
			zap.String("method", method))
		// GetNamespaces需要执行完整的权限验证，不能简化为命名空间检查
		// 继续执行权限验证，不绕过
	} else {
		// 所有其他Nacos操作（服务注册发现、配置中心）都只检查命名空间权限
		return d.checkNamespaceOnlyPermission(authCtx)
	}

	// 操作者信息提取
	log.Info("[Auth][Debug] Extracting operator information",
		utils.ZapRequestID(reqId))
	attachVal, ok := authCtx.GetAttachment(model.TokenDetailInfoKey)
	if !ok {
		log.Error("[Auth][Debug] Token detail info not found in context",
			utils.ZapRequestID(reqId))
		return false, model.ErrorTokenNotExist
	}
	log.Info("[Auth][Debug] Token detail info found in context",
		utils.ZapRequestID(reqId),
		zap.String("attachment-type", fmt.Sprintf("%T", attachVal)))

	operatorInfo, ok := attachVal.(OperatorInfo)
	if !ok {
		log.Error("[Auth][Debug] Failed to cast attachment to OperatorInfo",
			utils.ZapRequestID(reqId),
			zap.String("actual-type", fmt.Sprintf("%T", attachVal)))
		return false, model.ErrorTokenNotExist
	}

	log.Info("[Auth][Debug] Operator information extracted successfully",
		utils.ZapRequestID(reqId),
		zap.String("operator-id", operatorInfo.OperatorID),
		zap.String("role", string(operatorInfo.Role)),
		zap.Bool("disabled", operatorInfo.Disable))

	// 这里需要检查当 token 被禁止的情况，如果 token 被禁止，无论是否可以操作目标资源，都无法进行写操作
	if operatorInfo.Disable {
		log.Error("[Auth][Debug] Token is disabled",
			utils.ZapRequestID(reqId),
			zap.String("operator-id", operatorInfo.OperatorID))
		return false, model.ErrorTokenDisabled
	}

	// 特殊权限检查（命名空间创建）
	log.Info("[Auth][Debug] Checking special permissions",
		utils.ZapRequestID(reqId),
		zap.Bool("console-auth-enabled", d.IsOpenConsoleAuth()),
		zap.String("method", authCtx.GetMethod()),
		zap.String("role", string(operatorInfo.Role)))

	if d.IsOpenConsoleAuth() {
		if authCtx.GetMethod() == model.MethodNameCreateNamespace && operatorInfo.Role != model.OwnerUserRole && operatorInfo.Role != model.AdminUserRole {
			log.Info("[Auth][Debug] Namespace creation denied for non-admin user",
				utils.ZapRequestID(reqId),
				zap.String("method", authCtx.GetMethod()),
				zap.String("role", string(operatorInfo.Role)))
			return false, nil
		}
	}

	// 获取访问资源信息用于调试
	accessResources := authCtx.GetAccessResources()
	log.Info("[Auth][Debug] Access resources extracted",
		utils.ZapRequestID(reqId),
		zap.Int("total-resource-types", len(accessResources)))

	for resType, entries := range accessResources {
		log.Info("[Auth][Debug] Resource type details",
			utils.ZapRequestID(reqId),
			zap.String("resource-type", resType.String()),
			zap.Int("entry-count", len(entries)))
		for i, entry := range entries {
			log.Info("[Auth][Debug] Resource entry",
				utils.ZapRequestID(reqId),
				zap.String("resource-type", resType.String()),
				zap.Int("entry-index", i),
				zap.String("entry-id", entry.ID),
				zap.String("entry-owner", entry.Owner))
		}
	}

	log.Debug("[Auth][Checker] check permission args", utils.RequestID(authCtx.GetRequestContext()),
		zap.String("method", authCtx.GetMethod()), zap.Any("resources", authCtx.GetAccessResources()))

	// 执行核心权限检查
	log.Info("[Auth][Debug] Starting doCheckPermission",
		utils.ZapRequestID(reqId))
	ok, err := d.doCheckPermission(authCtx)
	log.Info("[Auth][Debug] doCheckPermission completed",
		utils.ZapRequestID(reqId),
		zap.Bool("passed", ok),
		zap.Error(err))

	if ok {
		log.Info("[Auth][Debug] Permission check passed, returning success",
			utils.ZapRequestID(reqId))
		return ok, nil
	}

	// 策略同步重试机制
	log.Info("[Auth][Debug] Permission check failed, attempting strategy sync",
		utils.ZapRequestID(reqId),
		zap.Error(err))

	// 强制同步一次db中strategy数据到cache
	if err = d.cacheMgn.AuthStrategy().ForceSync(); err != nil {
		log.Error("[Auth][Checker] force sync strategy to cache failed",
			utils.RequestID(authCtx.GetRequestContext()), zap.Error(err))
		log.Error("[Auth][Debug] Strategy sync failed",
			utils.ZapRequestID(reqId),
			zap.Error(err))
		return false, err
	}

	log.Info("[Auth][Debug] Strategy sync completed, retrying permission check",
		utils.ZapRequestID(reqId))

	// 重新执行权限检查
	retryOk, retryErr := d.doCheckPermission(authCtx)
	log.Info("[Auth][Debug] Retry permission check completed",
		utils.ZapRequestID(reqId),
		zap.Bool("passed", retryOk),
		zap.Error(retryErr))

	return retryOk, retryErr
}

// checkNamespaceOnlyPermission 只检查命名空间权限，用于服务列表查询
// 对于服务列表查询，我们只需要验证用户是否有权限访问指定的命名空间，而不需要检查具体的服务权限
func (d *DefaultAuthChecker) checkNamespaceOnlyPermission(authCtx *model.AcquireContext) (bool, error) {
	reqId := utils.ParseRequestID(authCtx.GetRequestContext())

	log.Info("[Auth][Debug] Starting namespace-only permission check",
		utils.ZapRequestID(reqId))

	// 操作者信息提取
	log.Info("[Auth][Debug] Extracting operator information",
		utils.ZapRequestID(reqId))

	operatorInfo, exist := authCtx.GetAttachment(model.TokenDetailInfoKey)
	if !exist || operatorInfo == nil {
		log.Error("[Auth][Debug] Token detail info not found in context",
			utils.ZapRequestID(reqId))
		return false, errors.New("token detail info not found")
	}

	operator, ok := operatorInfo.(OperatorInfo)
	if !ok {
		log.Error("[Auth][Debug] Token detail info type assertion failed",
			utils.ZapRequestID(reqId),
			zap.String("attachment-type", fmt.Sprintf("%T", operatorInfo)))
		return false, errors.New("token detail info type assertion failed")
	}

	log.Info("[Auth][Debug] Operator information extracted successfully",
		utils.ZapRequestID(reqId),
		zap.String("operator-id", operator.OperatorID),
		zap.String("role", fmt.Sprintf("%d", operator.Role)),
		zap.Bool("disabled", operator.Disable))

	// 检查特殊权限
	log.Info("[Auth][Debug] Checking special permissions",
		utils.ZapRequestID(reqId),
		zap.Bool("console-auth-enabled", d.IsOpenConsoleAuth()),
		zap.String("method", authCtx.GetMethod()),
		zap.String("role", fmt.Sprintf("%d", operator.Role)))

	// 如果是超级管理员，直接通过
	if operator.Role == model.AdminUserRole {
		log.Info("[Auth][Debug] Admin user detected, allowing access",
			utils.ZapRequestID(reqId))
		return true, nil
	}

	// 提取访问资源
	accessRes := authCtx.GetAccessResources()
	log.Info("[Auth][Debug] Access resources extracted",
		utils.ZapRequestID(reqId),
		zap.Int("total-resource-types", len(accessRes)))

	// 只检查命名空间权限
	namespaceEntries := accessRes[apisecurity.ResourceType_Namespaces]
	if len(namespaceEntries) == 0 {
		log.Info("[Auth][Debug] No namespace resources found, allowing access",
			utils.ZapRequestID(reqId))
		return true, nil
	}

	log.Info("[Auth][Debug] Resource type details",
		utils.ZapRequestID(reqId),
		zap.String("resource-type", "Namespaces"),
		zap.Int("entry-count", len(namespaceEntries)))

	for i, entry := range namespaceEntries {
		log.Info("[Auth][Debug] Resource entry",
			utils.ZapRequestID(reqId),
			zap.String("resource-type", "Namespaces"),
			zap.Int("entry-index", i),
			zap.String("entry-id", entry.ID),
			zap.String("entry-owner", entry.Owner))
	}

	// 根据操作类型执行相应的命名空间权限检查
	operation := authCtx.GetOperation()
	log.Info("[Auth][Debug] Starting namespace permission checks",
		utils.ZapRequestID(reqId),
		zap.String("operation", resourceOperationToString(operation)))

	principal := model.Principal{
		PrincipalID:   operator.OperatorID,
		PrincipalRole: model.PrincipalUser, // 对于用户token，使用PrincipalUser类型
	}

	var hasPermission bool
	switch operation {
	case model.Read:
		hasPermission = d.isResourceReadable(principal, apisecurity.ResourceType_Namespaces, namespaceEntries)
		log.Info("[Auth][Debug] Namespace read permission check result",
			utils.ZapRequestID(reqId),
			zap.Bool("readable", hasPermission))
	case model.Create, model.Modify, model.Delete:
		hasPermission = d.isResourceEditable(principal, apisecurity.ResourceType_Namespaces, namespaceEntries)
		log.Info("[Auth][Debug] Namespace write permission check result",
			utils.ZapRequestID(reqId),
			zap.String("operation", resourceOperationToString(operation)),
			zap.Bool("editable", hasPermission))
	default:
		log.Warn("[Auth][Debug] Unknown operation type, defaulting to read permission check",
			utils.ZapRequestID(reqId),
			zap.String("operation", resourceOperationToString(operation)))
		hasPermission = d.isResourceReadable(principal, apisecurity.ResourceType_Namespaces, namespaceEntries)
	}

	if !hasPermission {
		log.Info("[Auth][Debug] Namespace permission denied",
			utils.ZapRequestID(reqId),
			zap.String("operation", resourceOperationToString(operation)))
		return false, errors.New("no permission")
	}

	log.Info("[Auth][Debug] Namespace-only permission check passed",
		utils.ZapRequestID(reqId),
		zap.String("operation", resourceOperationToString(operation)))
	return true, nil
}

// isNacosClientRequest 判断是否是Nacos客户端请求
// 只有明确标识为Nacos客户端的请求才返回true，其他所有请求都认为是内网控制台请求
func (d *DefaultAuthChecker) isNacosClientRequest(authCtx *model.AcquireContext) bool {
	reqId := utils.ParseRequestID(authCtx.GetRequestContext())
	ctx := authCtx.GetRequestContext()
	method := authCtx.GetMethod()

	if ctx != nil {
		// 检查是否有Nacos客户端标识
		if nacosClientFlag := ctx.Value(utils.StringContext("nacos-client-request")); nacosClientFlag != nil {
			if flagStr, ok := nacosClientFlag.(string); ok && flagStr == "true" {
				log.Info("[Auth][Debug] Nacos client flag detected, identified as Nacos client request",
					utils.ZapRequestID(reqId),
					zap.String("method", method))
				return true
			}
		}
	}

	// 默认情况：没有Nacos客户端标识的请求都认为是内网控制台请求
	// 内网控制台请求不需要设置任何特殊标识，这是默认行为
	log.Info("[Auth][Debug] No Nacos client flag found, identified as internal console request",
		utils.ZapRequestID(reqId),
		zap.String("method", method))
	return false
}

func canDowngradeAnonymous(authCtx *model.AcquireContext, err error) bool {
	if authCtx.GetModule() == model.AuthModule {
		return false
	}
	if authCtx.IsFromClient() && AuthOption.ClientStrict {
		return false
	}
	if authCtx.IsFromConsole() && AuthOption.ConsoleStrict {
		return false
	}
	if errors.Is(err, model.ErrorTokenInvalid) {
		return true
	}
	if errors.Is(err, model.ErrorTokenNotExist) {
		return true
	}
	return false
}

// VerifyCredential 对 token 进行检查验证，并将 verify 过程中解析出的数据注入到 model.AcquireContext 中
// step 1. 首先对 token 进行解析，获取相关的数据信息，注入到整个的 AcquireContext 中
// step 2. 最后对 token 进行一些验证步骤的执行
// step 3. 兜底措施：如果开启了鉴权的非严格模式，则根据错误的类型，判断是否转为匿名用户进行访问
//   - 如果是访问权限控制相关模块（用户、用户组、权限策略），不得转为匿名用户
func (d *DefaultAuthChecker) VerifyCredential(authCtx *model.AcquireContext) error {
	reqId := utils.ParseRequestID(authCtx.GetRequestContext())

	checkErr := func() error {
		authToken := utils.ParseAuthToken(authCtx.GetRequestContext())
		operator, err := d.decodeToken(authToken)
		if err != nil {
			log.Error("[Auth][Checker] decode token", zap.Error(err))
			return model.ErrorTokenInvalid
		}

		ownerId, isOwner, err := d.checkToken(&operator)
		if err != nil {
			log.Errorf("[Auth][Checker] check token err : %s", errors.WithStack(err).Error())
			return err
		}

		operator.OwnerID = ownerId
		ctx := authCtx.GetRequestContext()
		ctx = context.WithValue(ctx, utils.ContextIsOwnerKey, isOwner)
		ctx = context.WithValue(ctx, utils.ContextUserIDKey, operator.OperatorID)
		ctx = context.WithValue(ctx, utils.ContextOwnerIDKey, ownerId)
		authCtx.SetRequestContext(ctx)
		d.parseOperatorInfo(operator, authCtx)
		if operator.Disable {
			log.Warn("[Auth][Checker] token already disabled", utils.ZapRequestID(reqId),
				zap.Any("token", operator.String()))
		}
		return nil
	}()

	if checkErr != nil {
		if !canDowngradeAnonymous(authCtx, checkErr) {
			return checkErr
		}
		log.Warn("[Auth][Checker] parse operator info, downgrade to anonymous", utils.ZapRequestID(reqId),
			zap.Error(checkErr))
		// 操作者信息解析失败，降级为匿名用户
		authCtx.SetAttachment(model.TokenDetailInfoKey, newAnonymous())
	}

	return nil
}

func (d *DefaultAuthChecker) parseOperatorInfo(operator OperatorInfo, authCtx *model.AcquireContext) {
	ctx := authCtx.GetRequestContext()
	if operator.IsUserToken {
		user := d.Cache().User().GetUserByID(operator.OperatorID)
		if user != nil {
			operator.Role = user.Type
			ctx = context.WithValue(ctx, utils.ContextOperator, user.Name)
			ctx = context.WithValue(ctx, utils.ContextUserNameKey, user.Name)
			ctx = context.WithValue(ctx, utils.ContextUserRoleIDKey, user.Type)
		}
	} else {
		userGroup := d.Cache().User().GetGroup(operator.OperatorID)
		if userGroup != nil {
			ctx = context.WithValue(ctx, utils.ContextOperator, userGroup.Name)
			ctx = context.WithValue(ctx, utils.ContextUserNameKey, userGroup.Name)
		}
	}

	authCtx.SetAttachment(model.OperatorRoleKey, operator.Role)
	authCtx.SetAttachment(model.OperatorPrincipalType, func() model.PrincipalType {
		if operator.IsUserToken {
			return model.PrincipalUser
		}
		return model.PrincipalGroup
	}())
	authCtx.SetAttachment(model.OperatorIDKey, operator.OperatorID)
	authCtx.SetAttachment(model.OperatorOwnerKey, operator)
	authCtx.SetAttachment(model.TokenDetailInfoKey, operator)

	authCtx.SetRequestContext(ctx)
}

// DecodeToken
func (d *DefaultAuthChecker) DecodeToken(t string) (OperatorInfo, error) {
	return d.decodeToken(t)
}

// decodeToken 解析 token 信息，如果 t == ""，直接返回一个空对象
func (d *DefaultAuthChecker) decodeToken(t string) (OperatorInfo, error) {
	if t == "" {
		return OperatorInfo{}, model.ErrorTokenInvalid
	}

	ret, err := decryptMessage([]byte(AuthOption.Salt), t)
	if err != nil {
		return OperatorInfo{}, err
	}
	tokenDetails := strings.Split(ret, TokenSplit)
	if len(tokenDetails) != 2 {
		return OperatorInfo{}, model.ErrorTokenInvalid
	}

	detail := strings.Split(tokenDetails[1], "/")
	if len(detail) != 2 {
		return OperatorInfo{}, model.ErrorTokenInvalid
	}

	tokenInfo := OperatorInfo{
		Origin:      t,
		IsUserToken: detail[0] == model.TokenForUser,
		OperatorID:  detail[1],
		Role:        model.UnknownUserRole,
	}
	return tokenInfo, nil
}

// checkToken 对 token 进行检查，如果 token 是一个空，直接返回默认值，但是不返回错误
// return {owner-id} {is-owner} {error}
func (d *DefaultAuthChecker) checkToken(tokenInfo *OperatorInfo) (string, bool, error) {
	if IsEmptyOperator(*tokenInfo) {
		return "", false, nil
	}

	id := tokenInfo.OperatorID
	if tokenInfo.IsUserToken {
		user := d.Cache().User().GetUserByID(id)
		if user == nil {
			return "", false, model.ErrorNoUser
		}

		if tokenInfo.Origin != user.Token {
			return "", false, model.ErrorTokenNotExist
		}

		tokenInfo.Disable = !user.TokenEnable
		if user.Type == model.OwnerUserRole {
			return user.ID, true, nil
		}

		return user.Owner, false, nil
	}
	group := d.Cache().User().GetGroup(id)
	if group == nil {
		return "", false, model.ErrorNoUserGroup
	}

	if tokenInfo.Origin != group.Token {
		return "", false, model.ErrorTokenNotExist
	}

	tokenInfo.Disable = !group.TokenEnable
	return group.Owner, false, nil
}

func (d *DefaultAuthChecker) isResourceEditable(
	principal model.Principal,
	resourceType apisecurity.ResourceType,
	resEntries []model.ResourceEntry) bool {

	log.Info("[Auth][Debug] Starting isResourceEditable check",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("principal-role", string(principal.PrincipalRole)),
		zap.String("resource-type", resourceType.String()),
		zap.Int("entry-count", len(resEntries)))

	// 🔧 修复：资源条目为空时的安全处理
	if len(resEntries) == 0 {
		// 检查用户是否是管理员或所有者（只适用于用户类型的主体）
		if principal.PrincipalRole == model.PrincipalUser {
			if user := d.cacheMgn.User().GetUserByID(principal.PrincipalID); user != nil {
				if user.Type == model.AdminUserRole || user.Type == model.OwnerUserRole {
					log.Info("[Auth][Debug] Resource entries list is empty, allowing for admin/owner",
						zap.String("principal-id", principal.PrincipalID),
						zap.String("resource-type", resourceType.String()),
						zap.String("user-role", string(user.Type)),
						zap.Bool("result", true))
					return true
				}
			}
		}

		log.Info("[Auth][Debug] Resource entries list is empty, denying for non-admin user (SECURE DEFAULT)",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.String("principal-role", string(principal.PrincipalRole)),
			zap.Bool("result", false))
		return false // 🔧 默认拒绝
	}

	// 检查每个资源条目的权限
	for i, entry := range resEntries {
		log.Info("[Auth][Debug] Checking individual resource entry",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.Int("entry-index", i),
			zap.String("entry-id", entry.ID),
			zap.String("entry-owner", entry.Owner))

		editable := d.cacheMgn.AuthStrategy().IsResourceEditable(principal, resourceType, entry.ID)
		log.Info("[Auth][Debug] Resource entry editability result",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.Int("entry-index", i),
			zap.String("entry-id", entry.ID),
			zap.Bool("editable", editable))

		if !editable {
			log.Info("[Auth][Debug] Resource entry not editable, returning false",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resourceType.String()),
				zap.String("entry-id", entry.ID),
				zap.Bool("result", false))
			return false
		}
	}

	log.Info("[Auth][Debug] All resource entries are editable, returning true",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("resource-type", resourceType.String()),
		zap.Int("checked-entries", len(resEntries)),
		zap.Bool("result", true))

	return true
}

// isResourceReadable 检查资源是否可读（专门用于读操作）
// 与isResourceEditable分离，避免影响现有写操作的权限检查
func (d *DefaultAuthChecker) isResourceReadable(
	principal model.Principal,
	resourceType apisecurity.ResourceType,
	resEntries []model.ResourceEntry) bool {

	log.Info("[Auth][Debug] Starting isResourceReadable check",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("principal-role", string(principal.PrincipalRole)),
		zap.String("resource-type", resourceType.String()),
		zap.Int("entry-count", len(resEntries)))

	// 资源条目为空时的安全处理
	if len(resEntries) == 0 {
		// 检查用户是否是管理员或所有者（只适用于用户类型的主体）
		if principal.PrincipalRole == model.PrincipalUser {
			if user := d.cacheMgn.User().GetUserByID(principal.PrincipalID); user != nil {
				if user.Type == model.AdminUserRole || user.Type == model.OwnerUserRole {
					log.Info("[Auth][Debug] Resource entries list is empty, allowing for admin/owner (read check)",
						zap.String("principal-id", principal.PrincipalID),
						zap.String("resource-type", resourceType.String()),
						zap.String("user-role", string(user.Type)),
						zap.Bool("result", true))
					return true
				}
			}
		}

		log.Info("[Auth][Debug] Resource entries list is empty, denying for non-admin user (read check - SECURE DEFAULT)",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.String("principal-role", string(principal.PrincipalRole)),
			zap.Bool("result", false))
		return false // 默认拒绝
	}

	// 检查每个资源条目的读权限
	for i, entry := range resEntries {
		log.Info("[Auth][Debug] Checking individual resource entry for read permission",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.Int("entry-index", i),
			zap.String("entry-id", entry.ID),
			zap.String("entry-owner", entry.Owner))

		readable := d.cacheMgn.AuthStrategy().IsResourceReadable(principal, resourceType, entry.ID)
		log.Info("[Auth][Debug] Resource entry readability result",
			zap.String("principal-id", principal.PrincipalID),
			zap.String("resource-type", resourceType.String()),
			zap.Int("entry-index", i),
			zap.String("entry-id", entry.ID),
			zap.Bool("readable", readable))

		if !readable {
			log.Info("[Auth][Debug] Resource entry not readable, returning false",
				zap.String("principal-id", principal.PrincipalID),
				zap.String("resource-type", resourceType.String()),
				zap.String("entry-id", entry.ID),
				zap.Bool("result", false))
			return false
		}
	}

	log.Info("[Auth][Debug] All resource entries are readable, returning true",
		zap.String("principal-id", principal.PrincipalID),
		zap.String("resource-type", resourceType.String()),
		zap.Int("checked-entries", len(resEntries)),
		zap.Bool("result", true))

	return true
}

// doCheckPermission 执行权限检查
func (d *DefaultAuthChecker) doCheckPermission(authCtx *model.AcquireContext) (bool, error) {
	reqId := utils.ParseRequestID(authCtx.GetRequestContext())

	log.Info("[Auth][Debug] Starting doCheckPermission",
		utils.ZapRequestID(reqId))

	var checkNamespace, checkSvc, checkCfgGroup bool

	// 获取访问资源
	reqRes := authCtx.GetAccessResources()
	nsResEntries := reqRes[apisecurity.ResourceType_Namespaces]
	svcResEntries := reqRes[apisecurity.ResourceType_Services]
	cfgResEntries := reqRes[apisecurity.ResourceType_ConfigGroups]

	log.Info("[Auth][Debug] Resource entries extracted from context",
		utils.ZapRequestID(reqId),
		zap.Int("namespace-entries", len(nsResEntries)),
		zap.Int("service-entries", len(svcResEntries)),
		zap.Int("config-group-entries", len(cfgResEntries)))

	// 详细记录每种资源类型的条目
	for i, entry := range nsResEntries {
		log.Info("[Auth][Debug] Namespace resource entry",
			utils.ZapRequestID(reqId),
			zap.Int("index", i),
			zap.String("id", entry.ID),
			zap.String("owner", entry.Owner))
	}

	for i, entry := range svcResEntries {
		log.Info("[Auth][Debug] Service resource entry",
			utils.ZapRequestID(reqId),
			zap.Int("index", i),
			zap.String("id", entry.ID),
			zap.String("owner", entry.Owner))
	}

	for i, entry := range cfgResEntries {
		log.Info("[Auth][Debug] Config group resource entry",
			utils.ZapRequestID(reqId),
			zap.Int("index", i),
			zap.String("id", entry.ID),
			zap.String("owner", entry.Owner))
	}

	// 获取主体信息
	principleID, _ := authCtx.GetAttachments()[model.OperatorIDKey].(string)
	principleType, _ := authCtx.GetAttachments()[model.OperatorPrincipalType].(model.PrincipalType)
	p := model.Principal{
		PrincipalID:   principleID,
		PrincipalRole: principleType,
	}

	log.Info("[Auth][Debug] Principal information",
		utils.ZapRequestID(reqId),
		zap.String("principal-id", p.PrincipalID),
		zap.String("principal-role", string(p.PrincipalRole)))

	// 根据操作类型选择权限检查方式
	operation := authCtx.GetOperation()
	isReadOperation := operation == model.Read

	if isReadOperation {
		log.Info("[Auth][Debug] Starting resource readability checks (READ OPERATION)",
			utils.ZapRequestID(reqId))

		checkNamespace = d.isResourceReadable(p, apisecurity.ResourceType_Namespaces, nsResEntries)
		log.Info("[Auth][Debug] Namespace readability check result",
			utils.ZapRequestID(reqId),
			zap.Bool("readable", checkNamespace))

		checkSvc = d.isResourceReadable(p, apisecurity.ResourceType_Services, svcResEntries)
		log.Info("[Auth][Debug] Service readability check result",
			utils.ZapRequestID(reqId),
			zap.Bool("readable", checkSvc))
	} else {
		log.Info("[Auth][Debug] Starting resource editability checks (WRITE OPERATION)",
			utils.ZapRequestID(reqId))

		checkNamespace = d.isResourceEditable(p, apisecurity.ResourceType_Namespaces, nsResEntries)
		log.Info("[Auth][Debug] Namespace editability check result",
			utils.ZapRequestID(reqId),
			zap.Bool("editable", checkNamespace))

		checkSvc = d.isResourceEditable(p, apisecurity.ResourceType_Services, svcResEntries)
		log.Info("[Auth][Debug] Service editability check result",
			utils.ZapRequestID(reqId),
			zap.Bool("editable", checkSvc))
	}

	// 🔧 修复：配置组权限检查优化
	// 只有当配置组资源条目不为空时，才需要检查配置组权限
	// 对于不涉及配置组的操作（如服务实例删除），跳过配置组权限检查
	if len(cfgResEntries) > 0 {
		if isReadOperation {
			checkCfgGroup = d.isResourceReadable(p, apisecurity.ResourceType_ConfigGroups, cfgResEntries)
			log.Info("[Auth][Debug] Config group readability check result",
				utils.ZapRequestID(reqId),
				zap.Bool("readable", checkCfgGroup),
				zap.Bool("required", true))
		} else {
			checkCfgGroup = d.isResourceEditable(p, apisecurity.ResourceType_ConfigGroups, cfgResEntries)
			log.Info("[Auth][Debug] Config group editability check result",
				utils.ZapRequestID(reqId),
				zap.Bool("editable", checkCfgGroup),
				zap.Bool("required", true))
		}

		// 🎯 权限继承模型：如果用户没有具体的配置分组权限，但有命名空间权限，则允许访问
		if !checkCfgGroup && checkNamespace {
			log.Info("[Auth][Debug] Config group permission failed, checking namespace permission inheritance",
				utils.ZapRequestID(reqId),
				zap.Bool("namespace-permission", checkNamespace))

			// 基于命名空间权限继承：有命名空间权限就有该命名空间下配置分组的权限
			checkCfgGroup = checkNamespace
			log.Info("[Auth][Debug] Config group permission inherited from namespace",
				utils.ZapRequestID(reqId),
				zap.Bool("inherited-permission", checkCfgGroup),
				zap.String("reason", "namespace-permission-inheritance"))
		}
	} else {
		checkCfgGroup = true // 不涉及配置组的操作，跳过检查
		log.Info("[Auth][Debug] Config group permission check result",
			utils.ZapRequestID(reqId),
			zap.Bool("permission-granted", checkCfgGroup),
			zap.Bool("required", false),
			zap.String("reason", "no config group resources involved"))
	}

	checkAllResEntries := checkNamespace && checkSvc && checkCfgGroup

	log.Info("[Auth][Debug] Final permission check result",
		utils.ZapRequestID(reqId),
		zap.Bool("namespace-check", checkNamespace),
		zap.Bool("service-check", checkSvc),
		zap.Bool("config-group-check", checkCfgGroup),
		zap.Bool("all-passed", checkAllResEntries))

	var err error
	if !checkAllResEntries {
		err = ErrorNotPermission
		log.Info("[Auth][Debug] Permission denied",
			utils.ZapRequestID(reqId),
			zap.Error(err))
	} else {
		log.Info("[Auth][Debug] Permission granted",
			utils.ZapRequestID(reqId))
	}

	return checkAllResEntries, err
}

// checkAction 检查操作是否和策略匹配
func (d *DefaultAuthChecker) checkAction(expect string, actual model.ResourceOperation, method string) bool {
	// TODO 后续可针对读写操作进行鉴权, 并且可以针对具体的方法调用进行鉴权控制
	return true
}
